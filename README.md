<h1 align="center" style="margin-bottom: -10px;">reShape Vue</h1>
<p align="center" style="margin-bottom: -10px">
    <a href="#" target="_blank">
        <img src="https://github.com/henrybkr/reShape-vue/blob/master/src/assets/logo.png" width="40">
    </a>
</p>

## About reShape

<b>reShape</b> is a skeleton project serving the purpose of being a basis for large scale web applications. <b>reShape</b> is a suitable start point for projects involving the management of common retained data such as users and customers. The intention is to provide general support and provide a fair amount of customisation for a wide variety use cases.

<b>reShape</b> is currently split into two projects:

- [reShape API](https://github.com/henrybkr/reShape-api) - The API that offers functionality to interact primarily with the database.
- [reShape Vue](https://github.com/henrybkr/reShape-vue) - Vue.js front-end application for interacting with the API and presenting functionality via web browser.

## Changes

See the [extended changelog](CHANGELOG.md).

## Frontend Tech Stack

### Core
- [Vue 3](https://github.com/vuejs/vue-next)
- [Vite](https://github.com/vitejs/vite)
- [Pinia](https://pinia.vuejs.org/)
- [Tailwind CSS](https://tailwindcss.com/)

### Other
- [Vue Router](https://github.com/vuejs/vue-router)
- [Headless UI](https://headlessui.com/)
- [TanStack Query](https://github.com/TanStack/query)
- [TanStack Table](https://github.com/TanStack/table)
- [PrimeVue](https://primevue.org/)
- [Vue Tippy](https://github.com/kabbouchi/vue-tippy)
- [Unplugin Icons](https://github.com/antfu/unplugin-icons)
- [VueUse](https://vueuse.org/)
- [Chart.js](https://www.chartjs.org/)
- [Floating Vue](https://floating-vue.starpad.dev/)
- [Tiptap](https://tiptap.dev/)
- [VueUse](https://vueuse.org/)
- [shadcn-vue](https://www.shadcn-vue.com/)

## Licenses

- The Vue framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT). Please review other licenses for above mentioned tech as required, all of which are open source.
- <b>reShape</b> is a private project and should not be used under any circumstances without prior written consent.
