import { withThemeByClassName } from '@storybook/addon-styling';
import '../src/assets/scss/app.scss';
import { setup } from '@storybook/vue3';
import BiPersonFillAdd from '~icons/bi/person-fill-add';
import BiPlus from '~icons/bi/plus';

setup((app) => {
  app.component('IconBiPersonFillAdd', BiPersonFillAdd);
  app.component('IconBiPlus', BiPlus);
});

/** @type { import('@storybook/vue3').Preview } */
const preview = {
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
  },

  decorators: [
    // Adds theme switching support.
    // NOTE: requires setting "darkMode" to "class" in your tailwind config
    withThemeByClassName({
      themes: {
        light: 'light',
        dark: 'dark',
      },
      defaultTheme: 'light',
    }),
  ],
};

export default preview;
