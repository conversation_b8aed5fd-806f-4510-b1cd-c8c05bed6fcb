import Icons from 'unplugin-icons/vite';
import { mergeConfig } from 'vite';
import Components from 'unplugin-vue-components/vite';
import IconsResolver from 'unplugin-icons/resolver';

/** @type { import('@storybook/vue3-vite').StorybookConfig } */
const config = {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-interactions',
    {
      name: '@storybook/addon-styling',
      options: {},
    },
  ],
  framework: {
    name: '@storybook/vue3-vite',
    options: {},
  },
  docs: {
    autodocs: 'tag',
  },
  async viteFinal(config, { configType }) {
    return mergeConfig(config, {
      // customize the Vite config here
      plugins: [
        Components({
          resolvers: [
            IconsResolver({
              prefix: 'icon',
            }),
          ],
        }),
        Icons({
          compiler: 'vue3',
        }),
      ],
    });
  },
};
export default config;
