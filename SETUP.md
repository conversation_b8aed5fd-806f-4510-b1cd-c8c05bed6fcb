Step 1: Install mkcert and set up local CA

    Install mkcert:

        macOS: brew install mkcert

        Windows: Download mkcert

        Linux: Follow instructions on mkcert GitHub

    Run these commands in terminal:

    mkcert -install
    mkcert localhost

    This generates:

        localhost.pem (certificate)

        localhost-key.pem (key)

✅ Step 2: Enable HTTPS in Vite

    Place localhost.pem and localhost-key.pem in the root or a certs/ folder.

    Update your vite.config.js:

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import fs from 'fs'

export default defineConfig({
  plugins: [vue()],
  server: {
    https: {
      key: fs.readFileSync('certs/localhost-key.pem'),
      cert: fs.readFileSync('certs/localhost.pem')
    },
    port: 3000,
    host: 'localhost'
  }
})

Restart the Vue app:

npm run dev

Now accessible via: https://localhost:3000