<?php

namespace Reshape\Auth\Http\Controllers;

use Throwable;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;
use Laravel\Socialite\Facades\Socialite;
use Reshape\Users\Actions\ActiveOauthProviderAction;
use Reshape\Users\Actions\HandleOauthCallbackAction;
use Reshape\Users\Exceptions\OAuthAccountLinkingException;
use Laravel\Socialite\Two\InvalidStateException;
use Lara<PERSON>\Socialite\Two\User as SocialiteUser;
use Symfony\Component\HttpFoundation\RedirectResponse as SymfonyRedirectResponse;
use Illuminate\Http\JsonResponse;

final class OauthRegisterController extends Controller
{
    public function __construct(
        private readonly HandleOauthCallbackAction $handleOauthCallbackAction,
    ) {}

    /**
     * Redirect the user to the OAuth provider.
     */
    public function redirect(string $provider): JsonResponse
    {
        $provider = $provider . '_register';

        if (!$this->isValidProvider($provider) || !Auth::guest()) {
            abort(404);
        }

        $url = Socialite::driver($provider)->redirect()->getTargetUrl();

        return response()->json([
            'data' => [
                'url' => $url,
            ],
        ]);
    }

    /**
     * Handle the callback from the OAuth provider.
     */
    public function callback(string $provider): RedirectResponse
    {
        $originalProvider = $provider;
        $provider = $provider . '_register';

        if (!$this->isValidProvider($provider) || !Auth::guest()) {
            abort(404);
        }

        $frontEndPath = config('app.frontend_url');
        $registerFrontEndPath = $frontEndPath . '/register';

        try {
            $socialiteUser = Socialite::driver($provider)->user();
            $authenticatedUser = Auth::user();
            $user = $this->handleOauthCallbackAction->handle($provider, $socialiteUser, $authenticatedUser, true);
        } catch (InvalidStateException $invalidStateException) {
            // Noticed that we will hit this error when there is a CSRF/domain mismatch. Both frontend and backend should be tested via localhost.
            return Redirect::intended($registerFrontEndPath . '?error=timeout&provider=' . $originalProvider);
        } catch (OAuthAccountLinkingException $oauthAccountLinkingException) {
            // Shorten the message we return.
            $exceptionMessage = $oauthAccountLinkingException->getMessage();
            $message = "link";
            if (str_contains($exceptionMessage, 'does not match your account email')) {
                $message = "email";
            }
            return Redirect::intended($registerFrontEndPath . '?error=' . urlencode($message) . '&provider=' . $originalProvider);
        } catch (Throwable $throwable) {
            var_dump($throwable);die;
            return Redirect::intended($registerFrontEndPath . '?error=server&provider=' . $originalProvider);
        }

        // Where the user is not yet authenticated, log them in and redirect to dashboard.
        if (Auth::guest()) {
            Auth::login($user, true);
            return Redirect::intended($frontEndPath);
        }

        return Redirect::intended($frontEndPath . '?success=true&provider=' . $originalProvider);
    }

    /**
     * Disconnect from the OAuth provider. WIP.
     */
    public function destroy(string $provider): RedirectResponse
    {
        abort_unless($this->isValidProvider($provider), 404);

        $user = Auth::user();

        $user?->oauthConnections()->where('provider', $provider)->delete();
        session()->flash('success', "Your {$provider} account has been unlinked.");

        return Redirect::route('profile.show');
    }

    private function isValidProvider(string $provider): bool
    {
        $activeProviders = (new ActiveOauthProviderAction)->handle();

        return collect($activeProviders)->contains('slug', $provider);
    }
}