<?php

namespace Reshape\Auth\Http\Controllers;

use Throwable;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;
use Laravel\Socialite\Facades\Socialite;
use Reshape\Users\Actions\ActiveOauthProviderAction;
use Reshape\Users\Actions\HandleOauthCallbackAction;
use Reshape\Users\Exceptions\OAuthAccountLinkingException;
use Laravel\Socialite\Two\InvalidStateException;
use Illuminate\Http\JsonResponse;
use Reshape\Users\Http\Resources\OauthConnectionResource;

final class OauthController extends Controller
{
    public function __construct(
        private readonly HandleOauthCallbackAction $handleOauthCallbackAction,
    ) {}

    /**
     * Authenticated user: Get a list of all OAuth connections.
     */
    public function index()
    {
        $connections = auth()->user()->oauthConnections()->orderBy('created_at', 'desc')->get();

        return OauthConnectionResource::collection($connections);
    }

    /**
     * Redirect the user to the OAuth provider.
     */
    public function redirect(string $provider): JsonResponse
    {
        if (!$this->isValidProvider($provider)) {
            abort(404);
        }

        $url = Socialite::driver($provider)->redirect()->getTargetUrl();

        return response()->json([
            'data' => [
                'url' => $url,
            ],
        ]);
    }

    /**
     * Handle the callback from the OAuth provider.
     */
    public function callback(string $provider): RedirectResponse
    {
        if (!$this->isValidProvider($provider)) {
            abort(404);
        }

        $frontEndPath = config('app.frontend_url');
        $linkedAccountsFrontEndPath = $frontEndPath . '/profile/oauth';
        $loginFrontEndPath = $frontEndPath . '/login';

        try {
            $socialiteUser = Socialite::driver($provider)->user();
            $authenticatedUser = Auth::user();
            $user = $this->handleOauthCallbackAction->handle($provider, $socialiteUser, $authenticatedUser);
        } catch (InvalidStateException $invalidStateException) {
            // Noticed that we will hit this error when there is a CSRF/domain mismatch. Both frontend and backend should be tested via localhost.
            return Redirect::intended(Auth::check()
                ? $linkedAccountsFrontEndPath . '?error=timeout&provider=' . $provider
                : $loginFrontEndPath . '?error=timeout&provider=' . $provider
            );
        } catch (OAuthAccountLinkingException $oauthAccountLinkingException) {
            // Shorten the message we return.
            $exceptionMessage = $oauthAccountLinkingException->getMessage();
            $message = "link";
            if (str_contains($exceptionMessage, 'does not match your account email')) {
                $message = "email";
            }
            
            return Redirect::intended(Auth::check()
                ? $linkedAccountsFrontEndPath . '?error=' . urlencode($message) . '&provider=' . $provider
                : $loginFrontEndPath . '?error=' . urlencode($message) . '&provider=' . $provider
            );
        } catch (Throwable $throwable) {
            Log::error('Failed to handle OAuth callback', [
                'provider' => $provider,
                'exception' => $throwable->getMessage(),
            ]);
            // var_dump($throwable);die;
            return Redirect::intended(Auth::check()
                ? $linkedAccountsFrontEndPath . '?error=server&provider=' . $provider
                : $loginFrontEndPath . '?error=server&provider=' . $provider
            );
        }

        // Where the user is not yet authenticated, log them in and redirect to dashboard.
        if (Auth::guest() && $user) {
            Auth::login($user, true);
            // return redirect($frontEndPath . '?success=true&provider=' . $provider);
            return Redirect::intended($frontEndPath . '?success=true&provider=' . $provider);
        } else if (Auth::guest() && !$user) {
            return Redirect::intended($loginFrontEndPath . '?error=user&provider=' . $provider);
        }
        
        return Redirect::intended($linkedAccountsFrontEndPath . '?success=true&provider=' . $provider);
    }

    /**
     * Authenticated user: Disconnect from the OAuth provider.
     */
    public function destroy(string $provider)
    {
        abort_unless($this->isValidProvider($provider), 404);

        auth()->user()->oauthConnections()->where('provider', $provider)->delete();

        return response()->noContent();
    }

    private function isValidProvider(string $provider): bool
    {
        $activeProviders = (new ActiveOauthProviderAction)->handle();

        return collect($activeProviders)->contains('slug', $provider);
    }
}