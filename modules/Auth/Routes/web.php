<?php

use Illuminate\Support\Facades\Route;
use Reshape\Auth\Http\Controllers\AuthenticationController;
use Reshape\Auth\Http\Controllers\NewOtpController;
use Reshape\Auth\Http\Controllers\NewPasswordController;
use Reshape\Auth\Http\Controllers\OtpVerificationController;
use Reshape\Auth\Http\Controllers\PasswordController;
use Reshape\Auth\Http\Controllers\PasswordResetLinkController;
use Reshape\Auth\Http\Controllers\RegisterController;
use Reshape\Auth\Http\Controllers\OauthController;
use Reshape\Auth\Http\Controllers\OauthRegisterController;

Route::middleware('guest')->group(function () {
    Route::post('/register', RegisterController::class);
    Route::post('/register/otp/verify', OtpVerificationController::class);
    Route::post('/register/otp/resend', NewOtpController::class);
    Route::post('/register/password', PasswordController::class);

    

    Route::post('/login', [AuthenticationController::class, 'login']);

    Route::post('/password/forgot', PasswordResetLinkController::class)->name('password.email');

    Route::post('/password/reset', NewPasswordController::class)->name('password.update');
});

Route::delete('/logout', [AuthenticationController::class, 'logout'])->middleware('auth:sanctum');

Route::get('/auth/callback/{provider}', [OauthController::class, 'callback']); // Socialite Authentication callback.
Route::get('/auth/register/callback/{provider}', [OauthRegisterController::class, 'callback']); // Socialite registration callback.
