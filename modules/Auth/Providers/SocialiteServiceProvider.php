<?php

namespace Reshape\Auth\Providers;

use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON>\Socialite\Facades\Socialite;
use <PERSON>vel\Socialite\Two\LinkedInProvider;
use <PERSON>vel\Socialite\Two\GoogleProvider;
use <PERSON>vel\Socialite\Two\GithubProvider;

class SocialiteServiceProvider extends ServiceProvider
{
    // Register custom OAuth drivers.
    public function boot()
    {
        Socialite::extend('google_register', function ($app) {
            $config = $app['config']['services.google_register'];
            return Socialite::buildProvider(
                GoogleProvider::class, 
                $config
            );
        });
        
        Socialite::extend('linkedin_register', function ($app) {
            $config = $app['config']['services.linkedin_register'];
            return Socialite::buildProvider(
                LinkedInProvider::class,
                $config
            );
        });
        
        Socialite::extend('github_register', function ($app) {
            $config = $app['config']['services.github_register'];
            return Socialite::buildProvider(
                GithubProvider::class, 
                $config
            );
        });
    }
}