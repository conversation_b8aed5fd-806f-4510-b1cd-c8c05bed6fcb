<?php

namespace Reshape\Users\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Reshape\Users\Models\User;

class OauthConnection extends Model
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'provider',
        'provider_id',
        'data',
        'token',
        'refresh_token',
        'expires_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'data' => 'array',
        'expires_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'token',
        'refresh_token',
    ];

    /**
     * Get the user that owns the OAuth connection.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the token is expired.
     */
    public function isTokenExpired(): bool
    {
        if (!$this->expires_at) {
            return false;
        }

        return $this->expires_at->isPast();
    }

    /**
     * Get the provider name in a human-readable format.
     */
    public function getProviderNameAttribute(): string
    {
        return match ($this->provider) {
            'google' => 'Google',
            'facebook' => 'Facebook',
            'apple' => 'Apple',
            'github' => 'GitHub',
            'twitter' => 'Twitter',
            'linkedin' => 'LinkedIn',
            default => ucfirst($this->provider),
        };
    }

    /**
     * Get the user's avatar from the OAuth data.
     */
    public function getAvatarAttribute(): ?string
    {
        return $this->data['avatar'] ?? $this->data['picture'] ?? $this->data['avatar_url'] ?? null;
    }

    /**
     * Get the user's name from the OAuth data.
     */
    public function getNameAttribute(): ?string
    {
        return $this->data['name'] ?? null;
    }

    /**
     * Get the user's email from the OAuth data.
     */
    public function getEmailAttribute(): ?string
    {
        return $this->data['email'] ?? null;
    }

    /**
     * Scope to filter by provider.
     */
    public function scopeProvider($query, string $provider)
    {
        return $query->where('provider', $provider);
    }

    /**
     * Scope to filter by provider ID.
     */
    public function scopeProviderId($query, string $providerId)
    {
        return $query->where('provider_id', $providerId);
    }

    /**
     * Find an OAuth connection by provider and provider ID.
     */
    public static function findByProvider(string $provider, string $providerId): ?self
    {
        return static::where('provider', $provider)
            ->where('provider_id', $providerId)
            ->first();
    }
}
