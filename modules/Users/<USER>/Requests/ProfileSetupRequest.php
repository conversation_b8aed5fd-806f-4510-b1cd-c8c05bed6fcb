<?php

namespace Reshape\Users\Http\Requests;

use App\Features\Geo\Models\Country;
use App\Features\Geo\Models\Timezone;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;
use Reshape\Common\Models\Honorific;
use Reshape\Http\Request\FormRequest;

class ProfileSetupRequest extends FormRequest
{
    /**
     * Valid step names for profile setup.
     * This can be extended with additional steps in the future.
     */
    protected const VALID_ONBOARDING_STEPS = [
        'profile',
        // Might add more here later, such as 'company', etc.
    ];

    /**
     * Get the validation rules that apply to the request.
     * Rules vary based on the step being processed.
     */
    public function rules(): array
    {
        // For create method (POST), use numeric step.
        if ($this->isMethod('POST')) {
            $step = $this->input('step', 1);

            return match ($step) {
                1 => $this->stepOneRules(),      // About You step
                2 => $this->stepTwoRules(),      // Complete Profile step
            };
        }

        // For update method (PUT), use string step from route parameter
        $step = $this->route('step');

        return match ($step) {
            'profile' => $this->stepTwoRules(), // Profile completion step
        };
    }

    /**
     * Validation rules for Step 1: About You
     * Required fields: first_name, last_name, current_country_id, timezone_id, job_title
     */
    protected function stepOneRules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z\s\'-]+$/'],
            'last_name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z\s\'-]+$/'],
            'current_country_id' => ['required', 'integer', Rule::exists(Country::class, 'id')],
            'timezone_id' => ['required', 'integer', Rule::exists(Timezone::class, 'id')],
            'job_title' => ['string', 'max:255', 'regex:/^[a-zA-Z0-9\s\.\-&\/]+$/'],
            'middle_names' => ['nullable', 'string', 'max:255', 'regex:/^[a-zA-Z\s\'-]+$/'],
            'current_country_extra' => ['nullable', 'string', 'max:255', 'regex:/^[a-zA-Z0-9\s\.\,\-]+$/'],
            'honorific_id' => ['nullable', 'integer', Rule::exists(Honorific::class, 'id')],
        ];
    }

    /**
     * Validation rules for Step 2: Complete Profile
     * All fields are optional for this step
     */
    protected function stepTwoRules(): array
    {
        return [
            'profile_photo' => ['nullable', 'file', 'mimes:jpg,jpeg,png', 'max:1024'],
            'from_country_id' => ['nullable', 'integer', Rule::exists(Country::class, 'id')],
            'from_country_extra' => ['nullable', 'string', 'max:255', 'regex:/^[a-zA-Z0-9\s\.\,\-]+$/'],
            'intro' => ['nullable', 'string', 'max:200', 'regex:/^[a-zA-Z0-9\s\.\,\!\?\-\(\)\'\"]+$/'],
            'bio' => ['nullable', 'string', 'max:1000', 'regex:/^[a-zA-Z0-9\s\.\,\!\?\-\(\)\'\"\:\;]+$/'],
        ];
    }

    /**
     * Prepare the data for validation.
     * Decode any hashed IDs that need to be converted to integers.
     */
    protected function prepareForValidation(): void
    {
        $this->decode([
            'timezone_id',
            'honorific_id',
            'current_country_id',
            'from_country_id',
        ]);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator(Validator $validator): void
    {
        // Only validate step parameter for PUT requests
        if ($this->isMethod('PUT')) {
            $validator->after(function ($validator) {
                $step = $this->route('step');

                if (!in_array($step, self::VALID_ONBOARDING_STEPS)) {
                    $validator->errors()->add('step', 'Invalid step parameter. Allowed values: ' . implode(', ', self::VALID_ONBOARDING_STEPS));
                }
            });
        }
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'First name is required.',
            'first_name.regex' => 'First name may only contain letters, spaces, hyphens, and apostrophes.',
            'last_name.required' => 'Last name is required.',
            'last_name.regex' => 'Last name may only contain letters, spaces, hyphens, and apostrophes.',
            'middle_names.regex' => 'Middle names may only contain letters, spaces, hyphens, and apostrophes.',
            'current_country_id.required' => 'Current country is required.',
            'current_country_id.exists' => 'The selected country is invalid.',
            'current_country_extra.regex' => 'Location details may only contain letters, numbers, spaces, periods, commas, and hyphens.',
            'timezone_id.required' => 'Timezone is required.',
            'timezone_id.exists' => 'The selected timezone is invalid.',
            'job_title.required' => 'Job title is required.',
            'job_title.regex' => 'Job title may only contain letters, numbers, spaces, periods, hyphens, ampersands, and forward slashes.',
            'profile_photo.mimes' => 'Profile photo must be a JPG, JPEG, or PNG file.',
            'profile_photo.max' => 'Profile photo must not be larger than 1MB.',
            'from_country_extra.regex' => 'Location details may only contain letters, numbers, spaces, periods, commas, and hyphens.',
            'intro.max' => 'Introduction must not exceed 200 characters.',
            'intro.regex' => 'Introduction contains invalid characters.',
            'bio.max' => 'Bio must not exceed 1000 characters.',
            'bio.regex' => 'Bio contains invalid characters.',
        ];
    }
}