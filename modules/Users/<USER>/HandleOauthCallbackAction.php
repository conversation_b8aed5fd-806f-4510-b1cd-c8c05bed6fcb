<?php

namespace Reshape\Users\Actions;

use Reshape\Users\Models\User;
use App\Traits\AsFakeAction;
use InvalidArgumentException;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Reshape\Users\Exceptions\OAuthAccountLinkingException;
use Laravel\Socialite\Two\User as SocialiteUser;
use Reshape\Users\Jobs\UpdateUserProfileInformationJob;

final readonly class HandleOauthCallbackAction
{
    use AsFakeAction;

    public function handle(
        string $provider,
        SocialiteUser $socialiteUser,
        ?User $authenticatedUser = null,
        bool $isRegistration = false,
    ): User | null
    {
        return match (true) {
            $authenticatedUser instanceof User => $this->handleAuthenticatedUser($provider, $socialiteUser, $authenticatedUser),
            default => $this->handleUnauthenticatedUser($provider, $socialiteUser, $isRegistration),
        };
    }

    private function handleAuthenticatedUser(string $provider, SocialiteUser $socialiteUser, User $user): User
    {
        $this->validateAuthenticatedUserConnection($provider, $socialiteUser, $user);

        $this->updateUserProfile($user, $socialiteUser, $provider);

        return $user;
    }

    private function handleUnauthenticatedUser(string $provider, SocialiteUser $socialiteUser, bool $isRegistration): User | null
    {
        return DB::transaction(function () use ($provider, $socialiteUser, $isRegistration): User | null {
            $socialiteEmail = strtolower($socialiteUser->getEmail());
            $existingUser = User::query()
                ->whereEmail($socialiteEmail)
                ->first();

            // In this scenario, we are allowing login only.
            if (!$isRegistration && $existingUser) {
                return $existingUser;
            }

            
            // Only other case should be registration.
            return match (true) {
                $existingUser instanceof User => $this->handleExistingUser($existingUser, $provider, $socialiteUser),
                default => $this->createNewUserWithOauth($socialiteEmail, $provider, $socialiteUser),
            };
        });
    }

    private function validateAuthenticatedUserConnection(string $provider, SocialiteUser $socialiteUser, User $user): void
    {
        try {
            Validator::validate([
                'provider' => $provider,
                'provider_id' => $socialiteUser->getId(),
                'email' => strtolower($socialiteUser->getEmail()), // Case sensitivity handling.
            ], [
                'provider' => [
                    'required',
                    'max:255',
                    Rule::unique('oauth_connections')->where(
                        fn (Builder $query) => $query->where('provider', $provider)
                            ->where('provider_id', $socialiteUser->getId())
                    ),
                ],
                'email' => ['required', 'email', Rule::in([strtolower($user->email)])], // Case sensitivity handling.
            ], [
                'provider.unique' => __('This account from :provider is already connected to another account.', ['provider' => $provider]),
                'email.in' => __('The email address from this :provider does not match your account email.', ['provider' => $provider]),
            ]);
        } catch (ValidationException) {
            throw_if(strtolower($socialiteUser->getEmail()) !== strtolower($user->email), OAuthAccountLinkingException::emailMismatch($provider));

            throw new InvalidArgumentException(__('Validation error try again later.'));
        }
    }

    private function handleExistingUser(User $user, string $provider, SocialiteUser $socialiteUser): User
    {
        // If the user already has an OAuth connection for this provider, throw an exception
        // because they should login instead of register
        throw_if(
            $user->oauthConnections()->where('provider', $provider)->exists(),
            OAuthAccountLinkingException::existingConnection()
        );

        $this->updateUserProfile($user, $socialiteUser, $provider);

        return $user;
    }

    private function createNewUserWithOauth(string $email, string $provider, SocialiteUser $socialiteUser): User
    {
        // Provider will likely include "_register" so we need to remove it.
        $provider = str_replace('_register', '', $provider);
        // var_dump($socialiteUser);die;

        $user = User::create([
            'email' => $email,
            'email_verified_at' => now(),
            'status_id' => User::STATUS_ACTIVE,
        ]);

        $user->assign('standard-user');

        $this->updateUserProfile($user, $socialiteUser, $provider);

        return $user;
    }

    private function updateUserProfile(User $user, SocialiteUser $socialiteUser, string $provider): void
    {
        dispatch_sync(new UpdateUserProfileInformationJob($user, $socialiteUser, $provider));
    }
}