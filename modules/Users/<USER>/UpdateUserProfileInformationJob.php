<?php

namespace Reshape\Users\Jobs;

use Reshape\Users\Models\User;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Laravel\Socialite\Two\User as SocialiteUser;

final class UpdateUserProfileInformationJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(private User $user,
        private readonly SocialiteUser $socialiteUser, private readonly string $provider)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $user = $this->user;
        $socialiteUser = $this->socialiteUser;

        var_dump(['socialiteUser' => $socialiteUser]);print_r("<br><br>");
        var_dump(['user' => $user]);print_r("<br><br>");
        
        var_dump(['provider' => $this->provider]);print_r("<br><br>");
        var_dump(['provider_id' => $socialiteUser->getId()]);print_r("<br><br>");
        var_dump(['data' => $socialiteUser->getRaw()]);print_r("<br><br>");
        var_dump(['token' => $socialiteUser->token]);print_r("<br><br>");
        var_dump(['refresh_token' => $socialiteUser->refreshToken]);print_r("<br><br>");
        var_dump(['expires_in' => ($socialiteUser->expiresIn ? now()->addSeconds($socialiteUser->expiresIn) : null)]);print_r("<br><br>");
        
        var_dump($user->oauthConnections());die;

        die;

        $user->oauthConnections()->updateOrCreate([
            'provider' => $this->provider,
        ], [
            'provider_id' => $socialiteUser->getId(),
            'data' => $socialiteUser->getRaw(),
            'token' => $socialiteUser->token,
            'refresh_token' => $socialiteUser->refreshToken,
            'expires_at' => $socialiteUser->expiresIn ? now()->addSeconds($socialiteUser->expiresIn) : null,
        ]);

        // $user->profile_photo_path = $socialiteUser->getAvatar(); // TODO
        $user->email_verified_at ??= now();
        $user->save();

        var_dump("OK!");die;
    }
}