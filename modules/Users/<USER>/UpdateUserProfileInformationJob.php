<?php

namespace Reshape\Users\Jobs;

use Reshape\Users\Models\User;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use <PERSON>vel\Socialite\Two\User as SocialiteUser;

final class UpdateUserProfileInformationJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(private User $user,
        private readonly SocialiteUser $socialiteUser, private readonly string $provider)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $user = $this->user;
        $socialiteUser = $this->socialiteUser;

        var_dump(['user_id' => $user->id]);print_r("<br><br>");
        var_dump(['user_exists_in_db' => $user->exists]);print_r("<br><br>");
        var_dump(['provider' => $this->provider]);print_r("<br><br>");
        var_dump(['provider_id' => $socialiteUser->getId()]);print_r("<br><br>");

        // Check if user has any existing OAuth connections
        $existingConnections = $user->oauthConnections()->get();
        var_dump(['existing_oauth_connections' => $existingConnections->toArray()]);print_r("<br><br>");

        // Try to see what happens when we try to create the connection
        try {
            var_dump("About to create OAuth connection...");print_r("<br><br>");

            $connection = $user->oauthConnections()->updateOrCreate([
                'provider' => $this->provider,
            ], [
                'provider_id' => $socialiteUser->getId(),
                'data' => $socialiteUser->getRaw(),
                'token' => $socialiteUser->token,
                'refresh_token' => $socialiteUser->refreshToken,
                'expires_at' => $socialiteUser->expiresIn ? now()->addSeconds($socialiteUser->expiresIn) : null,
            ]);

            var_dump(['oauth_connection_created' => $connection->toArray()]);print_r("<br><br>");

        } catch (\Exception $e) {
            var_dump(['error' => $e->getMessage()]);print_r("<br><br>");
            var_dump(['error_file' => $e->getFile()]);print_r("<br><br>");
            var_dump(['error_line' => $e->getLine()]);print_r("<br><br>");
            var_dump(['error_trace' => $e->getTraceAsString()]);print_r("<br><br>");
            die;
        }

        // $user->profile_photo_path = $socialiteUser->getAvatar(); // TODO
        $user->email_verified_at ??= now();
        $user->save();

        var_dump("OK!");die;
    }
}