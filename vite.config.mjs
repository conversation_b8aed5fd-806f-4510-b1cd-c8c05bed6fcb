import { defineConfig } from 'vite';
import Vue from '@vitejs/plugin-vue';
import Icons from 'unplugin-icons/vite';
import IconsResolver from 'unplugin-icons/resolver';
import Components from 'unplugin-vue-components/vite';
import path from 'path';
import { fileURLToPath, URL } from 'url';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    Vue(),
    Components({
      resolvers: [
        IconsResolver({
          prefix: 'icon',
        }),
      ],
    }),
    Icons({
      compiler: 'vue3',
    }),
  ],
  define: {
    'process.env': process.env,
  },
  server: {
    host: true,
    port: 3000,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@assets': fileURLToPath(new URL('./src/assets', import.meta.url)),
      '@users': path.resolve(__dirname, './src/modules/users'),
      '@companies': path.resolve(__dirname, './src/modules/companies'),
      '@customers': path.resolve(__dirname, './src/modules/customers'),
      '@settings': path.resolve(__dirname, './src/modules/settings'),
      '@contacts': path.resolve(__dirname, './src/modules/contacts'),
      '@util': path.resolve(__dirname, './src/util'),
      'tailwind.config.js': path.resolve(__dirname, 'tailwind.config.js'),
    },
  },
  optimizeDeps: {
    include: ['tailwind.config.js'],
  },
});
