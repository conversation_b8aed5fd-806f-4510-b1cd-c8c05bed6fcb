import { useRouter } from 'vue-router';

/**
 * Determine if the user is authenticated but doesn't have a profile yet.
 */
export const ProfileMissing = async ({ to, next }) => {
  const { useAuth, useToasts } = await import('@/store');
  const store = useAuth();
  const router = useRouter();

  // Check that the user is authenticated.
  if (!store.user) {
    // Only show toast if this is not already a login page or auth-related page.
    const toastStore = useToasts();
    const existingToast = toastStore.toasts.find(t => t.title === 'Authentication Required');

    const redirect = !!to.fullPath.slice(1) ?
      { name: 'auth.login', query: { redirect: to.fullPath } }
      : { name: 'auth.login' };
    
    Reshape.toast(
      {
        title: 'Authentication Required',
        message: 'You must be logged in to access this page.'
      },
      'warning'
    );

    next(redirect);
  } else if (store.user?.profile) {
    // User already has a profile, redirect to dashboard.

    // The problem -- The below retains the route (/welcome) in this case, which is not acceptable. I am expecting a redirect.
    // next({ name: 'index' });

    // What is a better way to handle this so we send the user to the named route 'index'?
    next({ name: 'index' });
  } else {
    next();
  }
};
