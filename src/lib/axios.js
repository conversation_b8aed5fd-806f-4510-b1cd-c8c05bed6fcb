import Axios from 'axios';

export const CancelToken = Axios.CancelToken;

import qs from 'qs';

import { Http } from '@/types';

export const axios = Axios.create({
  baseURL: import.meta.env.VITE_BACKEND_URL,
  withCredentials: true,
  headers: {
    'X-Requested-With': 'XMLHttpRequest',
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
  paramsSerializer: (params) => {
    return qs.stringify(params, { encode: false });
  },
});

axios.interceptors.response.use(
  (response) => response.data,
  async (error) => {
    const response = error.response;
    const { useAuth } = await import('@/store');
    const { logoutIfAuthenticated } = useAuth();

    switch (response.status) {
      // Handle Session Timeouts (Unauthorized)
      case Http.UNAUTHORIZED:
      case Http.SESSION_EXPIRED:
        // Only show session expired toast if user was actually authenticated
        // and this isn't just a user check (like from authAttempt middleware).
        const authStore = useAuth();
        const isUserCheckRequest = error.config?.url?.includes('/api/user');

        if (authStore.authenticated && !isUserCheckRequest) {
          Reshape.toast(
            {
              title: 'Session Expired',
              message: 'Your session has expired. Please log in again to continue.'
            },
            'warning'
          );
          logoutIfAuthenticated(false); // No toast - let middleware handle redirect.
        } else if (authStore.authenticated && isUserCheckRequest) {
          // Silent logout for user check requests.
          logoutIfAuthenticated(false);
        }
        break;

      // Handle Forbidden
      case Http.FORBIDDEN:
        // Don't redirect to 403 if logout is in progress.
        if (typeof window !== 'undefined' && window.__LOGOUT_IN_PROGRESS__) {
          // During logout, ignore 403 errors as they're expected.
          break;
        }
        
        // Disabled for now.
        // Use window.location for navigation since useRouter() can't be called outside component context
        // if (typeof window !== 'undefined') {
        //   window.location.href = '/403';
        // }

        break;

      // Handle Server Error
      case Http.INTERNAL_SERVER_ERROR:
      case Http.NOT_IMPLEMENTED:
      case Http.SERVICE_UNAVAILABLE:
      default:
        Reshape.toast({ title: response.data.message }, 'danger');
    }

    return Promise.reject(error);
  }
);
