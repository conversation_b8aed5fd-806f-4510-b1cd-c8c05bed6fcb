<template>
  <div>
    <VPageHeader
      title="Add Currency"
      description="Define a new currency for use throughout the application."
    >
      <template #actions>
        <VButton type="submit"
          form="currency-form"
          intent="secondary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>
        <VButton
          intent="danger"
          @click="confirmModalOpen = true"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <CurrencyForm @submit="submit" />

      <div class="flex w-full justify-start lg:justify-end space-x-3">
        <VButton
          type="submit"
          form="currency-form"
          intent="primary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>

        <VButton intent="danger" @click="confirmModalOpen = true">
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </div>
    </VContainer>
    
    <VConfirmModal
      @confirmed="router.push({ name: 'settings.currencies.index' })"
      v-model:open="confirmModalOpen"
    />
  </div>
</template>

<script setup>
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconPlusLg from '~icons/bi/plus-lg';
import CurrencyForm from '@/modules/currencies/components/CurrencyForm.vue';
import { ref } from 'vue';
import { useCreateCurrency } from '@/modules/currencies/api/post-currency';
const confirmModalOpen = ref(false);

const createCurrencyMutation = useCreateCurrency({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Currency Successfully Created',
          message: `The currency <b>${result.data?.name}</b> was successfully created.`,
        },
        'success'
      );
      router.push({
        name: 'settings.currencies.view',
        params: { id: result.data.locked_id }
      });
    },
  },
});

const submit = (form) => {
  form.submit((data) => createCurrencyMutation.mutateAsync({
    data,
  }));
};

</script>
