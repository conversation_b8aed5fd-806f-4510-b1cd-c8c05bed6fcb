<template>
  <div>
    <VPageHeader title="View Currency" description="Overview of selected currency." />
    <VContainer class="my-3">
      <TransitionFade>
        <div v-if="isLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>
        <div v-else-if="currency" class="grid gap-3 lg:grid-cols-5 items-start">
          <ViewPanel class="col-span-5 xl:col-span-4 order-2 xl:order-1">
            <PanelDefaultField label="Currency Name" :value="currency.name" allowCopy />
            <PanelDefaultField label="Symbol" :value="currency.symbol" allowCopy />
            <PanelDefaultField label="ISO" :value="currency.iso" allowCopy />
            <PanelBooleanField label="Is Popular" :value="currency.is_popular" />
            <PanelBooleanField label="Is Prefixed" :value="currency.is_prefixed" />
            <PanelDefaultField label="Status" :value="currency?.status?.name">
              <template #default>
                <CurrencyStatusBadge :status="currency?.status?.name" />
              </template>
            </PanelDefaultField>
            <PanelDateTimeField label="Date Created" :value="currency.created_at" />
            <PanelDateTimeField label="Date Updated" :value="currency.updated_at" />
          </ViewPanel>

          <VCard class="col-span-5 xl:col-span-1 order-1 xl:order-2">
            <VCardHeader>Actions</VCardHeader>
            <VCardBody class="flex gap-3 justify-start flex-wrap">
              <!-- Button that returns the user to the referring page. -->
              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'settings.currencies.index' }"
              >All Currencies
                <template #start>
                  <BIconListUl />
                </template>
              </VButton>

              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'settings.currencies.activity', params: { id: currency.locked_id } }"
              >View Activity
                <template #start>
                  <BIconActivity />
                </template>
              </VButton>

              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <VButton intent="danger">
                    <template #start>
                      <BIconPencilSquare />
                    </template>
                    <template #end>
                      <BIconChevronDown />
                    </template>
                    Modify
                  </VButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{
                        name: 'settings.currencies.update',
                        params: { id: currency.locked_id },
                      }"
                      class="w-full"
                    >
                      <BIconPencilSquare />Edit
                    </RouterLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem @click="openCurrencyDeleteConfirmationDialog">
                    <BIconTrash />Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </VCardBody>
          </VCard>
        </div>
        <template v-else>
          <VEmptyState
            title="Currency Not Found"
            description="The requested currency was not found."
            :contact="true"
          />
        </template>
      </TransitionFade>
    </VContainer>

    <VConfirmModal
      v-model:open="isCurrencyDeleteConfirmationDialog"
      @confirmed="deleteCurrencyMutation.mutate({
        currencyId: props.id
      })"
      title="Confirm Currency Deletion"
      :description="`<p>You're about to delete the requested currency: <b>${name}</b>.</p><p>Please confirm you wish to proceed.</p>`"
      :icon="BIconTrashFill"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BIconListUl from '~icons/bi/list-ul';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconChevronDown from '~icons/bi/chevron-down';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconArrowRight from '~icons/bi/arrow-right';
import BIconActivity from '~icons/bi/activity';
import ViewPanel from '@/components/ui/panels/ViewPanel.vue';
import PanelDefaultField from '@/components/ui/panels/fields/ViewPanel/PanelDefaultField.vue';
import PanelBooleanField from '@/components/ui/panels/fields/ViewPanel/PanelBooleanField.vue';
import PanelDateTimeField from '@/components/ui/panels/fields/ViewPanel/PanelDateTimeField.vue';
import PanelContactField from '@/components/ui/panels/fields/ViewPanel/PanelContactField.vue';
import CurrencyStatusBadge from '@/modules/currencies/components/CurrencyStatusBadge.vue'
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';

import { useDisclosure } from '@/hooks';
import { useRouter } from 'vue-router';
import { useDeleteCurrency } from '@/modules/currencies/api/delete-currency';
import { useCurrency } from '@/modules/currencies/api/get-currency';
import { ref } from 'vue';
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const router = useRouter();

const flagLoaded = ref(false);

const {
  isOpen: isCurrencyDeleteConfirmationDialog,
  open: openCurrencyDeleteConfirmationDialog
} = useDisclosure(false);

const { data: currency, isLoading } = useCurrency({
  currencyId: props.id,
});

// Computed property to return the currency name if set.
const name = computed(() => {
  return currency.value?.name ? currency.value.name : 'Undefined';
});

// Computed property to return the currency emoji if set.
const emojiValue = computed(() => {
  try {
    return JSON.parse(currency.value?.emoji);
  } catch (error) {
    return currency.value?.emoji ? currency.value.emoji : 'Undefined';
  }
});

// Computed property to convert three character codes to actual language string.
const languages = computed(() => {
  try {
    const languagesArray = JSON.parse(currency.value?.languages);
    const languages = languagesArray.map((language) => {
      return new Intl.DisplayNames(['en'], { type: 'language' }).of(language);
    });
    return languages.length > 0 ? languages.join(', ') : 'Undefined';
  } catch (error) {
    return currency.value?.languages ? currency.value.languages : 'Undefined';
  }
});

// Computed property converting the currencies array to a readable string.
const currencies = computed(() => {
  return currency.value?.currencies
    ? currency.value.currencies.map((currency) => currency.name).join(', ')
    : null;
});

// Computed property converting the tld array to a readable string.
const tld = computed(() => {
  if (!currency.value?.tld) return null;
  const arr = JSON.parse(currency.value?.tld);
  return arr.length > 0 ? arr.map((tld) => tld).join(', ') : null;
});

const deleteCurrencyMutation = useDeleteCurrency({
  mutationConfig: {
    onSuccess: () => {
      Reshape.toast(
        {
          title: 'Currency Deleted',
          message: `The requested currency <b>${name.value}</b> was successfully deleted.`,
        },
        'success'
      );
      router.push({
        name: 'settings.currencies.index',
      });
    },
  },
});

</script>
