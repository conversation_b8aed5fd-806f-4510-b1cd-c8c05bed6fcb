<template>
  <div>
    <VPageHeader
      title="Currencies"
      description="Overview of all currencies defined within the application."
    >
      <template #actions>
        <VButton
          as="router-link"
          intent="secondary"
          :to="{ name: 'settings.currencies.create' }"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Add Currency
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="py-2 lg:py-6">
      <CurrencyTable
        title="All Currencies"
        :records="currencies?.data"
        :total-records="currencies?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
      />
    </VContainer>
  </div>
</template>

<script setup lang="ts">
import { useFilterParams } from '@/hooks/query';
import CurrencyTable from '@/modules/currencies/components/CurrencyTable.vue';
import BIconPlusLg from '~icons/bi/plus-lg';
import { useCurrencies } from '../api/get-currencies';

const { params, search, sort, paginate } = useFilterParams();

const { data: currencies, isLoading } = useCurrencies({
  params: params.value,
});
</script>
