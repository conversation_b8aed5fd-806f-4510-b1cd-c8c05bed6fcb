<template>
  <div>
    <VPageHeader title="Update Currency" description="Update an existing currency in the system.">
      <template #actions>
        <VButton type="submit"
          form="currency-form"
          intent="secondary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Confirm
        </VButton>
        <VButton
          intent="danger"
          @click="confirmModalOpen = true"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <TransitionFade>
        <CurrencyForm v-if="!isCurrencyLoading" :currency="currency" @submit="submit" />
        <div v-else class="flex w-full justify-center p-6">
          <VSpinner size="xl" />
        </div>
      </TransitionFade>

      <div class="flex w-full justify-start lg:justify-end space-x-3">
        <VButton
          type="submit"
          form="currency-form"
          intent="primary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Confirm
        </VButton>

        <VButton intent="danger" @click="confirmModalOpen = true">
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </div>
    </VContainer>
    
    <VConfirmModal
      @confirmed="router.push({ name: 'settings.currencies.index' })"
      v-model:open="confirmModalOpen"
    />
  </div>
</template>

<script setup>
import CurrencyForm from '@/modules/currencies/components/CurrencyForm.vue';
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconCheckLg from '~icons/bi/check-lg';
import { ref } from 'vue';
import { useUpdateCurrency } from '@/modules/currencies/api/put-currency';
import { useCurrency } from '@/modules/currencies/api/get-currency';

const confirmModalOpen = ref(false);

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const updateCurrencyMutation = useUpdateCurrency({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Currency Updated',
          message: `The currency <b>${result.data?.name}</b> was successfully updated.`,
        },
        'success'
      );
      router.push({ name: 'settings.currencies.view', params: { id: result.data.locked_id } });
    },
  },
});

const { data: currency, isLoading: isCurrencyLoading } = useCurrency({
  currencyId: props.id
});

const submit = (form) => {
  form.submit((data) => updateCurrencyMutation.mutateAsync({
    data,
    currencyId: props.id,
  }));
};

</script>
