import UserLayout from '@/layouts/UserLayout.vue';
import { auth } from '@/router/middleware';

const routes = [
  {
    path: '/settings/currencies',
    component: UserLayout,
    meta: {
      middleware: auth,
    },
    children: [
      {
        path: '',
        name: 'settings.currencies.index',
        component: () => import('./pages/CurrencyIndex.vue'),
        meta: {
          title: 'Currencies',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Currencies', name: 'settings.currencies.index' },
          ],
        },
      },
      {
        path: ':id/view',
        name: 'settings.currencies.view',
        component: () => import('./pages/CurrencyView.vue'),
        props: true,
        meta: {
          title: 'View Currency',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Currencies', name: 'settings.currencies.index' },
            { label: 'View', name: 'settings.currencies.view' },
          ],
        },
      },
      {
        path: 'add',
        name: 'settings.currencies.create',
        component: () => import('./pages/CurrencyCreate.vue'),
        meta: {
          title: 'Add Currency',
          breadcrumbs: [
            { label: 'Currencies', name: 'settings.currencies.index' },
            { label: 'Add', name: 'settings.currencies.create' },
          ],
        },
      },
      {
        path: ':id/update',
        name: 'settings.currencies.update',
        component: () => import('./pages/CurrencyUpdate.vue'),
        props: true,
        meta: {
          title: 'Edit Currency',
          breadcrumbs: [
            { label: 'Currencies', name: 'settings.currencies.index' },
            { label: 'Edit', name: 'settings.currencies.update' },
          ],
        },
      },
      {
        path: ':id/activity',
        name: 'settings.currencies.activity',
        component: () => import('@/modules/activity/pages/ActivityIndex.vue'),
        props: (route) => ({
          activitableType: 'Currency',
          activitableId: route.params.id,
        }),
        meta: {
          title: 'Currency Activity',
          headerTitle: 'Currency Activity',
          headerDescription: 'View the activity for the selected currency.',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Currencies', name: 'settings.currencies.index' },
            { label: 'View', name: 'settings.currencies.view' },
            { label: 'Activity', name: 'settings.currencies.activity' },
          ],
        },
      },
    ],
  },
];

export default routes;
