<template>
  <form id="currency-form" class="space-y-4" @submit.prevent="submit">
    <FormContainer
      title="Currency Details"
      description="Typical values that define a currency."
      :icon="BIconCashStack"
    >
      <template #body>
        <div class="grid grid-cols-6 gap-6">
          <VField
            class="col-span-6 md:col-span-3"
            label="Name"
            :errors="form.errors.get('name')"
          >
            <VInput
              v-model="form.name"
              placeholder="Enter Currency Name"
            />
          </VField>

          <VField
            class="col-span-6 md:col-span-3"
            label="Symbol"
            required
            :errors="form.errors.get('symbol')"
          >
            <VInput
              v-model="form.symbol"
              placeholder="Enter Currency Symbol"
            />
          </VField>

          <VField
            class="col-span-6 md:col-span-2 lg:col-span-4"
            label="ISO"
            required
            :errors="form.errors.get('iso')"
            help="The 3 letter ISO code for the currency."
          >
            <VInput
              v-model="form.iso"
              placeholder="Enter ISO"
            />
          </VField>

          <VField
            class="col-span-3 md:col-span-2 lg:col-span-1"
            label="Is Fractional"
            help="Factional currencies are typically unofficial or alternative currencies typically used to promote local trade or political autonomy."
            required
          >
            <VToggle v-model="form.is_fractional" class="mt-3">
              <span class="text-platinum-800 dark:text-midnight-100">
                {{ form.is_fractional ? 'Yes' : 'No' }}
              </span>
            </VToggle>
          </VField>
          
          <VField
            class="col-span-3 md:col-span-2 lg:col-span-1"
            label="Is Popular"
            help="Toggle to define if the currency should be listed as a 'Popular' option."
            required
          >
            <VToggle v-model="form.is_popular" class="mt-3">
              <span class="text-platinum-800 dark:text-midnight-100">
                {{ form.is_popular ? 'Yes' : 'No' }}
              </span>
            </VToggle>
          </VField>

          <VField
            class="col-span-6 lg:col-span-3"
            label="Status"
            help="Selecting anything other than 'Active' will prevent the currency from being used in the application."
            required
            :errors="form.errors.get('status_id')"
          >
            <StatusCombobox
              v-model="form.status_id"
              statusable="Currency"
              placeholder="Select a Status"
              :default-option="props.currency ? null : 'Active'"
              required
            />
          </VField>
        </div>
      </template>
    </FormContainer>
  </form>
</template>

<script setup>
import FormContainer from '@/components/ui/forms/FormContainer.vue';
import { useForm } from '@/hooks/form';
import BIconCashStack from '~icons/bi/cash-stack';
import StatusCombobox from '@/components/ui/form/composables/StatusCombobox.vue';

const props = defineProps({
  currency: {
    type: Object,
    default: null,
  },
});

const emits = defineEmits(['submit']);

const submit = () => emits('submit', form.value);

const form = useForm({
  name: props.currency?.name ?? '',
  symbol: props.currency?.symbol ?? '',
  iso: props.currency?.iso ?? '',
  is_fractional: props.currency?.is_fractional ?? false,
  is_popular: props.currency?.is_popular ?? false,
  status_id: props.currency?.status?.locked_id ?? '',
});
</script>