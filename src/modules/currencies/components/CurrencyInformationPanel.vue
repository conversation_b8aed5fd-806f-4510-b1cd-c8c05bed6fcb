<template>
  <VInformationPanel :loading="isCurrencyLoading">
    <VDefaultField label="Name" :value="currency.name" />
    <VDefaultField label="Symbol" :value="currency.symbol" />
    <VDefaultField label="ISO" :value="currency.iso" />
    <VBooleanField label="Is Popular" :value="currency.is_popular" />
    <VTimeField label="Created At" :value="currency.created_at" />
    <VTimeField label="Updated At" :value="currency.updated_at" />
  </VInformationPanel>
</template>

<script setup>
import { useCurrency } from '@/modules/currencies/api/get-currency';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const { data: currency, isLoading: isCurrencyLoading } = useCurrency({
  currencyId: props.id,
});

</script>
