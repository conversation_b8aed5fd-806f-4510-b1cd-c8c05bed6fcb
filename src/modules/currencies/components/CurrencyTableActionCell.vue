<template>
  <div class="flex items-center justify-end space-x-1.5">
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <VButton intent="table">
          <BIconThreeDots class="h-5 w-5" />
        </VButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem as-child>
          <RouterLink
            :to="{
              name: 'settings.currencies.view',
              params: { id: row.locked_id },
            }"
          >
            <BIconSearch />View
          </RouterLink>
        </DropdownMenuItem>

        <DropdownMenuItem as-child>
          <RouterLink
            :to="{
              name: 'settings.currencies.activity',
              params: { id: row.locked_id },
            }"
          >
            <BIconActivity />Activity
          </RouterLink>
        </DropdownMenuItem>

        <DropdownMenuGroup>
          <DropdownMenuLabel>Modify</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem as-child>
            <RouterLink
              :to="{
                name: 'settings.currencies.update',
                params: { id: row.locked_id },
              }"
              class="w-full"
            >
              <BIconPencilSquare />Edit
            </RouterLink>
          </DropdownMenuItem>
          <DropdownMenuItem @click="openCurrencyDeleteConfirmationDialog">
            <BIconTrash />Delete
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>

    <VConfirmModal
      v-model:open="isCurrencyDeleteConfirmationDialog"
      @confirmed="deleteCurrencyMutation.mutate({
        currencyId: props.row.locked_id
      })"
      title="Confirm Currency Deletion"
      :description="`<p>You're about to delete the currency: <b>${props.row.name}</b>.</p><p>Please confirm you wish to proceed.</p>`"
      :icon="BIconTrashFill"
    />
  </div>
</template>

<script setup lang="ts">
import BIconSearch from '~icons/bi/search';
import BIconThreeDots from '~icons/bi/three-dots';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconActivity from '~icons/bi/activity';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/shadcn/dropdown-menu';
import { useDisclosure } from '@/hooks';
import { useDeleteCurrency } from '../api/delete-currency';

// Declare the Reshape global variable
declare const Reshape: {
  toast: (options: { title: string; message: string }, type: string) => void;
};

interface CurrencyRow {
  locked_id: string;
  name: string;
  [key: string]: any;
}

const props = defineProps<{
  row: CurrencyRow;
}>();

const deleteCurrencyMutation = useDeleteCurrency({
  mutationConfig: {
    onSuccess: () => {
      Reshape.toast(
        {
          title: 'Currency Deleted',
          message: `The currency <b>${props.row.name}</b> was successfully deleted.`,
        },
        'success'
      );
    },
  },
});

const {
  isOpen: isCurrencyDeleteConfirmationDialog,
  open: openCurrencyDeleteConfirmationDialog
} = useDisclosure(false);
</script>
