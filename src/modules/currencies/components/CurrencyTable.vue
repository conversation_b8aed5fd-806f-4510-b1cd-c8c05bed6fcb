<template>
  <Table
    v-bind="$attrs"
    :columns="columns"
    :default-sort="[{
      id: 'name',
      desc: true,
    }]"
    paginated
    sortable
    searchable
    controlled
  />
</template>

<script setup>
import { h, ref } from 'vue';

import Table from '@/components/ui/tables/Table.vue';

import ActionCell from './CurrencyTableActionCell.vue';
import CurrencyStatusBadge from './CurrencyStatusBadge.vue';
import TableCell from '@/components/ui/tables/TableCell.vue';
import CurrencyTableLink from '@/modules/currencies/components/CurrencyTableLink.vue';
import { humanizeTime } from '@/util/dateUtils';

const flagLoaded = ref(false);

const columns = ref([
  {
    accessorFn: (row) => row?.name,
    id: 'name',
    header: 'Name',
    // cell: (info) => h(TableCell, { value: info.getValue() }),
    cell: (info) => h(CurrencyTableLink, { row: info.row.original }),
  },
  {
    accessorFn: (row) => row?.symbol,
    id: 'symbol',
    header: 'Symbol',
    cell: (info) => h(TableCell, { value: info.getValue() }),
  },
  {
    accessorFn: (row) => row?.iso,
    id: 'iso',
    header: 'ISO',
    cell: (info) => h(TableCell, { value: info.getValue() }),
  },
  {
    accessorKey: 'created_at',
    header: 'Created Date',
    cell: (info) => humanizeTime(info.row.original.created_at),
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: (info) => h(CurrencyStatusBadge, {
      status: info.row.original.status?.name,
      key: info.row.original.locked_id,
    }),
  },
  {
    id: 'actions',
    header: () => h('span', { class: 'w-full flex justify-end' }),
    cell: (info) => h(ActionCell, {
      row: info.row.original,
      key: info.row.original.locked_id,
    }),
    enableSorting: false,
    enableResizing: false,
    size: 50,
  },
]);
</script>
