
<template>
  <EntityLink
    v-if="row.name"
    entity-type="Currency"
    :entity-id="row.locked_id"
    :show-icon="false"
    :value="row.name"
  />
  <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
</template>

<script setup>
import EntityLink from '@/components/ui/links/EntityLink.vue';

const props = defineProps({
  row: {
    type: Object,
    required: true,
  },
});

</script>