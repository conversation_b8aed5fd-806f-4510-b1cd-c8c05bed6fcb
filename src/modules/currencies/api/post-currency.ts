import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { Currency } from '@/types/api';

export const createCurrency = ({ data }: { data: CreateCurrencyPayload }) => {
  return Reshape.http().post(`/api/currencies`, data);
};

type CreateCurrencyPayload = Omit<Currency, 'id' | 'locked_id'>;

type UseCreateCurrencyOptions = {
  mutationConfig?: MutationConfig<typeof createCurrency>;
};

export const useCreateCurrency = ({ mutationConfig }: UseCreateCurrencyOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ['currencies'],
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createCurrency,
  });
};