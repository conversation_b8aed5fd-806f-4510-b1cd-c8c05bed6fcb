import { queryOptions, useQuery } from '@tanstack/vue-query';
import { Currency, Meta } from '@/types/api';

import { QueryConfig } from '@/lib/vue-query';

export const getCurrencies = (
  params,
  signal
): Promise<{
  data: Currency[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/currencies`, {
    params,
    signal,
  });
};

export const getCurrenciesQueryOptions = ({ params }: { params?: unknown } = {}) => {
  return queryOptions({
    queryKey: params ? ['currencies', params] : ['currencies'],
    queryFn: ({ signal }) => getCurrencies(params, signal),
  });
};

type UseCurrenciesOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getCurrenciesQueryOptions>;
};

export const useCurrencies = ({ queryConfig, params }: UseCurrenciesOptions = {}) => {
  return useQuery({
    ...getCurrenciesQueryOptions({ params }),
    ...queryConfig,
  });
}; 