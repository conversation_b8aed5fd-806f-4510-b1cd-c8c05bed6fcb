import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getCurrenciesQueryOptions } from './get-currencies';

// Declare the Reshape global variable
declare const Reshape: {
  http: () => {
    delete: (url: string) => Promise<any>;
  };
};

export const deleteCurrency = ({ currencyId }: { currencyId: string }) => {
  return Reshape.http().delete(`/api/currencies/${currencyId}`);
};

type UseDeleteCurrencyOptions = {
  mutationConfig?: {
    onSuccess?: (...args: any[]) => void;
    [key: string]: any;
  };
};

export const useDeleteCurrency = ({ mutationConfig }: UseDeleteCurrencyOptions = {}) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['currencies', args[1].currencyId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getCurrenciesQueryOptions().queryKey,
      });
      if (onSuccess) {
        onSuccess(...args);
      }
    },
    ...restConfig,
    mutationFn: deleteCurrency,
  });
}; 