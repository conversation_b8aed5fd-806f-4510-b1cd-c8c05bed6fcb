import { useQuery, queryOptions } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Currency } from '@/types/api';

export const getCurrency = ({
  currencyId,
}: {
  currencyId: string;
}): Promise<{ data: Currency }> => {
  return Reshape.http()
    .get(`/api/currencies/${currencyId}`)
    .then(({ data }) => data);
};

export const getCurrencyQueryOptions = (currencyId: string) => {
  return queryOptions({
    queryKey: ['currencies', currencyId],
    queryFn: () => getCurrency({ currencyId }),
  });
};

type UseCurrencyOptions = {
  currencyId: string;
  queryConfig?: QueryConfig<typeof getCurrencyQueryOptions>;
};

export const useCurrency = ({ currencyId, queryConfig }: UseCurrencyOptions) => {
  return useQuery({
    ...getCurrencyQueryOptions(currencyId),
    ...queryConfig,
  });
}; 