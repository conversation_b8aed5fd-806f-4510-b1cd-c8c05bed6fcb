import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getCurrencyQueryOptions } from './get-currency';

import { Currency } from '@/types/api';

export const updateCurrency = ({ currencyId, data }: { currencyId: string, data: UpdateCurrencyPayload }) => {
  return Reshape.http().put(`/api/currencies/${currencyId}`, data);
};

type UpdateCurrencyPayload = Partial<Omit<Currency, 'locked_id'>>;

type UseUpdateCurrencyOptions = {
  currencyId: string;
  mutationConfig?: MutationConfig<typeof updateCurrency>;
};

export const useUpdateCurrency = ({ currencyId, mutationConfig }: UseUpdateCurrencyOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['currencies', args[1].currencyId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getCurrencyQueryOptions(args[1].currencyId).queryKey,
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: updateCurrency,
  });
};