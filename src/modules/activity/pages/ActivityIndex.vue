<template>
  <div>
    <VPageHeader
      return-action
      :return-action-exclude-named-routes="['activity.view']"
    />
    <VContainer class="py-2 lg:py-6">
      <ActivityTable
        title="Activity"
        :records="activity?.data"
        :total-records="activity?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
      />
    </VContainer>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import ActivityTable from '@/modules/activity/components/ActivityTable.vue';
import { useActivities } from '@/modules/activity/api/get-activities';
import { useUserActivities } from '@/modules/users/api/get-user-activities';
import { useCustomerActivities } from '@/modules/customers/api/get-customer-activities';
import { useCompanyActivities } from '@/modules/companies/api/get-company-activities';
import { useCompanyUserActivities } from '@/modules/companies/api/get-company-user-activities';
import { useFilterParams } from '@/hooks/query';
import { useAuth } from '@/store';
import { useRouter } from 'vue-router';

const props = defineProps({
  activitableType: {
    type: String,
    required: false,
  },
  activitableId: {
    type: String,
    required: false,
  },
  self: {
    // Generally only used in the case of user.
    type: Boolean,
    default: false,
  },
  caused: {
    // Generally only used in the case of user - Whether the activity is caused by the entity.
    // In the case of company, suggests we want linked user caused activities. 
    type: Boolean,
    default: false,
  },
});

const router = useRouter();

// Lowercase the activitable type.
const activitableTypeLower = computed(() => {
  return props?.activitableType?.toLowerCase();
});

// Return the activitable ID based on the self prop and activitableType.
const activitableId = computed(() => {
  if (!props?.self && !props?.activitableId) {
    return null;
  }
  if (props?.self && props?.activitableType.toLowerCase() === 'user') {
    const auth = useAuth(); // Only needed if self is true.
    return auth.user.locked_id;
  }
  return props.activitableId;
});

// Get the filter params.
const { params, search, sort, paginate } = useFilterParams();

// Default sort is newest first.
params.value.sort = '-created_at';

// Dedicated activity endpoints.
const activityEndpoints = {
  'user': useUserActivities,  
  'company': useCompanyActivities,
  'customer': useCustomerActivities,
};

// Dedicated caused endpoints.
const activityCausedEndpoints = {
  'company': useCompanyUserActivities,
};

// If there is a dedicated activity endpoint, use it, else use the general endpoint with filters where relevant.
const useActivityHook = () => {
  // If caused is true and there is a match, use that.
  if (props.caused && activityCausedEndpoints[activitableTypeLower.value]) {
    return activityCausedEndpoints[activitableTypeLower.value]({
      params: params.value,
      id: activitableId.value,
    });
  }

  // Match not found or not caused, continue.
  const hook = activityEndpoints[activitableTypeLower.value];
  if (hook) {
    // Specific hook found, use it.
    return hook({
      caused: props.caused,
      params: params.value,
      id: activitableId.value,
    });
  }

  // Inject fallback filters where applicable.
  if (activitableTypeLower.value && activitableId.value) {
    params.value.filter = {
      subject_type: props.activitableType,
      subject_id: activitableId.value,
    };
  }

  // Use the general endpoint.
  return useActivities({
    caused: props.caused,
    params: params.value,
  });
};

const { data: activity, isLoading } = useActivityHook();

</script>
