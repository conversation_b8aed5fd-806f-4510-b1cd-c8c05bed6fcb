<template>
  <div>
    <VPageHeader title="View Activity" description="Overview of selected activity." />
    <VContainer class="py-3">
      <TransitionFade>
        <div v-if="isLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>
        <div v-else-if="activity" class="grid gap-3 lg:grid-cols-5 items-start">
          <ViewPanel class="col-span-5 xl:col-span-4 order-2 xl:order-1">
            <VCardHeader>Activity Record</VCardHeader>
            <PanelDefaultField
              label="Event"
              :value="ucfirst(activity.description)"
              help="The event that occurred."
            />
            <PanelDefaultField
              label="Subject"
              :value="activity.subject_type"
              help="What is the focus of the event?"
            />
            <PanelEntityField
              label="Subject Reference"
              :value="subjectReferenceValue"
              :entityType="activity?.subject_type"
              :entityId="activity?.subject?.locked_id"
              allowCopy
            />
            <PanelDateTimeField
              label="Date Occurred"
              :value="activity.created_at"
            />
            <PanelDefaultField
              label="Related Type"
              :value="activity.related_type"
              help="Whom or what is related to the event if any?"
            />
            <PanelEntityField
              label="Related Reference"
              :value="relatedReferenceValue"
              :entityType="activity?.related_type"
              :entityId="activity?.related?.locked_id"
              allowCopy
            />
            <PanelDefaultField
              label="Causer Type"
              :value="activity?.causer_type"
              help="Who or what caused the event?"
            />
            <PanelEntityField
              label="Causer Reference"
              :value="causerReferenceValue"
              :entityType="activity?.causer_type"
              :entityId="activity?.causer?.locked_id"
              allowCopy
            />
            <PanelDefaultField
              label="Properties"
              :value="activity?.properties ? JSON.stringify(activity.properties) : null"
              help="The properties used for this event (What values were included or changed)."
            >
              <template #default v-if="activity?.properties">
                <ActivityField :value="activity.properties" />
              </template>
            </PanelDefaultField>
          </ViewPanel>

          <VCard class="col-span-5 xl:col-span-1 order-1 xl:order-2">
            <VCardHeader>Actions</VCardHeader>
            <VCardBody class="flex gap-3 justify-center flex-wrap xl:flex-col">
              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'activity.index' }"
                centered
              >All Activity
                <template #start>
                  <BIconListUl />
                </template>
              </VButton>
              
              <VButton
                v-if="previousRoute"
                as="router-link"
                intent="danger"
                centered
                :to="previousRoute ? { name: previousRoute.name, params: previousRoute.params } : { name: 'index' }"
              >
                <template #start>
                  <BIconArrowReturnLeft />
                </template>
                Return to {{ previousRoute.meta.title }}
              </VButton>
            </VCardBody>
          </VCard>
        </div>
        <template v-else>
          <VEmptyState
            title="Activity Not Found"
            description="The requested activity was not found."
            :contact="true"
          />
        </template>
      </TransitionFade>
    </VContainer>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BIconListUl from '~icons/bi/list-ul';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import ViewPanel from '@/components/ui/panels/ViewPanel.vue';
import PanelDefaultField from '@/components/ui/panels/fields/ViewPanel/PanelDefaultField.vue';
import PanelDateTimeField from '@/components/ui/panels/fields/ViewPanel/PanelDateTimeField.vue';
import ActivityField from '@/components/ui/fields/ActivityField.vue';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';

import { useRouter } from 'vue-router';
import { useActivity } from '@/modules/activity/api/get-activity';
import { ucfirst } from '@/util/textUtils';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const router = useRouter();

const { data: activity, isLoading } = useActivity({
  activityId: props.id,
});

const relatedReferenceValue = computed(() => {
  return activity.value.related?.full_name || activity.value.related?.name || activity.value.related?.title || null;
});

const subjectReferenceValue = computed(() => {
  return activity.value.subject?.full_name || activity.value.subject?.name || activity.value.subject?.title || null;
});

const causerReferenceValue = computed(() => {
  return activity.value.causer?.full_name || activity.value.causer?.name || activity.value.causer?.title || null;
});

// Get the user's previous route using useRouter().
const previousRoute = typeof router.options.history.state.back === 'string'
  ? router.resolve(router.options.history.state.back)
  : null;

</script>
