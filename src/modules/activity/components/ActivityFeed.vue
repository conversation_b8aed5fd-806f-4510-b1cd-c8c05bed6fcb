<template>
  <VCard>
    <VCardHeader class="flex items-center justify-between bg-primary-500">
      <h2 class="flex items-center gap-2">
        <BIconActivity />
        {{ title }}
      </h2>
      <div class="grid grid-flow-col items-stretch space-x-2">
        <VTooltip
          v-if="activities?.data?.length > 4"
          :content="expand ? 'Collapse' : 'Expand'"
        >
          <VButton
            size="sm"
            intent="secondary"
            @click="expand = !expand"
            class="h-full"
          >
            <component
              :is="!expand ? BIconArrowsExpand : BIconArrowsCollapse"
              class="inline-flex w-4 align-middle"
            />
          </VButton>
        </VTooltip>
      </div>
    </VCardHeader>
    <VCardBody class="!p-0">
      <TransitionFade>
        <VSpinner v-if="isLoading" size="lg" class="text-center p-3" />
        <TransitionGroup
          name="staggered-fade"
          tag="ul"
          v-else-if="activities?.data?.length"
          role="list"
          class="scroll relative overflow-y-auto transition-[max-height] p-1.5 scrollbar-thin"
          :style="{ maxHeight: expand ? `${maxExpandedHeight}px` : `${maxCollapsedHeight}px` }"
        >
          <li v-for="(activity, index) in activities.data" :key="activity.locked_id">
            <ActivityFeedItem
              :activity="activity"
              :is-last="index === activities.data.length - 1"
              :index="index"
            />
          </li>
        </TransitionGroup>
        <div v-else class="px-2 py-4 sm:p-6 text-center text-sm text-platinum-600 dark:text-midnight-200 italic">
          No activity to display.
        </div>
      </TransitionFade>
    </VCardBody>
    <VCardFooter>
      <VButton
        as="router-link"
        :to="activityViewAllRoute"
        centered
        class="w-full"
        intent="primary"
      >
        <template #start>
          <BIconSearch />
        </template>
        View All
      </VButton>
    </VCardFooter>
  </VCard>
</template>

<script setup>
import { ref, computed } from 'vue';
import BIconActivity from '~icons/bi/activity';
import BIconArrowsCollapse from '~icons/bi/arrows-collapse';
import BIconArrowsExpand from '~icons/bi/arrows-expand';
import BIconSearch from '~icons/bi/search';
import ActivityFeedItem from '@/modules/activity/components/ActivityFeedItem.vue';

import { useUserActivities } from '@/modules/users/api/get-user-activities';
import { useCompanyUserActivities } from '@/modules/companies/api/get-company-user-activities'; // Activities events from linked users.
import { useFilterParams } from '@/hooks/query';
import { useAuth } from '@/store';

// Props
const props = defineProps({
  activitableType: {
    type: String,
    required: true,
    validator: (value) => {
      return ['user', 'company', 'customer'].includes(value.toLowerCase());
    },
  },
  activitableId: {
    type: String,
    required: false,
  },
  self: {
    // Generally only used in the case of user.
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: 'Recent Activity',
  },
  maxCollapsedHeight: {
    type: Number,
    default: 400,
  },
  maxExpandedHeight: {
    type: Number,
    default: 1000,
  },
});

const expand = ref(false);

// Lowercase the activitable type.
const activitableTypeLower = computed(() => {
  return props?.activitableType?.toLowerCase();
});

// Return the activitable ID based on the self prop and activitableType.
const activitableId = computed(() => {
  if (!props?.self && !props?.activitableId) {
    return null;
  }
  if (props?.self && props?.activitableType.toLowerCase() === 'user') {
    const auth = useAuth(); // Only needed if self is true.
    return auth.user.locked_id;
  }
  return props.activitableId;
});

// Get the filter params.
const { params } = useFilterParams();

// Default sort is newest first.
params.value.sort = '-created_at';
params.value.per_page = 10;

// Dedicated activity endpoints.
const activityEndpoints = {
  'user': useUserActivities,  
  'company': useCompanyUserActivities,
  // 'customer': useCustomerActivities,
};

// Only intended to be used for specific endpoints. Validation in place.
const useActivityHook = () => {
  const hook = activityEndpoints[activitableTypeLower.value];
  if (hook) {
    // Specific hook found, use it.
    return hook({
      caused: true,
      params: params.value,
      id: activitableId.value,
    });
  }

  return [];
};

const { data: activities, isLoading } = useActivityHook();

const activityViewAllRoute = computed(() => {
  if (activitableTypeLower.value === 'user' && props?.self) {
    return { name: 'profile.activity-caused' }
  } else if (activitableTypeLower.value === 'user' && !props?.self) {
    return { name: 'users.activity-caused', params: { id: activitableId.value } };
  } else if (activitableTypeLower.value === 'company') {
    return { name: 'companies.user-activity', params: { id: activitableId.value } };
  } else if (activitableTypeLower.value === 'customer') {
    return { name: 'customers.activity', params: { id: activitableId.value } };
  }

  return { name: 'activity.index' }; // fallback.
});

</script>
