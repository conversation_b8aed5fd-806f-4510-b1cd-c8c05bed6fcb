<template>
  <Table
    v-bind="$attrs"
    :columns="columns"
    :default-sort="[{
      id: 'created_at',
      desc: true,
    }]"
    paginated
    sortable
    searchable
    controlled
  />
</template>

<script setup>
import { h, ref } from 'vue';

import Table from '@/components/ui/tables/Table.vue';
import { humanizeTime } from '@/util/dateUtils';
import { ucfirst, truncate } from '@/util/textUtils';

import ActionCell from './partials/ActivityTableActionCell.vue';
import EntityLink from '@/components/ui/links/EntityLink.vue';

const props = defineProps({
  management: {
    type: Boolean,
    default: false,
  },
});

const columns = ref([
  {
    accessorKey: 'created_at',
    header: 'Date & Time',
    cell: (info) => humanizeTime(info.getValue()),
  },
  {
    accessorKey: 'description',
    header: 'Event',
    cell: (info) => {
      const description = info.getValue() || '';
      const capitalized = ucfirst(description);
      const subjectType = info.row.original?.subject_type || '';
      const checkMatches = ['created', 'updated', 'deleted'];
      if (checkMatches.includes(description.toLowerCase())) {
        return [capitalized, subjectType].filter(Boolean).join(' ');
      } else {
        return description;
      }
    },
  },
  {
    accessorKey: 'related_id',
    header: 'Related To',
    cell: (info) => {
      const original = info.row.original || {};
      const related = original.related || {};

      const name =
        related.name ||
        related.full_name ||
        related.title ||
        original.related_type || null;

      return h(EntityLink, {
        value: truncate(name),
        entityType: original.related_type || null,
        entityId: original.related?.locked_id || null,
        undefinedText: 'No Related Data Available',
        showAvatar: true,
        avatarSize: 'sm',
        showEntityType: true,
      });
    }
  },
  {
    accessorKey: 'causer_id',
    header: 'Causer',
    cell: (info) => {
      const original = info.row.original || {};
      const causer = original.causer || {};

      const name =
        causer.name ||
        causer.full_name ||
        original.causer_type;

      return h(EntityLink, {
        value: truncate(name),
        entityType: original.causer_type,
        entityId: original.causer?.locked_id,
        undefinedText: 'No Causer Data Available',
        showAvatar: true,
        avatarSize: 'sm',
      });
    }
  },
  {
    id: 'actions',
    header: () => h('span', { class: 'w-full flex justify-end' }),
    cell: (info) => h(ActionCell, { row: info.row.original, management: props.management }),
    enableSorting: false,
    enableResizing: false,
    size: 50,
  },
]);
</script>
