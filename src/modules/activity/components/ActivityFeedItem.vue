<template>
  <div class="p-1.5 relative transition-all duration-500 ease-in-out" :class="{ 'opacity-0 translate-y-5': !isVisible }">
    <span
      v-if="!isLast"
      class="absolute left-[2.375rem] top-[3.25rem] -ml-px h-full w-0.5 bg-primary-200 dark:bg-primary-500/50 z-30"
      aria-hidden="true"
    />
    <router-link
      :to="{
        name: 'activity.view',
        params: { id: activity.locked_id },
      }"
      class="block group cursor-pointer focus-within:ring-2 focus-within:ring-primary-300 dark:focus-within:ring-primary-600 rounded"
    >
      <div class="relative rounded p-3 transition group-hover:bg-primary-100 dark:group-hover:bg-midnight-300 block">
        <div class="flex gap-4">
          <div
            v-if="activity.user || activityTypeIcon"
            class="relative z-40"
            :class="{ '-mt-0.5': activity?.causer?.locked_id }"
          >
            <div v-if="activityTypeIcon" class="flex w-10 items-center justify-center">
              <div class="flex h-10 w-10 items-center justify-center rounded-full bg-primary-500 p-1.5">
                <component :is="activityTypeIcon" class="text-platinum-50 dark:text-midnight-500" />
              </div>
            </div>
            <span v-if="activity?.causer?.locked_id" class="absolute top-6 -right-1.5 rounded-full">
              <!-- <component :is="activityTypeIcon" class="h-3 w-3 text-white" aria-hidden="true" /> -->
              <VAvatar
                src="https://picsum.photos/100"
                size="xxs"
                :name="causerName"
                :avatarable="activity?.causer_type.toLowerCase()"
                :avatarable-id="activity?.causer?.locked_id"
                class="rounded-full ring ring-platinum-50 dark:ring-midnight-500 group-hover:ring-primary-100 dark:group-hover:ring-midnight-300 transition"
              />
            </span>
          </div>

          <div class="min-w-0 flex-1">
            <div>
              <div class="text-sm">
                <div class="flex justify-between">
                  <span class="grow text-base text-primary-500 font-semibold">{{ activityTitle }}</span>
                  <span class="min-w-fit">
                    <VTooltip :content="formattedDate(activity.created_at)">
                      <span class="ml-2 cursor-pointer text-xxs text-platinum-950/50 dark:text-platinum-50/30 hover:text-platinum-950/70 dark:hover:text-platinum-50/50">
                        {{ timeAgo(activity.created_at) }}
                      </span>
                    </VTooltip>
                  </span>
                </div>
                <p
                  class="mt-1 text-sm text-platinum-950 dark:text-midnight-50 [&_span]:font-semibold [&_span]:text-platinum-900 dark:[&_span]:text-midnight-100"
                  v-html="activityDescription"
                ></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </router-link>
  </div>
</template>

<script setup>
import { timeAgo, formattedDate } from '@/util/dateUtils';
import { ref, onMounted } from 'vue';
// Fallback icon
import BIconStickyFill from '~icons/bi/sticky-fill';
// User icons
import BIconPersonFillAdd from '~icons/bi/person-fill-add';
import BIconPersonFillCheck from '~icons/bi/person-fill-check';
import BIconPersonFillX from '~icons/bi/person-fill-x';
// Company icons
import BIconBuildingFillAdd from '~icons/bi/building-fill-add';
import BIconBuildingFillCheck from '~icons/bi/building-fill-check';
import BIconBuildingFillX from '~icons/bi/building-fill-x';
// Customer icons
import BIconPersonAdd from '~icons/bi/person-add';
import BIconPersonCheck from '~icons/bi/person-check';
import BIconPersonX from '~icons/bi/person-x';
// Announcement icons
import BIconMegaphoneFill from '~icons/bi/megaphone-fill';
// Contact icons
import BIconPersonVcardFill from '~icons/bi/person-vcard-fill';
// Comment icons
import BIconChatQuoteFill from '~icons/bi/chat-quote-fill';
// Address icons
import BIconJournalPlus from '~icons/bi/journal-plus';
import BIconJournalCheck from '~icons/bi/journal-check';
import BIconJournalX from '~icons/bi/journal-x';

import { computed } from 'vue';
import { ucfirst, truncate } from '@/util/textUtils';

const props = defineProps({
  activity: {
    type: Object,
    required: true,
  },
  isLast: {
    type: Boolean,
    default: false,
  },
  index: {
    type: Number,
    default: 0,
  },
});

const isVisible = ref(false);

onMounted(() => {
  setTimeout(() => {
    isVisible.value = true;
  }, 100 * props.index); // Increase delay based on index
});

// Determine which icon to show based on activity type
const activityTypeIcon = computed(() => {
  const action = props.activity.description.toLowerCase(); // Will typically be: created, updated or deleted.
  const subjectType = props.activity.subject_type.toLowerCase();
  switch (subjectType) {
    case 'user':
      if (action === 'created') {
        return BIconPersonFillAdd;
      } else if (action === 'deleted') {
        return BIconPersonFillX;
      }
      return BIconPersonFillCheck; // Includes updated.
    case 'company':
      if (action === 'created') {
        return BIconBuildingFillAdd;
      } else if (action === 'deleted') {
        return BIconBuildingFillX;
      }
      return BIconBuildingFillCheck;
    case 'customer':
      if (action === 'created') {
        return BIconPersonAdd;
      } else if (action === 'deleted') {
        return BIconPersonX;
      }
      return BIconPersonCheck;
    case 'announcement':
      return BIconMegaphoneFill;
    case 'comment':
      return BIconChatQuoteFill;
    case 'contact':
      return BIconPersonVcardFill;
    case 'address':
      if (action === 'created') {
        return BIconJournalPlus;
      } else if (action === 'updated') {
        return BIconJournalCheck;
      } else if (action === 'deleted') {
        return BIconJournalX;
      }
    default:
      return BIconStickyFill;
  }
});

const activityTitle = computed(() => {
  const {
    subject_type: subjectType,
    subject,
    description,
    related_type: relatedType,
    causer_type: causerType,
    related,
    causer,
  } = props.activity;
  const isSelfAction = subjectType === causerType && subject?.locked_id === causer.locked_id;
  const selfSuffix = isSelfAction ? ' for Self' : '';
  
  if (['created', 'updated', 'deleted'].includes(description)) {
    return `${ucfirst(description)} ${subjectType} ${selfSuffix}`;
  }

  return `${causerType} Event Occurred`;
});

const relatedValue = computed(() => {
  const relatedObj = props.activity?.related;
  const relatedType = props.activity?.related_type;
  if (relatedObj && relatedType) {
    // Attempt to return a relevant value.
    const value = relatedObj?.full_name || relatedObj?.name || relatedObj?.title || null;
    if (value) {
      return truncate(value, 30);
    }
  }
  return null;
})

const activityDescription = computed(() => {
  const {
    subject_type: subjectType,
    subject,
    related_type: relatedType,
    causer_type: causerType,
    related,
    causer,
  } = props.activity;

  const action = props.activity.description;
  const isSelfAction = subjectType === causerType && subject?.locked_id === causer?.locked_id;

  let relatedString = ''
  if (related && related.locked_id !== causer?.locked_id) {
    if (relatedValue.value) {
      relatedString = ` in relation to the ${relatedType.toLowerCase()} <span>${relatedValue.value}</span>`;
    } else {
      relatedString = ` in relation to a ${relatedType.toLowerCase()}`;
    }
  } else if (related && related.locked_id === causer?.locked_id) {
    if (action === 'created') {
      relatedString = ` for themselves`;
    } else {
      relatedString = ` in relation to themselves`;
    }
  }
  

  if (isSelfAction) {
    if (action === 'created') {
      return `<span>${causerName.value}</span> added a contact for themselves.`;
    } else if (action === 'updated') {
      const attributesCount = Object.keys(props.activity.properties.attributes).length;
      return `<span>${causerName.value}</span> updated their <span>${subjectType.toLowerCase()}</span>, changing <span>${attributesCount}</span> values in total.`;
    } else if (action === 'deleted') {
      return `<span>${causerName.value}</span> removed a <span>${subjectType.toLowerCase()}</span> belonging to them.`;
    }
  } else {
    if (action === 'created') {
      return `<span>${causerName.value}</span> added a <span>${subjectType.toLowerCase()}</span>${relatedString}.`;
    } else if (action === 'updated') {
      const attributesCount = Object.keys(props.activity.properties.attributes).length;
      return `<span>${causerName.value}</span> updated a <span>${subjectType.toLowerCase()}</span>${relatedString}, changing <span>${attributesCount}</span> values in total.`;
    } else if (action === 'deleted') {
      return `<span>${causerName.value}</span> removed a <span>${subjectType.toLowerCase()}</span>.`;
    }
  }
  return 'An action was taken!';
});

const causerName = computed(() => {
  return props.activity?.causer?.full_name || props.activity?.causer?.name || props.activity?.causer?.title || null;
});

</script>