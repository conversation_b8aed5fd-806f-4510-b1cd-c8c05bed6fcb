<template>
  <div 
    v-if="showActionTrigger"
    class="flex items-center justify-end space-x-1.5"
  >
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <VButton intent="table">
          <BIconThreeDots class="h-5 w-5" />
        </VButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          v-if="canView"
          as-child
        >
          <RouterLink
            :to="{
              name: 'activity.view',
              params: { id: row.locked_id },
            }"
          >
            <BIconSearch />View
          </RouterLink>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<script setup lang="ts">
import type { Activity } from '@/types/api';
import BIconSearch from '~icons/bi/search';
import BIconThreeDots from '~icons/bi/three-dots';
import { useAuth } from '@/store/auth';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';

const props = defineProps<{
  row: Activity;
}>();

const auth = useAuth();

const canView = auth.can(['activity.view', 'activity.view.any', 'activity.view.self']);
const showActionTrigger = canView; // Add more conditions if adding more actions.

</script>