import { auth } from '@/router/middleware';
import UserLayout from '@/layouts/UserLayout.vue';

const routes = [
  {
    path: '/activity',
    component: UserLayout,
    meta: {
      middleware: auth,
    },
    children: [
      {
        path: '',
        name: 'activity.index',
        component: () => import('./pages/ActivityIndex.vue'),
        meta: {
          title: 'Activity',
          headerTitle: 'Activity',
          headerDescription: 'Overview of activity records.',
          breadcrumbs: [
            { label: 'Activity', name: 'activity.index' },
          ],
        },
      },
      {
        path: '/activity/:id/view',
        name: 'activity.view',
        component: () => import('./pages/ActivityView.vue'),
        props: true,
        meta: {
          title: 'View Activity',
          headerTitle: 'View Activity',
          headerDescription: 'Overview of a selected activity.',
          breadcrumbs: [
            { label: 'Activity', name: 'activity.index' },
            { label: 'View', name: 'activity.view' },
          ],
        },
      },
    ],
  },
];

export default routes;
