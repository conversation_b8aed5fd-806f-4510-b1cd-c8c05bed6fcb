import { useQuery, queryOptions } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Activity } from '@/types/api/activity';

export const getActivity = ({
  activityId,
}: {
  activityId: string;
}): Promise<Activity> => {
  return Reshape.http()
    .get(`/api/activity/${activityId}`)
    .then(({ data }) => data);
};

export const getActivityQueryOptions = (activityId: string) => {
  return queryOptions({
    queryKey: ['activity', activityId],
    queryFn: () => getActivity({ activityId }),
  });
};

type UseActivityOptions = {
  activityId: string;
  queryConfig?: QueryConfig<typeof getActivityQueryOptions>;
};

export const useActivity = ({ activityId, queryConfig }: UseActivityOptions) => {
  return useQuery({
    ...getActivityQueryOptions(activityId),
    ...queryConfig,
  });
}; 