import { queryOptions, useQuery } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Activity, Meta } from '@/types/api';

export const getActivities = (
  params,
  signal
): Promise<{ data: Activity[]; meta: Meta }> => {
  return Reshape.http().get(
    `/api/activity`,
    {
      params,
      signal,
    }
  );
};

export const getActivitiesQueryOptions = ({
  params,
}: {
  params?: unknown;
} = {}) => {
  return queryOptions({
    queryKey: params ? ['activities', params] : ['activities'],
    queryFn: ({ signal }) => getActivities(params, signal),
  });
};

type UseActivitiesOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getActivitiesQueryOptions>;
};

export const useActivities = ({ queryConfig, params }: UseActivitiesOptions) => {
  return useQuery({
    ...getActivitiesQueryOptions({ params }),
    ...queryConfig,
  });
}; 