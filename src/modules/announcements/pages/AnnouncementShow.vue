<template>
  <div>
    <VPageHeader :showTitle="false" :showDescription="false" />
    <VContainer class="py-3">
      <TransitionFade>
        <div v-if="isLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>

        <div v-else-if="announcement">
          <div class="relative w-full h-80 overflow-hidden rounded-lg shadow flex items-end p-3 md:p-6">
            <div class="absolute inset-0 bg-gradient-to-t from-platinum-50 dark:from-midnight-900 to-transparent">
              <VImage
                :src="announcement?.headerImage"
                class="w-full h-full object-cover -z-10"
                :lazyLoad="false"
              />
            </div>
            <div class="z-20 text-left w-full">
              <AnnouncementHeader :announcement="announcement">
                <template #title>{{ announcement.title || 'Announcement' }}</template>
                <template #actions>
                  <DropdownMenu v-if="showHeaderActions">
                    <DropdownMenuTrigger as-child>
                      <VButton intent="secondary" size="xs" class="rounded-full p-1.5">
                        <BIconThreeDots />
                      </VButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem v-if="auth.can('announcements.view')" as-child>
                        <RouterLink
                          :to="{
                            name: auth.can('announcements.view.any') ? 'settings.announcements.view' : 'announcements.view',
                            params: { id: announcement.locked_id },
                          }"
                          class="w-full"
                        >
                          <BIconSearch />View Record
                        </RouterLink>
                      </DropdownMenuItem>
                      <DropdownMenuItem v-if="auth.can('announcements.update')" as-child>
                        <RouterLink
                          :to="{
                            name: auth.can('announcements.update.any') ? 'settings.announcements.update' : 'announcements.update',
                            params: { id: announcement.locked_id },
                          }"
                          class="w-full"
                        >
                          <BIconPencilSquare />Edit
                        </RouterLink>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </template>
              </AnnouncementHeader>
            </div>
          </div>

          <div class="mt-3 prose prose-lg max-w-none bg-platinum-50 dark:bg-midnight-600 p-3 md:px-6 md:py-4 rounded-lg shadow">
            <MarkdownField class="text-platinum-950 dark:text-midnight-50" :value="announcement.body" />
          </div>
          
          <div class="mt-6 border-t pt-3">
            <div class="flex items-center justify-between mb-3">
              <div>
                <h2 class="text-2xl font-semibold text-primary-500">Comments
                  <VTooltip content="Comments are ordered by rating then date created.">
                    <BIconInfoCircleFill class="inline w-4 ml-0.5 " />
                  </VTooltip>
                </h2>
              </div>
              <span
                class="text-xs text-platinum-600 dark:text-midnight-50"
              >Total Comments: {{ totalComments }}</span>
            </div>
            <CommentPoster
              commentable-type="Announcement"
              :commentable-id="announcement.locked_id"
              class="mb-3"
            />
            <CommentFeed
              commentable-type="Announcement"
              :commentable-id="announcement.locked_id"
              :can-react="true"
              :can-rate="true"
              @total="(total) => totalComments = total"
            >
              <template #empty>
                <span class="text-sm text-midnight-100">Comments will appear here. Join the conversation!</span>
              </template>
            </CommentFeed>
          </div>
        </div>
      </TransitionFade>
    </VContainer>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useAnnouncement } from '@/modules/announcements/api/get-announcement';
import CommentFeed from '@/modules/comments/components/Feed/CommentFeed.vue';
import CommentPoster from '@/modules/comments/components/Feed/CommentPoster.vue';
import MarkdownField from '@/components/ui/fields/MarkdownField.vue';
import BIconInfoCircleFill from '~icons/bi/info-circle-fill';
import BIconSearch from '~icons/bi/search';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconThreeDots from '~icons/bi/three-dots';
import { useAuth } from '@/store';
import AnnouncementHeader from '@/modules/announcements/components/AnnouncementHeader.vue';

const auth = useAuth();

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const totalComments = ref(0);

const { data: announcement, isLoading } = useAnnouncement({
  announcementId: props.id
});

const showHeaderActions = computed(() => {
  return auth.can('announcements.view')
    || auth.can('announcements.view.any')
    || auth.can('announcements.update')
    || auth.can('announcements.update.any');
});

</script>
