<template>
  <div>
    <VPageHeader>
      <VButton
        v-if="hasPermissionToCreateAnnouncement"
        as="router-link"
        :to="{ name: props.management ? 'settings.announcements.create' : 'announcements.create' }"
        intent="secondary"
      >Add Announcement
        <template #start>
          <BIconPlusLg />
        </template>
      </VButton>
    </VPageHeader>

    <VContainer class="py-2 lg:py-6">
      <AnnouncementsTable
        title="Announcements"
        :records="announcements?.data"
        :total-records="announcements?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
        :management="props.management"
      />
    </VContainer>
  </div>
</template>

<script setup>
import AnnouncementsTable from '@/modules/announcements/components/AnnouncementsTable.vue';
import BIconPlusLg from '~icons/bi/plus-lg';
import { useAnnouncements } from '@/modules/announcements/api/get-announcements';
import { useFilterParams } from '@/hooks/query';
import { ref } from 'vue';
import { useAuth } from '@/store';

const auth = useAuth();

const props = defineProps({
  management: {
    type: Boolean,
    default: false,
  },
});

const hasPermissionToCreateAnnouncement = ref(auth.can('announcements.create') || auth.can('announcements.create.any'));

const { params, search, sort, paginate } = useFilterParams();

const { data: announcements, isLoading } = useAnnouncements({
  params: params.value,
  management: props.management,
});
</script>
