<template>
  <div>
    <VPageHeader>
      <template #actions>
        <VButton
          type="submit"
          form="announcement-form"
          intent="secondary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Confirm
        </VButton>
        <VButton
          intent="danger"
          @click="confirmModalOpen = true"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <TransitionFade>
        <AnnouncementForm
          v-if="!isAnnouncementLoading"
          :announcement="announcement"
          @submit="submit"
        />
        <div v-else class="flex w-full justify-center p-6">
          <VSpinner size="xl" />
        </div>
      </TransitionFade>
      
      <div class="flex w-full justify-start lg:justify-end space-x-3">
        <VButton
          type="submit"
          form="announcement-form"
          intent="primary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Confirm
        </VButton>
        <VButton
          intent="danger"
          @click="confirmModalOpen = true"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>

        <VConfirmModal
          @confirmed="router.push({ name: props.management ? 'settings.announcements.index' : 'announcements.index' })"
          v-model:open="confirmModalOpen"
        />
      </div>
    </VContainer>
  </div>
</template>

<script setup>
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconCheckLg from '~icons/bi/check-lg';
import AnnouncementForm from '@/modules/announcements/components/AnnouncementForm.vue';
import { useAnnouncement } from '@/modules/announcements/api/get-announcement';
import { useUpdateAnnouncement } from '@/modules/announcements/api/put-announcement';
import { ref } from 'vue';

const confirmModalOpen = ref(false);

const updateAnnouncementMutation = useUpdateAnnouncement({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Announcement Successfully Updated',
          message: `The announcement <b>${result.data.title}</b> was successfully updated.`,
        },
        'success'
      );
      router.push({
        name: props.management ? 'settings.announcements.view' : 'announcements.view',
        params: { id: result.data.locked_id }
      });
    },
  },
});

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  management: {
    type: Boolean,
    default: false,
  },
});

const { data: announcement, isLoading: isAnnouncementLoading } = useAnnouncement({
  announcementId: props.id,
  management: props.management
});

const submit = (form) => {
  form.submit((data) => updateAnnouncementMutation.mutateAsync({
    announcementId: props.id,
    management: props.management,
    data
  }));
};
</script>
