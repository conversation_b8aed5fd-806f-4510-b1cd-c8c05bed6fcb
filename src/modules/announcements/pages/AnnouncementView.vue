<template>
  <div>
    <VPageHeader />
    <VContainer class="my-3">
      <TransitionFade>
        <div v-if="isLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>
        <div v-else-if="announcement" class="grid gap-3 lg:grid-cols-5">
          <ViewPanel class="lg:col-span-4 order-2 xl:order-1">
            <PanelDefaultField
              label="Title"
              :value="announcement.title"
              allow-copy
            />
            <PanelDefaultField
              label="Body"
              :value="announcement.body"
              allow-copy
              markdown
            >
            </PanelDefaultField>
            <PanelDefaultField
              label="Announcable Type"
              :value="announcement.announcable_type"
              help="The type of entity this announcement is related to."
            />
            <PanelEntityField
              label="Announcable Reference"
              :value="announceableReferenceValue"
              :entityType="announcement?.announcable_type"
              :entityId="announcement?.announcable?.locked_id"
              help="The entity this announcement is related to, if any."
              allowCopy
            />
            <PanelDefaultField
              label="Status"
              :value="announcement?.status?.name"
            >
              <AnnouncementStatusBadge :status="announcement?.status" />
            </PanelDefaultField>
            <PanelDateTimeField
              label="Start Date"
              :value="announcement?.started_at"
              help="The date and time the announcement will be visible."
            />
            <PanelDateTimeField
              label="End Date"
              :value="announcement?.ended_at"
              help="The date and time the announcement will no longer be visible."
            />
            <PanelEntityField
              label="Created By"
              :value="announcement?.creator?.full_name"
              entity-type="User"
              :entity-id="announcement?.creator?.locked_id"
              show-avatar
              allow-copy
            />
            <PanelDateTimeField label="Date Created" :value="announcement?.created_at" />
            <PanelDateTimeField label="Date Updated" :value="announcement?.updated_at" />
          </ViewPanel>

          <VCard class="lg:col-span-1 order-1 xl:order-2">
            <VCardHeader>Actions</VCardHeader>
            <VCardBody class="flex gap-3 justify-center flex-wrap xl:flex-col">
              <!-- Button that returns the user to the referring page. -->
              <VButton
                v-if="previousRoute?.name"
                intent="danger"
                as="router-link"
                :to="{ name: previousRoute.name }"
              >Return to {{ previousRoute.meta.title }}
                <template #start>
                  <BIconArrowReturnLeft />
                </template>
              </VButton>

              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'announcements.show', params: { id: announcement.locked_id } }"
              >View Announcement
                <template #start>
                  <BIconSearch />
                </template>
              </VButton>
              
              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: management ? 'settings.announcements.index' : 'announcements.index' }"
              >View All Announcements
                <template #start>
                  <BIconListUl />
                </template>
              </VButton>
              
              <VButton
                v-if="management"
                intent="primary"
                as="router-link"
                :to="{ name: 'settings.announcements.activity', params: { id: announcement.locked_id } }"
              >View Activity
                <template #start>
                  <BIconActivity />
                </template>
              </VButton>

              <DropdownMenu v-if="canEdit || canDelete">
                <DropdownMenuTrigger as-child>
                  <VButton intent="danger">
                    <template #start>
                      <BIconPencilSquare />
                    </template>
                    <template #end>
                      <BIconChevronDown />
                    </template>Modify
                  </VButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem v-if="canEdit" as-child>
                    <RouterLink
                      :to="{
                        name: management ? 'settings.announcements.update' : 'announcements.update',
                        params: { id: announcement.locked_id },
                      }"
                      class="w-full"
                    >
                      <BIconPencilSquare />Edit
                    </RouterLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem v-if="canDelete" @click="openAnnouncementDeleteConfirmationDialog">
                    <BIconTrash />Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

            </VCardBody>
          </VCard>
        </div>
        <template v-else>
          <VEmptyState
            title="Announcement Not Found"
            description="The requested announcement was not found."
            :contact="true"
          />
        </template>
      </TransitionFade>
    </VContainer>

    <VConfirmModal
      v-if="canDelete"
      v-model:open="isAnnouncementDeleteConfirmationDialog"
      @confirmed="deleteAnnouncementMutation.mutate({
        announcementId: props.id
      })"
      title="Confirm Announcement Deletion"
      :description="`<p>You're about to delete the announcement titled: <b>${title}</b>.</p><p>Please confirm you wish to proceed.</p>`"
      :icon="BIconTrashFill"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconChevronDown from '~icons/bi/chevron-down';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconListUl from '~icons/bi/list-ul';
import BIconActivity from '~icons/bi/activity';
import BIconSearch from '~icons/bi/search';
import ViewPanel from '@/components/ui/panels/ViewPanel.vue';
import PanelDefaultField from '@/components/ui/panels/fields/ViewPanel/PanelDefaultField.vue';
import PanelDateTimeField from '@/components/ui/panels/fields/ViewPanel/PanelDateTimeField.vue';
import AnnouncementStatusBadge from '@/modules/announcements/components/AnnouncementStatusBadge.vue';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';
import { useDisclosure } from '@/hooks';
import { useRouter } from 'vue-router';
import { useDeleteAnnouncement } from '@/modules/announcements/api/delete-announcement';
import { useAnnouncement } from '@/modules/announcements/api/get-announcement';
import { useAuth } from '@/store';

const auth = useAuth();

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  management: {
    type: Boolean,
    default: false,
  },
});

const router = useRouter();
// Get the user's previous route using useRouter().
// If the current route is single level, don't bother.
const previousRoute = computed(() => {
  let resolved = null;
  if (
    typeof router.options.history.state.back === 'string'
  ) {
    resolved = router.resolve(router.options.history.state.back);
  }

  // We don't want it previous route is the index page (have a button already).
  if (['announcements.index', 'settings.announcements.index'].includes(resolved?.name)) return null;

  return resolved;
});

const deleteAnnouncementMutation = useDeleteAnnouncement({
  mutationConfig: {
    onSuccess: () => {
      Reshape.toast(
        {
          title: 'Announcement Deleted',
          message: `The requested announcement <b>${title.value}</b> was successfully deleted.`,
        },
        'success'
      );
      router.push({
        name: props.management ? 'settings.announcements.index' : 'announcements.index',
      });
    },
  },
});

const {
  isOpen: isAnnouncementDeleteConfirmationDialog,
  open: openAnnouncementDeleteConfirmationDialog
} = useDisclosure(false);



const { data: announcement, isLoading } = useAnnouncement({
  announcementId: props.id,
  management: true
});

const title = computed(() => {
  return announcement.value?.title ? announcement.value.title : 'Undefined';
});

const announceableReferenceValue = computed(() => {
  return announcement.value?.announcable?.full_name || announcement.value?.announcable?.name || announcement.value?.announcable?.title || null;
});

const canEdit = computed(() => {
  return props.management || auth.can('announcements.update.any') || auth.user?.locked_id === announcement.value?.creator?.locked_id;
});

const canDelete = computed(() => {
  return props.management || auth.can('announcements.delete.any');
});

</script>
