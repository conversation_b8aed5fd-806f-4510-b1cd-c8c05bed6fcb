<template>
  <div>
    <VPageHeader>
      <template #actions>
        <VButton
          type="submit"
          form="announcement-form"
          intent="secondary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>
        <VButton intent="danger" @click="confirmModalOpen = true">
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <AnnouncementForm @submit="submit" :management="management" />

      <div class="flex w-full justify-start lg:justify-end space-x-3">
        <VButton
          type="submit"
          form="announcement-form"
          intent="primary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>

        <VButton intent="danger" @click="confirmModalOpen = true">
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </div>
    </VContainer>

    <VConfirmModal
      @confirmed="router.push({ name: props.management ? 'settings.announcements.index' : 'announcements.index' })"
      v-model:open="confirmModalOpen"
    />
  </div>
</template>

<script setup>
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconPlusLg from '~icons/bi/plus-lg';
import AnnouncementForm from '@/modules/announcements/components/AnnouncementForm.vue';
import { ref } from 'vue';
import { useCreateAnnouncement } from '@/modules/announcements/api/post-announcement';

const props = defineProps({
  management: {
    type: Boolean,
    default: false,
  },
});

const confirmModalOpen = ref(false);

const createAnnouncementMutation = useCreateAnnouncement({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Announcement Successfully Created',
          message: `The announcement <b>${result.data?.title}</b> was successfully created.`,
        },
        'success'
      );
      router.push({
        name: props.management ? 'settings.announcements.view' : 'announcements.view',
        params: { id: result.data.locked_id }
      });
    },
  },
});

const submit = (form) => {
  form.submit((data) => createAnnouncementMutation.mutateAsync({
    data,
    management: props.management
  }));
};
</script>
