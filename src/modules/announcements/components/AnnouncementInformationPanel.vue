<template>
  <VInformationPanel class="rounded-md" :loading="isAnnouncementLoading">
    <VDefaultField label="Title" :value="announcement.title" />
    <VDefaultField label="Status" :value="announcement.status.name" />
    <VDefaultField label="Body" :value="announcement.body" class="sm:col-span-2" :limit="200" />
    <VDefaultField label="Start Date" :value="announcement.started_at" />
    <VDefaultField label="End Date" :value="announcement.ended_at" />
    <VTimeField label="Created At" :value="announcement.created_at" />
    <VTimeField label="Updated At" :value="announcement.updated_at" />
  </VInformationPanel>
</template>

<script setup>
import { useAnnouncement } from '@/modules/announcements/api/get-announcement';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const { 
  data: announcement,
  isLoading: isAnnouncementLoading,
} = useAnnouncement({ announcementId: props.id });

</script>
