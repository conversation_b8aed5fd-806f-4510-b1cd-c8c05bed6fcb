<template>
  <form id="announcement-form" class="space-y-4" @submit.prevent="submit">
    <FormContainer
      title="Announcement Details"
      description="Define the content of the announcement."
      :icon="BIconMegaphoneFill"
    >
      <template #body>
        <div class="grid grid-cols-6 gap-6">
          <VField
            label="Title"
            class="col-span-6"
            required
            :errors="form.errors.get('title')"
            help="The heading of the announcement."
          >
            <VInput
              v-model="form.title"
              type="text"
              placeholder="Enter Announcement Title"
            />
          </VField>

          <VField
            label="Description"
            class="col-span-6"
            :errors="form.errors.get('body')"
            help="The body of the announcement."
            required
          >
            
            <RichTextEditor
              v-model="form.body"
              placeholder="Enter Announcement Body"
              wrapperClass="max-h-96 overflow-y-auto"
            />
            
          </VField>
        </div>
      </template>
    </FormContainer>
    <FormContainer
      title="Announcement Scope"
      description="Define who this announcement should be shown to and when."
      :icon="BIconPersonWorkspace"
    >
      <template #body>
        <div class="grid grid-cols-6 gap-6">
          <VField
            v-if="management"
            class="col-span-6 sm:col-span-2 xl:col-span-1"
            label="Global Announcement"
            help="Whether this announcement is shown to all users or specific company users only."
            required
          >
            <VToggle v-model="globalAnnouncement" class="mt-3">
              <span class="text-platinum-800 dark:text-midnight-100">
                {{ globalAnnouncement ? 'Enabled' : 'Disabled' }}
              </span>
            </VToggle>
          </VField>
          
          <TransitionFade>
            <VField
              v-if="!globalAnnouncement"
              class="col-span-6 md:col-span-4 xl:col-span-2 2xl:col-span-2"
              :errors="form.errors.get('announcable_type')"
              label="Entity"
              :help="management ? 'Themes can be tied to specific entities, such as users or companies.' : 'Select who this announcement should be shown to.'"
            >
              <EntityCombobox
                v-model="form.announcable_type"
                value-prop="name"
                @update:modelValue="
                  () => {
                    form.announcable_id = '';
                  }
                "
              />
            </VField>
          </TransitionFade>

          <TransitionFade>
            <VField
              v-if="!globalAnnouncement"
              :label="form.announcable_type ? form.announcable_type + ' Reference' : 'Entity Reference'"
              class="col-span-6 xl:col-span-3 2xl:col-span-4"
              required
              :errors="form.announcable_type ? form.errors.get('announcable_id') : []"
              :help="
                form.announcable_type
                  ? `Select a ${form.announcable_type} reference this record should be tied to.`
                  : 'Based on the entity selected, select the record this contact should be tied to.'
              "
            >
              <TransitionFadeLeftToRight>
                <span
                  v-if="!form.announcable_type"
                  class="mt-2.5 inline-block select-none italic text-platinum-600 dark:text-midnight-200"
                >Select Entity to continue.</span>
                <UserCombobox v-else-if="form.announcable_type === 'User'" v-model="form.announcable_id" />
                <CompanyCombobox v-else-if="form.announcable_type === 'Company'" v-model="form.announcable_id" />
                <CustomerCombobox v-else-if="form.announcable_type === 'Customer'" v-model="form.announcable_id" />
                <span
                  v-else
                  class="mt-2 inline-block select-none italic text-platinum-500 dark:text-midnight-100"
                >Selection Invalid</span>
              </TransitionFadeLeftToRight>
            </VField>
          </TransitionFade>

        </div>
        <div class="grid grid-cols-6 gap-6">
          <VField
            class="col-span-6 sm:col-span-2 2xl:col-span-1"
            label="Status"
            help="Selecting any value other than active will prevent the announcement from being shown to users."
            required
          >
            <StatusCombobox
              v-model="form.status_id"
              statusable="Announcement"
              placeholder="Select a Status"
              :default-option="props.announcement ? null : 'Active'"
              required
            />
          </VField>
          <VField
            class="col-span-6 sm:col-span-4 2xl:col-span-2"
            label="Start Date"
            :errors="form.errors.get('started_at')"
            help="When this announcement should become visible to relevant users (default now)."
            required
          >
            <VDateTimeInput v-model="form.started_at" required />
          </VField>
          <VField
            class="col-span-6 sm:col-span-2 xl:col-span-1 xl:col-start-1 2xl:col-start-auto"
            label="Set End Date?"
            help="Does this announcement require an end date? Set to no to show indefinitely."
            required
          >
            <VToggle v-model="allowEndDate" class="mt-3">
              <span class="text-platinum-800 dark:text-midnight-100">
                {{ allowEndDate ? 'Yes' : 'No' }}
              </span>
            </VToggle>
          </VField>
          <TransitionFade>
            <VField
              v-if="allowEndDate"
              class="col-span-6 sm:col-span-4 xl:col-span-5 2xl:col-span-2"
              label="End Date"
              :errors="form.errors.get('ended_at')"
              help="When this announcement should be hidden if ever. Default disabled."
              required
            >
              <VDateTimeInput v-model="form.ended_at" required />
            </VField>
          </TransitionFade>
        </div>
      </template>
    </FormContainer>
  </form>
</template>

<script setup>
import { ref, watch } from 'vue';
import FormContainer from '@/components/ui/forms/FormContainer.vue';
import { useForm } from '@/hooks/form';
import BIconPersonWorkspace from '~icons/bi/person-workspace';
import BIconMegaphoneFill from '~icons/bi/megaphone-fill';
import EntityCombobox from '@/components/ui/form/composables/EntityCombobox.vue';
import UserCombobox from '@/components/ui/form/composables/UserCombobox.vue';
import CompanyCombobox from '@/components/ui/form/composables/CompanyCombobox.vue';
import CustomerCombobox from '@/components/ui/form/composables/CustomerCombobox.vue';
import StatusCombobox from '@/components/ui/form/composables/StatusCombobox.vue';
import RichTextEditor from '@/components/ui/form/RichTextEditor.vue';

const props = defineProps({
  announcement: {
    type: Object,
    default: null,
  },
  management: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['submit']);

const submit = () => emits('submit', form.value);

const form = useForm({
  title: props.announcement?.title ?? '',
  body: props.announcement?.body ?? '',
  announcable_type: props.announcement?.announcable_type ?? '',
  announcable_id: props.announcement?.announcable?.locked_id ?? '',
  status_id: props.announcement?.status?.locked_id ?? '',
  started_at: props.announcement?.started_at ?? new Date()
    .toISOString()
    .replace('T', ' ')
    .replace('Z', '')
    .split('.')[0],
  ended_at: props.announcement?.ended_at ?? null,
});

const allowEndDate = ref(props.announcement?.ended_at ? true : false);

const globalAnnouncement = ref(props.management && (!props.announcement || (!props.announcement?.announcable_type && !props.announcement?.announcable_id) ? true : false));

watch(allowEndDate, (newVal) => {
  if (!newVal) {
    form.value.ended_at = null;
  } else {
    // Set default end date to one month from now.
    form.value.ended_at = new Date(
      new Date()
      .setMonth(
        new Date().getMonth() + 1
      ))
      .toISOString()
      .replace('T', ' ')
      .replace('Z', '')
      .split('.')[0];
  }
});

</script>
