<template>
  <h1 class="text-4xl xl:text-5xl font-bold text-primary-600 dark:text-primary-500 ">
    <slot name="title"></slot>
  </h1>
  <div class="space-y-1 sm:flex justify-between w-full items-end">
    <div class="text-sm text-platinum-950 dark:text-midnight-50 mt-2 lg:flex items-center gap-2">
      <span
        v-if="announcableValue && announcement?.announcable"
        class="flex items-center gap-2"
      >
        <span>Announcement for {{ announcementTypeLower }}: </span>
        <VEntityLink
            v-if="announcement?.announcable"
            :entity-type="announcement?.announcable_type"
            :entity-id="announcement?.announcable_id"
            :value="announcableValue"
            show-avatar
            avatar-size="xs"
        />
      </span>
      <span v-else class="flex items-center gap-2">
        <span>Global Announcement</span>
      </span>
      <span class="hidden lg:inline font-extrabold"> · </span>
      <span>Published <span>{{ humanizeTime(announcement.start_date) }}</span></span>
      <span v-if="announcement?.creator" class="flex items-center gap-2">
        <span class="hidden lg:inline font-extrabold"> · </span>
        <span>Posted by</span>
        <VEntityLink
          v-if="announcement?.creator"
          entity-type="User"
          :entity-id="announcement?.creator?.locked_id"
          :value="announcement?.creator?.full_name || 'Admin'"
          :show-avatar="mdAndLarger"
          avatar-size="xs"
        />
      </span>
    </div>
    <slot name="actions"></slot>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useBreakpoints, breakpointsTailwind } from '@vueuse/core';
import { humanizeTime } from '@/util/dateUtils';

const props = defineProps({
    announcement: {
        type: Object,
        required: true,
    },
});

const announcableValue = computed(() => {
    return props.announcement?.announcable?.first_name || props.announcement?.announcable?.name || false;
});

const announcementTypeLower = computed(() => {
    return props.announcement?.announcable_type?.toLowerCase();
});

const breakpoints = useBreakpoints(breakpointsTailwind);
const mdAndLarger = breakpoints.greaterOrEqual('md');

</script>
