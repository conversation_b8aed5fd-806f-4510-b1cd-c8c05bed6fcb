<template>
  <div class="flex items-center justify-end space-x-1.5">
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <VButton intent="table">
          <BIconThreeDots class="h-5 w-5" />
        </VButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem as-child>
          <RouterLink
            :to="{
              name: 'announcements.show',
              params: { id: row.locked_id },
            }"
          >
            <BIconSearch />View
          </RouterLink>
        </DropdownMenuItem>
        <DropdownMenuItem as-child>
          <RouterLink
            :to="{
              name: management ? 'settings.announcements.view' : 'announcements.view',
              params: { id: row.locked_id },
            }"
          >
            <BIconListUl />View Record
          </RouterLink>
        </DropdownMenuItem>
        <DropdownMenuItem
          v-if="management"
          as-child
        >
          <RouterLink
            :to="{
              name: 'settings.announcements.activity',
              params: { id: row.locked_id },
            }"
          >
            <BIconActivity />Activity
          </RouterLink>
        </DropdownMenuItem>
        <DropdownMenuGroup v-if="management">
          <DropdownMenuLabel>Modify</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem as-child>
            <RouterLink
              :to="{
                name: 'settings.announcements.update',
                params: { id: row.locked_id },
              }"
              class="w-full"
            >
              <BIconPencilSquare />Edit
            </RouterLink>
          </DropdownMenuItem>
          <DropdownMenuItem 
            v-if="props.management"
            @click="openAnnouncementDeleteConfirmationDialog"
          >
            <BIconTrash />Delete
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>

    <VConfirmModal
      v-if="props.management"
      v-model:open="isAnnouncementDeleteConfirmationDialog"
      @confirmed="deleteAnnouncementMutation.mutate({
        announcementId: props.row.locked_id
      })"
      title="Confirm Announcement Deletion"
      :description="`<p>You're about to delete the announcement titled: <b>${title}</b>.</p><p>Please confirm you wish to proceed.</p>`"
      :icon="BIconTrashFill"
    />
  </div>
</template>

<script setup lang="ts">
import BIconSearch from '~icons/bi/search';
import BIconThreeDots from '~icons/bi/three-dots';
import BIconListUl from '~icons/bi/list-ul';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconActivity from '~icons/bi/activity';
import { computed } from 'vue';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/shadcn/dropdown-menu';
import { useDisclosure } from '@/hooks';
import type { CellContext } from '@tanstack/vue-table';
import type { Announcement } from '@/types/api';
import { useDeleteAnnouncement } from '@/modules/announcements/api/delete-announcement';

export type ActionCellProps = {
  row: CellContext<Announcement, unknown>;
  management: boolean;
};

const title = computed(() => {
  return props.row.title ? props.row.title : 'Undefined';
});

const props = defineProps<ActionCellProps>();

const deleteAnnouncementMutation = useDeleteAnnouncement({
  mutationConfig: {
    onSuccess: () => {
      Reshape.toast(
        {
          title: 'Announcement Deleted',
          message: `The requested announcement <b>${title.value}</b> was successfully deleted.`,
        },
        'success'
      );
    },
  },
});

const {
  isOpen: isAnnouncementDeleteConfirmationDialog,
  open: openAnnouncementDeleteConfirmationDialog
} = useDisclosure(false);

</script>