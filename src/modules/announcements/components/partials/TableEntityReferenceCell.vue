<template>
  <!-- {{ row.announcable }} -->
  <VEntityLink
    v-if="row.announcable && row.announcable_type"
    :entity-type="row.announcable_type"
    :entity-id="row.announcable?.locked_id"
    :value="relatedReferenceValue"
    show-entity-type
    show-avatar
    avatar-size="sm"
  />
  <template v-else>
    <span class="italic text-platinum-600 dark:text-midnight-200">Global Announcement</span>
  </template>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  row: {
    type: Object,
    default: null,
  },
});

const relatedReferenceValue = computed(() => {
  return props.row.announcable?.full_name || props.row.announcable?.name || props.row.announcable?.title || null;
});
</script>