<template>
  <Table
    v-bind="$attrs"
    :columns="columns"
    :default-sort="[{
      id: 'start_date',
      desc: true,
    }]"
    paginated
    sortable
    searchable
    controlled
  />
</template>

<script setup>
import { computed, h } from 'vue';

import Table from '@/components/ui/tables/Table.vue';
import { humanizeTime } from '@/util/dateUtils';
import { stripHtmlTags } from '@/util/textUtils';

import TableActionCell from '@/modules/announcements/components/partials/TableActionCell.vue';
import AnnouncementStatusBadge from '@/modules/announcements/components/AnnouncementStatusBadge.vue';
import TableEntityReferenceCell from '@/modules/announcements/components/partials/TableEntityReferenceCell.vue';

const props = defineProps({
  management: {
    type: Boolean,
    default: false,
  },
});

const titleTruncateLength = props.management ? 20 : 30;
const bodyTruncateLength = props.management ? 30 : 50;

const columns = computed(() => {
  const baseColumns = [
    {
      accessorKey: 'title',
      header: 'Title',
      cell: (info) => {
        const title = info.getValue();
        if (!title) return '';
        return title.length > titleTruncateLength ? title.substring(0, titleTruncateLength) + '...' : title;
      },
    },
    {
      accessorKey: 'body',
      header: 'Body',
      cell: (info) => {
        const body = info.getValue();
        if (!body) return '';
        const cleanBody = stripHtmlTags(body);
        return cleanBody.length > bodyTruncateLength ? cleanBody.substring(0, bodyTruncateLength) + '...' : stripHtmlTags(cleanBody);
      },
    },
  ];

  if (props.management) {
    baseColumns.push({
      accessorKey: 'announcable_id',
      header: 'Related Entity',
      enableSorting: false,
      cell: (info) => {
        return h(TableEntityReferenceCell, { row: info.row.original });
      },
    });
  }

  baseColumns.push(
    {
      accessorKey: 'status',
      header: 'Status',
      cell: (info) => h(AnnouncementStatusBadge, { status: info.row.original.status }),
    },
    {
      accessorKey: 'start_date',
      header: props.management ? 'Start Date' : 'Posted Date',
      cell: (info) => humanizeTime(info.getValue()),
    },
    {
      id: 'actions',
      header: () => h('span', { class: 'w-full flex justify-end' }),
      cell: (info) => h(TableActionCell, { row: info.row.original, management: props.management }),
      enableSorting: false,
      enableResizing: false,
      size: 50,
    }
  );

  return baseColumns;
});
</script>
