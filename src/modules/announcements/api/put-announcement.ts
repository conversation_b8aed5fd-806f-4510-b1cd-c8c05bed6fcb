import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getAnnouncementQueryOptions } from './get-announcement';

import { Announcement } from '@/types/api';

export const updateAnnouncement = ({ announcementId, management, data }: { announcementId: string, management: boolean, data: UpdateAnnouncementPayload }) => {
  return Reshape.http().put(`/api/${management ? 'management/' : ''}announcements/${announcementId}`, data);
};

type UpdateAnnouncementPayload = Partial<Omit<Announcement, 'locked_id'>>;

type UseUpdateAnnouncementOptions = {
  announcementId: string;
  management: boolean;
  mutationConfig?: MutationConfig<typeof updateAnnouncement>;
};

export const useUpdateAnnouncement = ({ announcementId, management, mutationConfig }: UseUpdateAnnouncementOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['announcements', args[1].announcementId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getAnnouncementQueryOptions(args[1].announcementId, false).queryKey,
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: updateAnnouncement,
  });
};