import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { Announcement } from '@/types/api';

export const createAnnouncement = ({ management, data }: { management: boolean, data: CreateAnnouncementPayload }) => {
  return Reshape.http().post(`/api/${management ? 'management/' : ''}announcements`, data);
};

type CreateAnnouncementPayload = Omit<Announcement, 'id' | 'locked_id'>;

type UseCreateAnnouncementOptions = {
  management: boolean;
  mutationConfig?: MutationConfig<typeof createAnnouncement>;
};

export const useCreateAnnouncement = ({ management, mutationConfig }: UseCreateAnnouncementOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ['announcements'],
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createAnnouncement,
  });
};