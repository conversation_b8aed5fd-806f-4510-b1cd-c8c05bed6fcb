import { queryOptions, useQuery } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Announcement, Meta } from '@/types/api';

export const getAnnouncements = (
  params,
  management,
  signal
): Promise<{ data: Announcement[]; meta: Meta }> => {
  return Reshape.http().get(
    `/api/${management ? 'management/' : ''}announcements`,
    {
      params,
      signal,
    }
  );
};

export const getAnnouncementsQueryOptions = ({
  params,
  management,
}: {
  params?: unknown;
  management: boolean;
} = { management: false }) => {
  return queryOptions({
    queryKey: params ? ['announcements', params] : ['announcements'],
    queryFn: ({ signal }) => getAnnouncements(params, management, signal),
  });
};

type UseAnnouncementsOptions = {
  params?: unknown;
  management: boolean;
  queryConfig?: QueryConfig<typeof getAnnouncementsQueryOptions>;
};

export const useAnnouncements = ({ queryConfig, management, params }: UseAnnouncementsOptions) => {
  return useQuery({
    ...getAnnouncementsQueryOptions({ params, management }),
    ...queryConfig,
  });
}; 