import { useMutation, useQueryClient } from '@tanstack/vue-query';
import { MutationConfig } from '@/lib/vue-query';

// Declare the Reshape global variable
declare const Reshape: {
  http: () => {
    post: (url: string, data: any) => Promise<any>;
  };
};
export const createAnnouncementComment = ({ data }: { data: CreateAnnouncementCommentPayload }) => {
  return Reshape.http().post(`/api/announcements/${data.commentable_id}/comments`, data);
};

type CreateAnnouncementCommentPayload = {
  body: string;
  commentable_type: 'Announcement';
  commentable_id: string;
  parent_id?: string;
};

type useCreateAnnouncementCommentOptions = {
  mutationConfig?: MutationConfig<typeof createAnnouncementComment>;
};

export const useCreateAnnouncementComment = ({ mutationConfig }: useCreateAnnouncementCommentOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ['comments'],
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createAnnouncementComment,
  });
};