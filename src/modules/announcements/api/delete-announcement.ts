import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getAnnouncementsQueryOptions } from './get-announcements';

export const deleteAnnouncement = ({ announcementId }: { announcementId: string }) => {
  return Reshape.http().delete(`/api/management/announcements/${announcementId}`);
};

type UseDeleteAnnouncementOptions = {
  mutationConfig?: MutationConfig<typeof deleteAnnouncement>;
};

export const useDeleteAnnouncement = ({ mutationConfig }: UseDeleteAnnouncementOptions = {}) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['announcements', args[1].announcementId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getAnnouncementsQueryOptions().queryKey,
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteAnnouncement,
  });
};
