import { queryOptions, useQuery } from '@tanstack/vue-query';

import { Comment, Meta } from '@/types/api';
import { QueryConfig } from '@/lib/vue-query';
import { Reshape } from '@/Reshape';

export const getAnnouncementComments = (
    params,
    signal,
    id
): Promise<{
    data: Comment[];
    meta: Meta;
}> => {
    return Reshape.http().get(`/api/announcements/${id}/comments`, {
        params,
        signal,
    });
};

export const getAnnouncementCommentsQueryOptions = ({ params, id }: { params?: unknown, id?: string } = {}) => {
    return queryOptions({
        queryKey: params ? ['comments', params] : ['comments'],
        queryFn: ({ signal }) => getAnnouncementComments(params, signal, id),
    });
};

type UseAnnouncementCommentsOptions = {
    params?: unknown;
    queryConfig?: QueryConfig<typeof getAnnouncementCommentsQueryOptions>;
    id?: string;
};

export const useAnnouncementComments = ({ queryConfig, params, id }: UseAnnouncementCommentsOptions = {}) => {
    return useQuery({
        ...getAnnouncementCommentsQueryOptions({ params, id }),
        ...queryConfig,
    });
}; 