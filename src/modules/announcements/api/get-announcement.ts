import { useQuery, queryOptions } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Announcement } from '@/types/api';
import { Reshape } from '@/Reshape';

export const getAnnouncement = ({
  announcementId,
  management = false,
}: {
  announcementId: string;
  management?: boolean;
}): Promise<{ Announcement }> => {
  return Reshape.http()
    .get(`/api/${management ? 'management/' : ''}announcements/${announcementId}`)
    .then(({ data }) => data);
};

export const getAnnouncementQueryOptions = (announcementId: string, management?: boolean) => {
  return queryOptions({
    queryKey: ['announcements', announcementId],
    queryFn: () => getAnnouncement({ announcementId, management }),
  });
};

type UseAnnouncementOptions = {
  announcementId: string;
  management?: boolean;
  queryConfig?: QueryConfig<typeof getAnnouncementQueryOptions>;
};

export const useAnnouncement = ({ announcementId, management = false, queryConfig }: UseAnnouncementOptions) => {
  return useQuery({
    ...getAnnouncementQueryOptions(announcementId, management),
    ...queryConfig,
  });
}; 