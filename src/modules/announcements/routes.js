import UserLayout from '@/layouts/UserLayout.vue';
import { auth } from '@/router/middleware';

export default [
  // User Announcements
  {
    path: '/announcements',
    component: UserLayout,
    meta: {
      middleware: auth,
    },
    children: [
      {
        path: '',
        name: 'announcements.index',
        component: () => import('@/modules/announcements/pages/AnnouncementIndex.vue'),
        meta: {
          title: 'Announcements',
          headerTitle: 'Announcements',
          headerDescription: 'All relevant announcements.',
          breadcrumbs: [
            { label: 'Announcements', name: 'announcements.index' },
          ],
        },
      },
      {
        path: 'add',
        name: 'announcements.create',
        component: () => import('@/modules/announcements/pages/AnnouncementCreate.vue'),
        meta: {
          title: 'Add Announcement',
          headerTitle: 'Add Announcement',
          headerDescription: 'Create a new announcement.',
          breadcrumbs: [
            { label: 'Announcements', name: 'announcements.index' },
            { label: 'Add', name: 'announcements.create' },
          ],
        },
      },
      {
        path: ':id/edit',
        name: 'announcements.update',
        component: () => import('@/modules/announcements/pages/AnnouncementUpdate.vue'),
        props: true,
        meta: {
          title: 'Update Announcement',
          headerTitle: 'Update Announcement',
          headerDescription: 'Update the details of the selected announcement.',
          breadcrumbs: [
            { label: 'Announcements', name: 'announcements.index' },
            { label: 'Edit', name: 'announcements.update' },
          ],
        },
      },
      {
        path: ':id',
        name: 'announcements.show',
        component: () => import('@/modules/announcements/pages/AnnouncementShow.vue'),
        props: true,
        meta: {
          title: 'View Announcement Details',
          headerTitle: 'View Announcement Details',
          headerDescription: 'View the selected announcement\'s details.',
          breadcrumbs: [
            { label: 'Announcements', name: 'announcements.index' },
            { label: 'View', name: 'announcements.show' },
          ],
        },
      },
      {
        path: ':id',
        name: 'announcements.view',
        component: () => import('@/modules/announcements/pages/AnnouncementView.vue'),
        props: true,
        meta: {
          title: 'View Announcement',
          headerTitle: 'View Announcement',
          headerDescription: 'View the selected announcement.',
          breadcrumbs: [
            { label: 'Announcements', name: 'announcements.index' },
            { label: 'View Details', name: 'announcements.view' },
          ],
        },
      },
    ],
  },
  // Management Announcements
  {
    path: '/settings/announcements',
    component: UserLayout,
    meta: {
      middleware: auth,
    },
    children: [
      {
        path: '',
        name: 'settings.announcements.index',
        component: () => import('@/modules/announcements/pages/AnnouncementIndex.vue'),
        props: {
          management: true,
        },
        meta: {
          title: 'Announcements',
          headerTitle: 'Announcements',
          headerDescription: 'All announcements defined within the application.',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Announcements', name: 'settings.announcements.index' },
          ],
        },
      },
      {
        path: 'add',
        name: 'settings.announcements.create',
        component: () => import('@/modules/announcements/pages/AnnouncementCreate.vue'),
        props: {
          management: true,
        },
        meta: {
          title: 'Add Announcement',
          headerTitle: 'Add Announcement',
          headerDescription: 'Create a global announcement or target a specific entity.',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Announcements', name: 'settings.announcements.index' },
            { label: 'Add', name: 'settings.announcements.create' },
          ],
        },
      },
      {
        path: ':id/edit',
        name: 'settings.announcements.update',
        component: () => import('@/modules/announcements/pages/AnnouncementUpdate.vue'),
        props: (route) => ({
          management: true,
          id: route.params.id,
        }),
        meta: {
          title: 'Edit Announcement',
          headerTitle: 'Edit Announcement',
          headerDescription: 'Update the details of the selected announcement.',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Announcements', name: 'settings.announcements.index' },
            { label: 'Edit', name: 'settings.announcements.update' },
          ],
        },
      },
      {
        path: ':id/view',
        name: 'settings.announcements.view',
        component: () => import('@/modules/announcements/pages/AnnouncementView.vue'),
        props: (route) => ({
          management: true,
          id: route.params.id,
        }),
        meta: {
          title: 'View Announcement',
          headerTitle: 'View Announcement',
          headerDescription: 'View the details of the selected announcement.',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Announcements', name: 'settings.announcements.index' },
            { label: 'View', name: 'settings.announcements.view' },
          ],
        },
      },
      {
        path: ':id/activity',
        name: 'settings.announcements.activity',
        component: () => import('@/modules/activity/pages/ActivityIndex.vue'),
        props: (route) => ({
          management: true,
          activitableType: 'Announcement',
          activitableId: route.params.id,
        }),
        meta: {
          title: 'Announcement Activity',
          headerTitle: 'Announcement Activity',
          headerDescription: 'View the activity for this announcement.',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Announcements', name: 'settings.announcements.index' },
            { label: 'View', name: 'settings.announcements.view' },
            { label: 'Activity', name: 'settings.announcements.activity' },
          ],
        },
      },
    ],
  },
];
