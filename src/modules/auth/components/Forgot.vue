<template>
  <AuthLayout>
    <div class="space-y-4 p-6 sm:p-8 md:space-y-6">
      <h1 class="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl">Reset your password</h1>
      <form class="space-y-4 md:space-y-6" @submit.prevent="submit">
        <ValidationErrors :errors="form.errors.flat()" />

        <VField label="Email" required>
          <VInput v-model="form.email" type="email" placeholder="Email" autocomplete="email" />
        </VField>

        <VField label="Password" required>
          <VInput v-model="form.password" required type="password" placeholder="Password" autocomplete="password" />
        </VField>

        <div class="flex items-center justify-between">
          <VCheckbox v-model="form.remember">Remember me</VCheckbox>
          <RouterLink
            :to="{ name: 'auth.password.forgot' }"
            class="text-sm font-medium text-primary-600 hover:underline"
            >Forgot password?</RouterLink
          >
        </div>

        <VButton type="submit" class="w-full" intent="primary">Sign in</VButton>

        <p class="text-sm font-light text-gray-500">
          Don’t have an account yet?
          <RouterLink :to="{ name: 'auth.register' }" class="font-medium text-primary-600 hover:underline"
            >Sign up</RouterLink
          >
        </p>
      </form>
    </div>
  </AuthLayout>
</template>

<script>
import LogoContainer from '@/components/ui/logos/LogoContainer.vue';
import AuthLayout from '@/modules/auth/components/AuthLayout.vue';
import AuthSessionStatus from '@/modules/auth/components/AuthSessionStatus.vue';
import BIconGithub from '~icons/bi/github';
import BIconGoogle from '~icons/bi/google';
import BIconKey from '~icons/bi/key';
import BIconLinkedin from '~icons/bi/linkedin';

export default {
  name: 'Login',
  components: {
    AuthLayout,
    AuthSessionStatus,
    LogoContainer,

    BIconGoogle,
    BIconGithub,
    BIconLinkedin,
    BIconKey,
  },
  data() {
    return {
      form: Reshape.form({
        email: '',
        password: '',
        remember: false,
      }),
      status: '',
    };
  },
  computed: {
    valid() {
      return this.form.email && this.form.password;
    },
  },
  mounted() {
    if (this.$route.query.reset) {
      this.status = this.$route.query.reset;
    }
  },
  methods: {
    submit() {
      this.form.post('/login').then(() => {
        this.$route.query.redirect
          ? this.$router.push({ path: this.$route.query.redirect })
          : this.$router.push({ name: 'index' });
      });
    },
  },
};
</script>
