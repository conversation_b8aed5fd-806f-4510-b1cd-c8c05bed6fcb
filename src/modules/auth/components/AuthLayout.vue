<template>
  <div class="flex min-h-full">
    <div class="z-10 flex flex-1 flex-col justify-center bg-platinum-300 px-4 py-12 shadow-xl shadow-black/75 drop-shadow-xl dark:bg-midnight-800 sm:px-6 lg:flex-none lg:px-20 2xl:px-40">
      <div class="mx-auto w-full max-w-md rounded-lg bg-platinum-200 shadow dark:bg-midnight-600 md:mt-0 lg:w-[28rem] 2xl:w-[32rem] lg:max-w-full xl:p-0">
        <slot></slot>
      </div>
    </div>
    <div class="relative hidden w-0 flex-1 lg:block">
      <div class="absolute inset-0 bg-gradient-to-l dark:bg-gradient-to-t from-platinum-300/80 dark:from-midnight-800/80 to-transparent z-10"></div>
      <Image class="absolute inset-0 h-full w-full object-cover" alt="" />
    </div>
  </div>
</template>

<script setup>
import Image from '@/components/ui/Image.vue';

const props = defineProps({
  page: {
    type: String,
    default: 'login',
  },
});
</script>
