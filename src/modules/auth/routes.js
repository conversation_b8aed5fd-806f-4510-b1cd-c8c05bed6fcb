import UserLayout from '@/layouts/UserLayout.vue';
import { guest } from '@/router/middleware';

export default [
  {
    path: '/',
    component: UserLayout,
    meta: {
      middleware: guest,
    },
    children: [
      {
        path: 'login',
        name: 'auth.login',
        component: () => import('./pages/Login.vue'),
        meta: {
          title: 'Login',
        },
      },
      {
        path: 'register',
        name: 'auth.register',
        component: () => import('./pages/register/Register.vue'),
        meta: {
          title: 'Register',
        },
      },
      {
        path: 'forgot',
        name: 'auth.password.forgot',
        component: () => import('./pages/password/Forgot.vue'),
        meta: {
          title: 'Forgot Password',
        },
      },
      {
        path: 'reset/:token',
        name: 'auth.password.reset',
        component: () => import('./pages/password/Reset.vue'),
        meta: {
          title: 'Reset Password',
        },
      },
    ],
  },
];
