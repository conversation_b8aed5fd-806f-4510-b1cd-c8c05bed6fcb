<template>
  <auth-layout page="forgot">
    <transition-fade>
      <Email v-if="step === 'email'" @sent="sent" />
      <Sent v-if="step === 'sent'" />
    </transition-fade>
  </auth-layout>
</template>

<script setup>
import { ref } from 'vue';
import AuthLayout from '@/modules/auth/components/AuthLayout.vue';
import Email from '@/modules/auth/pages/password/partials/Email.vue';
import Sent from '@/modules/auth/pages/password/partials/Sent.vue'

const step = ref('email');

function sent() {
  step.value = 'sent';
}
</script>
