<template>
  <AuthLayout>
    <div class="space-y-4 p-6 sm:p-8 md:space-y-6">
      <h1
        class="text-xl font-bold leading-tight tracking-tight text-primary-500 md:text-2xl"
      >Set a new password</h1>
      <form @submit.prevent="submit">
        <div class="space-y-4 md:space-y-6">
          <VField
            label="Password"
            required
            :errors="form.errors.get('password')"
          >
            <VInput
              v-model="form.password"
              type="password"
              placeholder="Password"
              required
            />
          </VField>

          <VField
            label="Confirm Password"
            required
            :errors="form.errors.get('password_confirmation')"
          >
            <VInput
              v-model="form.password_confirmation"
              required
              type="password"
              placeholder="Confirm Password"
            />
          </VField>
        </div>
        
        <VButton
          type="submit"
          class="mt-8 w-full"
          intent="primary"
          centered
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Reset password
        </VButton>
      </form>
      <p class="text-center text-sm text-platinum-900 dark:text-midnight-100">
        <RouterLink :to="{ name: 'auth.login' }" class="font-medium text-primary-500 hover:text-primary-400">
          <BIconArrowLeft class="mr-1 inline w-4" />Return to Login
        </RouterLink>
      </p>
    </div>
  </AuthLayout>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import AuthLayout from '@/modules/auth/components/AuthLayout.vue'
import BIconCheckLg from '~icons/bi/check-lg'
import BIconArrowLeft from '~icons/bi/arrow-left'
import { useForm } from '@/hooks/form';

// Route & Router
const route = useRoute()
const router = useRouter()

// Reactive form setup using Reshape (assuming global import or composable)
const form = useForm({
  email: route.query.email ?? '',
  token: route.params.token ?? '',
  password: '',
  password_confirmation: '',
})

const submit = () => {
  form.value
    .submit('post', 'password/reset')
    .then((response) => {
      Reshape.toast(
        {
          title: 'Password Updated',
          message: `Your password has been successfully reset. You can now login with your new password!`,
        },
        'success'
      );
      router.push({
        name: 'auth.login',
      });
    });
};
</script>
