<template>
  <div class="space-y-4 p-6 sm:p-8 md:space-y-6">
    <div>
      <h1 class="text-xl font-bold leading-tight tracking-tight text-primary-500 md:text-2xl">
        Forgot your password?
      </h1>
      <p class="my-2 text-sm text-platinum-950 dark:text-midnight-100">
        No problem! Resetting your password is super simple. Just provide us with a valid email address below and
        we’ll drop you a quick email with instructions to get you back on track.
      </p>
      <hr class="mt-4 border-platinum-300 dark:border-midnight-400" />
    </div>
    <form class="space-y-4 md:space-y-6" id="email" @submit.prevent="submit">
      <ValidationErrors :errors="form.errors.flat()" />

      <VField label="Email" required>
        <VInput v-model="form.email" type="email" placeholder="Email" autocomplete="email" />
      </VField>
      <div class="space-y-4">
        <VButton type="submit" form="email" class="w-full" centered intent="primary">
          <template #start>
            <BIconSendFill class="mr-1" />
          </template>
          Send Reset Instructions
        </VButton>

        <p class="text-center text-sm text-platinum-900 dark:text-midnight-100">
          <RouterLink :to="{ name: 'auth.login' }" class="font-medium text-primary-500 hover:text-primary-400">
            <BIconArrowLeft class="mr-1 inline w-4" />Return to Login
          </RouterLink>
        </p>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import BIconSendFill from '~icons/bi/send-fill';
import BIconArrowLeft from '~icons/bi/arrow-left';

const emit = defineEmits(['sent']);

const form = ref(
  Reshape.form({
    email: '',
  })
);

const submit = async () => {
  await Reshape.csrf();

  form.value.post('/password/forgot').then(() => emit('sent'));
};
</script>
