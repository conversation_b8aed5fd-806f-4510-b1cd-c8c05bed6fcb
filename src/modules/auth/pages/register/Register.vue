<template>
  <AuthLayout page="register">
    <TransitionFade>
      <component
        :is="currentComponent"
        :key="step"
        v-bind="currentProps"
        @complete="handleNext"
        @transition="goToStep"
      />
    </TransitionFade>
  </AuthLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';

import AuthLayout from '@/modules/auth/components/AuthLayout.vue';
import Email from '@/modules/auth/pages/register/partials/Email.vue';
import OneTimePassword from '@/modules/auth/pages/register/partials/Otp.vue';
import Setup from '@/modules/auth/pages/register/partials/Setup.vue';

const router = useRouter();

const steps = ['email', 'code', 'setup'];
const currentStepIndex = ref(0);
const step = computed(() => steps[currentStepIndex.value]);

const user = ref(null);

const componentsMap = {
  email: Email,
  code: OneTimePassword,
  setup: Setup,
};

const currentComponent = computed(() => componentsMap[step.value]);

const currentProps = computed(() => {
  if (step.value === 'email') return {};
  return { user: user.value };
});

function handleNext(payload) {
  switch (step.value) {
    case 'email':
      user.value = payload;
      nextStep();
      break;
    case 'code':
      user.value.token = payload;
      nextStep();
      break;
    case 'setup':
      Reshape.toast(
        { title: 'Your account has been created successfully. You can now login!' },
        'success'
      );
      // remove the remembered email from local storage in case one exists.
      if (localStorage.getItem('rememberedEmail')) {
        localStorage.removeItem('rememberedEmail');
      }

      router.push({ name: 'auth.login' });
      break;
    default:
      router.push({ name: 'auth.login' });
  }
}

function goToStep(stepName) {
  const index = steps.indexOf(stepName);
  if (index !== -1) currentStepIndex.value = index;
}

function nextStep() {
  if (currentStepIndex.value < steps.length - 1) {
    currentStepIndex.value++;
  }
}
</script>
