<template>
  <div>
    <TransitionFade>
      <VSpinner v-if="loading" size="xl" class="flex justify-center py-10" :contactUsMessage="false" />
      <div v-else class="space-y-4 p-6 sm:p-8 md:space-y-6">
        <div class="space-y-2">
          <h1 class="text-xl font-bold leading-tight tracking-tight text-primary-500 md:text-2xl text-center">Ready to get started?</h1>
          <p class="text-sm text-platinum-900 dark:text-midnight-100 text-center flex justify-center space-x-1.5 align-middle">
            <span>Already registered?</span>
            <VTextLink
              :to="{ name: 'auth.login' }"
              :disableIcon="true"
              class="inline-flex"
            >
              <BIconBoxArrowInRight class="h-4 w-4 inline mr-1" />Login
            </VTextLink>
          </p>
        </div>
        <form
          class="space-y-4 md:space-y-6"
          @submit.prevent="submit"
          aria-label="Registration form"
        >
          <ValidationErrors :errors="form.errors.flat()" />
          <VField label="Email" required>
            <VInput
              v-model="form.email"
              type="email"
              placeholder="Enter Email Address"
              autocomplete="email"
              aria-label="Email address"
            />
          </VField>

          <VButton
            type="submit"
            class="w-full group/next py-3 relative"
            intent="primary"
            centered
            aria-label="Submit registration"
            @mouseenter="show = true"
            @mouseleave="show = false"
          >
            <div class="absolute right-0 left-0">Sign Up</div>
            <template #end>
              <BIconArrowRight class="h-4 inline ml-1 -mt-0.5 opacity-0 transition group-hover/next:opacity-100 -translate-x-3 group-hover/next:translate-x-0 delay-0 group-hover/next:delay-200" />
            </template>
          </VButton>
        </form>
        <div class="space-y-3">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-platinum-300 dark:border-midnight-400"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <label
                class="bg-platinum-200 px-2 text-platinum-900 dark:bg-midnight-600 dark:text-midnight-200"
              >Or speed things up with</label>
            </div>
          </div>
          <div class="grid grid-cols-3 gap-3">
            <VTooltip content="Register via Google">
              <VButton
                intent="primary"
                outline
                class="group w-full"
                centered
                aria-label="Register via Google"
                @click="socialRegister('google')"
              >
                <BIconGoogle class="h-5 w-5 transition-colors group-hover:text-google-plus dark:group-hover:text-midnight-50" />
              </VButton>
            </VTooltip>
            <VTooltip content="Register via GitHub">
              <VButton
                intent="primary"
                outline
                class="group w-full"
                centered
                aria-label="Register via GitHub"
                @click="socialRegister('github')"
              >
                <BIconGithub class="h-5 w-5 transition-colors group-hover:text-github dark:group-hover:text-midnight-50" />
              </VButton>
            </VTooltip>
            <VTooltip content="Register via LinkedIn">
              <VButton
                intent="primary"
                outline
                class="group w-full"
                centered
                aria-label="Register via LinkedIn"
                @click="socialRegister('linkedin')"
              >
                <BIconLinkedin class="h-5 w-5 transition-colors group-hover:text-linkedin dark:group-hover:text-midnight-50" />
              </VButton>
            </VTooltip>
          </div>
          <div class="flex pt-3 px-10">
            <span class="text-xs text-platinum-900 dark:text-midnight-100 text-center">
              By registering, you agree to the {{ appTitle }}
              <div class="inline-block">
                <VTextLink
                  @click.prevent="showTermsModal = true"
                  :disableIcon="true"
                  linkType="button"
                >Terms of Service</VTextLink> & 
                <VTextLink
                  @click.prevent="showPrivacyModal = true"
                  :disableIcon="true"
                  linkType="button"
                >Privacy Policy</VTextLink>.
              </div>
            </span>
          </div>
        </div>
      </div>
    </TransitionFade>

    <TermsModal
      v-model:open="showTermsModal"
      @cancelled="showTermsModal = false"
      @showPrivacyModal="showPrivacyModal = true"
    />

    <PrivacyModal
      v-model:open="showPrivacyModal"
      @cancelled="showPrivacyModal = false"
      @showTermsModal="showTermsModal = true"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import BIconGoogle from '~icons/bi/google';
import BIconLinkedin from '~icons/bi/linkedin';
import BIconGithub from '~icons/bi/github';
import BIconBoxArrowInRight from '~icons/bi/box-arrow-in-right';
import BIconArrowRight from '~icons/bi/arrow-right';
import TermsModal from '@/composables/modals/TermsModal.vue';
import PrivacyModal from '@/composables/modals/PrivacyModal.vue';
import { axios } from '@/lib/axios';
import { useOAuthToasts } from '@/composables/useOAuthToasts';

const appTitle = import.meta.env.VITE_APP_TITLE || 'reShape';

// Initialize OAuth toast handling for registration.
useOAuthToasts({ successType: 'register' });

const show = ref(false);

const emit = defineEmits(['complete']);

const showTermsModal = ref(false);
const showPrivacyModal = ref(false);

const form = ref(
  Reshape.form({
    email: '',
    terms: '',
  })
);

const loading = ref(false);
const error = ref('');

async function submit() {
  loading.value = true;
  await Reshape.csrf();

  form.value
    .post('/register')
    .then((user) => emit('complete', user.data))
    .catch((error) => {
      if (error.response?.data?.errors) {
        form.value.setErrors(error.response.data.errors);
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

const socialRegister = (provider) => {
  loading.value = true;
  error.value = '';

  // Get redirect url from backend.
  axios.get(`/api/auth/redirect/register/${provider}`)
    .then((response) => {
      window.location.href = response.data.url;
    })
    .catch((e) => {
      console.error(e);
      error.value = e.message;
      loading.value = false;
    });
};
</script>
