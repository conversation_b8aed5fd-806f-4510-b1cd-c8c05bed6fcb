<template>
  <div>
    <VSpinner v-if="verifying" size="xl" class="flex justify-center py-10" />
    <div class="space-y-4 p-6 sm:p-8 md:space-y-6" v-else>
      <div>
        <h1 class="text-xl font-bold leading-tight tracking-tight text-primary-500 md:text-2xl">
          One-Time Password
        </h1>
        <h2 class="text-md mt-3 font-medium leading-tight tracking-tight text-platinum-950 dark:text-midnight-50">
          We've sent a verification code to your email. Please enter it below to continue.
        </h2>
      </div>

      <form class="space-y-4 md:space-y-6" @submit.prevent="submit">
        <VAlert intent="success" v-if="attempts > 0">
          <p class="font-semibold">{{ alertMessage }}</p>
          <p v-if="attempts === 1" class="mt-2">Make sure to check your spam or junk folder.</p>
          <p v-else class="mt-2">
            We've sent another email. If you're still not receiving it, please
            <VTextLink
              :to="{ name: 'support.contact', query: { topic: 'account-management' } }"
              disable-icon
              class="underline"
              style="color: unset;"
            >
              contact us</VTextLink>.
          </p>
        </VAlert>

        <div>
          <ValidationErrors :errors="form.errors.flat()" />
          <VOtp @complete="verify" placeholder="----" />
        </div>

        <VButton
          :disabled="resendCooldown > 0"
          @click="resend"
          intent="primary"
          class="w-full"
          centered
        >
          <template v-if="resendCooldown > 0">
            Resend in {{ resendCooldown }}s
          </template>
          <template v-else>
            Resend Verification Code
          </template>
        </VButton>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

const props = defineProps({
  user: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['complete']);

const form = ref(
  Reshape.form({
    email: '',
    token: '',
  })
);

const attempts = ref(0);
const verifying = ref(false);
const resendCooldown = ref(0);
let countdownInterval = null;

function startCooldown(seconds = 20) {
  resendCooldown.value = seconds;
  clearInterval(countdownInterval);
  countdownInterval = setInterval(() => {
    resendCooldown.value--;
    if (resendCooldown.value <= 0) {
      clearInterval(countdownInterval);
    }
  }, 1000);
}

const alertMessage = computed(() =>
  attempts.value === 1
    ? 'A new verification email has been sent.'
    : 'Another email has been sent to your inbox.'
);

async function resend() {
  if (resendCooldown.value > 0) return;

  attempts.value++;
  await Reshape.csrf();

  form.value
    .populate({ email: props.user.email })
    .post('/register/otp/resend')
    .then(() => {
      Reshape.toast(
        { title: 'A new verification code has been sent to your email.' },
        'success'
      );
      startCooldown(20);
    });
}

async function verify(token) {
  verifying.value = true;

  try {
    await Reshape.csrf();
    await form.value
      .populate({
        email: props.user.email,
        token,
      })
      .post('/register/otp/verify');

    emit('complete', token);
  } finally {
    verifying.value = false;
  }
}

onMounted(() => {
  startCooldown(20);
});
</script>
