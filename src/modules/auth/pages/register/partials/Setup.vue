<template>
  <TransitionFade>
    <VSpinner v-if="loading" size="xl" class="flex justify-center py-10" />
    <div class="space-y-4 p-6 sm:p-8 md:space-y-6">
      <h1 class="text-xl font-bold leading-tight tracking-tight text-primary-500 md:text-2xl">Set a password</h1>
      <form @submit.prevent="submit">
        <ValidationErrors :errors="form.errors.flat()" />

        <div class="space-y-4 md:space-y-6">
          <VField label="Password" required>
            <VInput v-model="form.password" type="password" placeholder="Enter Password" />
          </VField>

          <VField label="Confirm Password" required>
            <VInput
              v-model="form.password_confirmation"
              required
              type="password"
              placeholder="Confirm Password"
              autocomplete="password"
            />
          </VField>

          <VField label="Referral Code" help="If you have a referral code from an existing user, enter it here.">
            <VInput v-model="form.referral_code" type="text" placeholder="Enter Referral Code" />
          </VField>

          <VButton type="submit" intent="primary" class="w-full" centered>
            <BIconCheckLg class="mr-2" />
            <span>Complete Registration</span>
          </VButton>
        </div>
      </form>
    </div>
  </TransitionFade>
</template>

<script setup>
import { ref } from 'vue';
import { Http } from '@/types';
import BIconCheckLg from '~icons/bi/check-lg';
import AuthLayout from '@/modules/auth/components/AuthLayout.vue';

const props = defineProps({
  user: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['complete', 'transition']);

const form = ref(
  Reshape.form({
    email: '',
    token: '',
    password: '',
    password_confirmation: '',
    referral_code: '',
  })
);

const attempts = ref(0);
const loading = ref(false);

async function submit() {
  loading.value = true;
  await Reshape.csrf();

  form.value
    .populate({
      email: props.user.email,
      token: props.user.token,
    })
    .post('/register/password')
    .then(() => emit('complete'))
    .catch((error) => {
      loading.value = false;
      if (error.response.status == Http.GONE) {
        emit('transition', 'code');
      } else {
        throw error;
      }
    });
}
</script>
