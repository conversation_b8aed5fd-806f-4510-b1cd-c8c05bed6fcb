<template>
  <AuthLayout>
    <div class="space-y-4 px-6 py-6 sm:px-8 md:space-y-6">
      <TransitionFade>
        <VSpinner v-if="loginLoading" size="xl" class="flex justify-center py-10" :contactUsMessage="false" />
        <div v-else class="space-y-4 md:space-y-6">
          <div class="space-y-2">
            <h1 class="text-xl font-bold leading-tight tracking-tight text-primary-500 md:text-2xl text-center">Welcome back!</h1>
            <p class="text-sm text-platinum-900 dark:text-midnight-100 text-center flex justify-center space-x-1.5">
              <span>Don't have an account yet?</span>
              <VTextLink
                :to="{ name: 'auth.register' }"
                :disableIcon="true"
                class="inline-flex text-sm"
              >
                <BIconClipboardCheck class="h-4 w-[0.95rem] inline mr-1" />Sign up now!
              </VTextLink>
            </p>
          </div>
          <form class="space-y-4 md:space-y-6" @submit.prevent="submit">
          <ValidationErrors v-if="hasErrors" :errors="flatErrors" />
          <VAlert v-else-if="error" intent="danger">{{ error }}</VAlert>
          <VAlert v-if="status" intent="success">{{ status }}</VAlert>

          <VField label="Email" required>
            <VInput
              v-model="form.email"
              required
              type="email"
              placeholder="Email"
              autocomplete="email"
            />
          </VField>

          <VField label="Password" required>
            <VInput
              v-model="form.password"
              required
              type="password"
              placeholder="Password"
              autocomplete="password"
            />
          </VField>

          <div class="flex select-none items-center justify-between text-sm font-medium">
            <VCheckbox v-model="form.remember" :checked="form.remember">Remember me</VCheckbox>
            <VTextLink
              :to="{ name: 'auth.password.forgot' }"
              :disableIcon="true"
            >
              <BIconQuestionCircle class="mx-1 -mt-0.5 inline w-4" />Forgot password?
            </VTextLink>
          </div>

          <VButton type="submit" class="w-full align-middle" centered intent="primary">
            <div class="flex justify-center align-middle">
              <div class="align-middle">
                <BIconBoxArrowInRight class="float-left mr-2 h-full align-middle" />Sign in
              </div>
            </div>
          </VButton>
        </form>
        <div class="space-y-3">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-platinum-300 dark:border-midnight-400"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <label
                class="bg-platinum-200 px-2 text-platinum-900 dark:bg-midnight-600 dark:text-midnight-200"
              >Or continue with</label>
            </div>
          </div>
          <div class="grid grid-cols-3 gap-3">
            <VTooltip content="Login via Google">
              <VButton
                intent="primary"
                outline
                class="group w-full"
                centered
                aria-label="Login via Google"
                @click="socialLogin('google')"
              >
                <BIconGoogle class="h-5 w-5 transition-colors group-hover:text-google-plus dark:group-hover:text-midnight-50" />
              </VButton>
            </VTooltip>
            <VTooltip content="Login via GitHub">
              <VButton
                intent="primary"
                outline
                class="group w-full"
                centered
                aria-label="Login via GitHub"
                @click="socialLogin('github')"
              >
                <BIconGithub class="h-5 w-5 transition-colors group-hover:text-github dark:group-hover:text-midnight-50" />
              </VButton>
            </VTooltip>
            <VTooltip content="Login via LinkedIn">
              <VButton
                intent="primary"
                outline
                class="group w-full"
                centered
                aria-label="Login via LinkedIn"
                @click="socialLogin('linkedin')"
              >
                <BIconLinkedin class="h-5 w-5 transition-colors group-hover:text-linkedin dark:group-hover:text-midnight-50" />
              </VButton>
            </VTooltip>
          </div>
        </div>
        </div>
      </TransitionFade>
    </div>
  </AuthLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { axios } from '@/lib/axios';
import { useOAuthToasts } from '@/composables/useOAuthToasts';

import AuthLayout from '@/modules/auth/components/AuthLayout.vue';
import BIconBoxArrowInRight from '~icons/bi/box-arrow-in-right';
import BIconClipboardCheck from '~icons/bi/clipboard-check';
import BIconQuestionCircle from '~icons/bi/question-circle';
import BIconGoogle from '~icons/bi/google';
import BIconLinkedin from '~icons/bi/linkedin';
import BIconGithub from '~icons/bi/github';

const form = ref(
  Reshape.form({
    email: localStorage.getItem('rememberedEmail') ?? '',
    password: '',
    remember: !!localStorage.getItem('rememberedEmail'),
  })
);

const status = ref('');
const error = ref('');
const loginLoading = ref(false);

// Initialize OAuth toast handling.
useOAuthToasts({ successType: 'login' });

const route = useRoute();
const router = useRouter();

onMounted(() => {
  if (route.query.reset) {
    status.value = route.query.reset;
  }
});

const submit = () => {
  loginLoading.value = true;
  error.value = '';
  
  const { remember, email } = form.value;
  form.value
    .post('/login')
    .then(() => {
      if (remember) {
        localStorage.setItem('rememberedEmail', email);
      } else {
        localStorage.removeItem('rememberedEmail');
      }
      if (route.query.redirect) {
        router.push({
          path: route.query.redirect,
          replace: true,
        });
      } else {
        router.push({
          name: 'index',
          replace: true,
        });
      }
    })
    .catch((e) => {
      console.error(e);
      error.value = e.message;
      loginLoading.value = false;
    });
};

const flatErrors = computed(() => {
  return Array.isArray(form.value.errors.flat())
    ? form.value.errors.flat()
    : [];
});
const hasErrors = computed(() => flatErrors.value.length > 0);

const socialLogin = (provider) => {
  loginLoading.value = true;
  error.value = '';

  // Get redirect url from backend.
  axios.get(`/api/auth/redirect/connect/${provider}`)
    .then((response) => {
      window.location.href = response.data.url;
    })
    .catch((e) => {
      console.error(e);
      error.value = e.message;
      loginLoading.value = false;
    });
};

</script>