import { axios } from '@/lib/axios';

export const AuthService = {
  /**
   * Log the user in.
   */
  async login(data) {
    await Reshape.csrf();
    return axios.post('login', data);
  },

  /**
   * Log the user out.
   */
  async logout() {
    await Reshape.csrf();
    return axios.delete('logout');
  },

  /**
   * Get the authenticated user.
   */
  async user() {
    return axios.get('api/user');
  },
};
