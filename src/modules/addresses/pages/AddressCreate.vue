<template>
  <div>
    <VPageHeader>
      <template #actions>
        <VButton
          type="submit"
          form="address-form"
          intent="secondary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>
        <VButton
          @click="confirmModalOpen = true"
          intent="danger"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <AddressForm
        :addressable-type="addressableType"
        :addressable-id="addressableId"
        @submit="submit"
      />

      <div class="flex w-full justify-start lg:justify-end space-x-3">
        <VButton
          type="submit"
          form="address-form"
          intent="primary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>

        <VButton intent="danger" @click="confirmModalOpen = true">
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </div>
    </VContainer>

    <VConfirmModal
      v-model:open="confirmModalOpen"
      @confirmed="router.push(returnRoute)"
    />
  </div>
</template>

<script setup>
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconPlusLg from '~icons/bi/plus-lg';
import AddressForm from '@/modules/addresses/components/AddressForm.vue';
import { ref, computed } from 'vue';
import { useCreateAddress } from '@/modules/addresses/api/post-address';
import { useAuth } from '@/store/auth';

// Get auth store.
const auth = useAuth();

const props = defineProps({
  addressableType: {
    type: String,
    required: false,
  },
  addressableId: {
    type: String,
    required: false,
  },
  self: {
    type: Boolean,
    default: false,
  },
});

const user = ref(null);
// We only need the user id if address is for user and self.
if (props?.self && props?.addressableType === 'users') {
  user.value = auth.user;
}

// Override the addressableId. Supports self for user and addressableId for other entities.
const addressableId = computed(() => {
  if (props?.self && props?.addressableType === 'users') {
    return user.value.locked_id;
  }
  return props?.addressableId;
});

const confirmModalOpen = ref(false);

const returnRoute = computed(() => {
  if (props?.addressableType === 'users' && props?.self) {
    return { name: 'profile.addresses.index' };
  } else if (props?.addressableType && props?.addressableId) {
    return { name: `${props.addressableType}.addresses.index`, params: { id: props.addressableId } };
  } else {
    return { name: 'addresses.index' };
  }
});

const createAddressMutation = useCreateAddress({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Address Successfully Created',
          message: props?.self
            ? `Your requested address was successfully created.`
            : `Address for <b>${result.data?.addressable?.lookup}</b> was successfully created.`,
        },
        'success'
      );
      if (props?.successRoute && props?.self) {
        // User is creating an address for themselves
        router.push({ name: props.successRoute });
      } else {
        if (props?.addressableType && props?.addressableId) {
          // Specific entity route.
          router.push({ name: `${props?.addressableType}.addresses.view`, params: { id: props.addressableId } });
        } else {
          // Default route.
          router.push({ name: 'addresses.view', params: { id: result.data.locked_id } });
        }
      }
    },
  },
});

const submit = (form) => {
  form.submit((data) => createAddressMutation.mutateAsync({
    data,
  }));
};

</script>
