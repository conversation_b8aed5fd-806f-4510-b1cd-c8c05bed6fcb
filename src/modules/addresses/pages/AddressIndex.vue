<template>
  <div>
    <VPageHeader>
      <VButton
        as="router-link"
        :to="createRoute"
        intent="secondary"
      >
        <template #start>
          <BIconPlusLg />
        </template>
        Add Address
      </VButton>
    </VPageHeader>
    <VContainer class="py-2 lg:py-6">
      <AddressTable
        :addressable-type="addressableType || null"
        title="Addresses"
        :records="addresses?.data"
        :total-records="addresses?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
      />
    </VContainer>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useFilterParams } from '@/hooks/query';
import AddressTable from '@/modules/addresses/components/AddressTable.vue';
import BIconPlusLg from '~icons/bi/plus-lg';
import { useAddresses } from '@/modules/addresses/api/get-addresses';
import { useUserAddresses } from '@/modules/users/api/get-user-addresses';
import { useCompanyAddresses } from '@/modules/companies/api/get-company-addresses';
import { useCustomerAddresses } from '@/modules/customers/api/get-customer-addresses';
import { useAuth } from '@/store';

const props = defineProps({
  addressableType: {
    type: String,
    required: false,
  },
  addressableId: {
    type: String,
    required: false,
  },
  self: {
    type: Boolean,
    default: false,
  },
});

const createRoute = computed(() => {
  if (props?.addressableType === 'users' && props?.self) {
    // User is creating an address for themselves.
    return { name: 'profile.addresses.create' };
  } else if (props?.addressableType && props?.addressableId) {
    // User is creating an address for another entity.
    return { name: `${props.addressableType}.addresses.create`, params: { id: props.addressableId } };
  }
  // Fallback to the default create route.
  return { name: 'addresses.create' };
});

const headerTitle = computed(() => {
  if (!props?.addressableType) return null;
  if (props?.addressableType === 'users') {
    if (props?.self) return 'My Addresses';
    return 'User Addresses';
  }
  if (props?.addressableType === 'companies') {
    return 'Company Addresses';
  }
  if (props?.addressableType === 'customers') {
    return 'Customer Addresses';
  }
  return null;
});

// Return the addressable ID based on the self prop and addressableType
const addressableId = computed(() => {
  if (!props?.self && !props?.addressableId) {
    return null;
  }
  if (props?.self && props?.addressableType === 'users') {
    const auth = useAuth(); // Only needed if self is true.
    return auth.user.locked_id;
  }
  return props.addressableId;
});

const { params, search, sort, paginate } = useFilterParams();

const { data: addresses, isLoading } =  ({
  'users': useUserAddresses,
  'companies': useCompanyAddresses,
  'customers': useCustomerAddresses,
}[props?.addressableType] || useAddresses)({
    params: params.value,
    id: addressableId.value,
})

</script>
