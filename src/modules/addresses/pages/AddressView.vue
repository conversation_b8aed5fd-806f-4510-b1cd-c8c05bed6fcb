<template>
  <div>
    <VPageHeader />
    <VContainer class="my-3">
      <TransitionFade>
        <div v-if="isAddressLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>
        <div v-else-if="address" class="grid gap-3 lg:grid-cols-5 items-start">
          <ViewPanel class="col-span-5 xl:col-span-4 order-2 xl:order-1">
            <PanelDefaultField label="Address Name" :value="address.name" allowCopy />
            <PanelDefaultField label="Related Entity" :value="address.addressable?.lookup" allowCopy>
              <template #default>
                <VEntityLink
                  :entityType="address.addressable_type"
                  :entityId="address.addressable?.locked_id"
                  :value="address.addressable?.lookup"
                />
              </template>
            </PanelDefaultField>
            <PanelDefaultField label="Address Line 1" :value="address.address_line_1" allowCopy />
            <PanelDefaultField label="Address Line 2" :value="address.address_line_2" allowCopy />
            <PanelDefaultField v-if="address.address_line_3" label="Address Line 3" :value="address.address_line_3" allowCopy />
            <PanelDefaultField v-if="address.address_line_4" label="Address Line 4" :value="address.address_line_4" allowCopy />
            <PanelDefaultField label="City" :value="address.city" allowCopy />
            <PanelDefaultField label="State / County / Region" :value="address.state_county_region" allowCopy />
            <PanelDefaultField label="Postcode / Zipcode" :value="address.postcode_zipcode" allowCopy />
            <PanelCountryField
              label="Country Based"
              :value="address?.country?.name"
              :countryId="address?.country?.locked_id"
              :iso2="address?.country?.iso_alpha_2"
              allowCopy
            />
            <PanelDefaultField label="Status" :value="address.status?.name">
              <template #default>
                <AddressStatusBadge :status="address?.status?.name" />
              </template>
            </PanelDefaultField>
            <PanelDateTimeField label="Date Created" :value="address.created_at" />
            <PanelDateTimeField label="Date Updated" :value="address.updated_at" />
          </ViewPanel>

          <VCard class="col-span-5 xl:col-span-1 order-1 xl:order-2">
            <VCardHeader>Actions</VCardHeader>
            <VCardBody class="flex gap-3 flex-wrap justify-center xl:justify-start">
              <VButton
                v-if="entityRouteName"
                intent="primary"
                as="router-link"
                :to="{
                  name: entityRouteName,
                  params: { id: address.addressable?.locked_id },
                }"
                >View {{ address.addressable_type }}
                <template #start>
                  <BIconSearch />
                </template>
              </VButton>

              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'addresses.index' }"
              >View All Addresses
                <template #start>
                  <BIconListUl />
                </template>
              </VButton>

              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'addresses.activity', params: { id: address.locked_id } }"
              >View Activity
                <template #start>
                  <BIconActivity />
                </template>
              </VButton>
              
              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <VButton intent="danger">
                    <template #start>
                      <BIconPencilSquare />
                    </template>
                    <template #end>
                      <BIconChevronDown />
                    </template>
                    Modify
                  </VButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{
                        name: 'addresses.update',
                        params: { id: address.locked_id },
                      }"
                      class="w-full"
                    >
                      <BIconPencilSquare />Edit
                    </RouterLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem @click="openAddressDeleteConfirmationDialog">
                    <BIconTrash />Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <VConfirmModal
                v-model:open="isAddressDeleteConfirmationDialog"
                @confirmed="deleteAddressMutation.mutate({ addressId: address.locked_id })"
                title="Confirm Address Deletion"
                :description="`<p>You're about to delete the address <b>${address?.name}</b> for the ${address?.addressable?.type.toLowerCase()} <b>${addressableLookup}</b>.</p><p>Please confirm you wish to proceed.</p>`"
                :icon="BIconTrashFill"
              />

            </VCardBody>
          </VCard>
        </div>
        <template v-else>
          <VEmptyState
            title="Address Not Found"
            description="The requested address was not found."
            :contact="true"
          />
        </template>
      </TransitionFade>
    </VContainer>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconSearch from '~icons/bi/search';
import BIconChevronDown from '~icons/bi/chevron-down';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconListUl from '~icons/bi/list-ul';
import BIconActivity from '~icons/bi/activity';
import ViewPanel from '@/components/ui/panels/ViewPanel.vue';
import PanelDefaultField from '@/components/ui/panels/fields/ViewPanel/PanelDefaultField.vue';
import PanelCountryField from '@/components/ui/panels/fields/ViewPanel/PanelCountryField.vue';
import PanelDateTimeField from '@/components/ui/panels/fields/ViewPanel/PanelDateTimeField.vue';
import AddressStatusBadge from '@/modules/addresses/components/AddressStatusBadge.vue';

import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';

import { useDisclosure } from '@/hooks';
import { Address } from '@/types';
import { useRouter } from 'vue-router';
import { useDeleteAddress } from '@/modules/addresses/api/delete-address';
import { useAddress } from '@/modules/addresses/api/get-address';

const { isOpen: isAddressDeleteConfirmationDialog, open: openAddressDeleteConfirmationDialog } = useDisclosure(false);

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const router = useRouter();

const { 
  data: address,
  isLoading: isAddressLoading
} = useAddress({ addressId: props.id });

const deleteAddressMutation = useDeleteAddress({
  mutationConfig: {
    onSuccess: () => {
      router.push({
        name: 'addresses.index',
      });
      Reshape.toast(
        {
          title: 'Address Deleted',
          message: `The requested address <b>${address.value.name}</b> for the ${address.value.addressable.type.toLowerCase()} <b>${
            address.value.addressable.lookup
          }</b> was successfully deleted.`,
        },
        'success'
      );
    },
  },
});

// Computed property to return an entity route name based on the entity type.
const entityRouteName = computed(() => {
  if (!address.value?.addressable_type) return false;

  const entityType = address.value.addressable_type.toLowerCase();
  if (entityType === 'user') {
    return 'users.view';
  } else if (entityType === 'company') {
    return 'companies.view';
  } else if (entityType === 'customer') {
    return 'customers.view';
  }
  return false;
});

const addressableLookup = computed(() => {
  return address.value.addressable?.lookup || 'Undefined';
});
</script>
