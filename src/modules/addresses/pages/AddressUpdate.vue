<template>
  <div>
    <VPageHeader>
      <template #actions>
        <VButton
          type="submit"
          form="address-form"
          intent="secondary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Update
        </VButton>
        <VButton
          @click="confirmModalOpen = true"
          intent="danger"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <TransitionFade>
        <div v-if="isAddressLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>
        <div v-else class="space-y-3">
          <AddressForm
            :address="address"
            :addressable-type="addressableType"
            :addressable-id="addressableId"
            @submit="submit"
          />
          <div class="flex w-full justify-start lg:justify-end space-x-3">
            <VButton
              type="submit"
              form="address-form"
              intent="primary"
            >
              <template #start>
                <BIconCheckLg />
              </template>
              Confirm
            </VButton>

            <VButton intent="danger" @click="confirmModalOpen = true">
              <template #start>
                <BIconArrowReturnLeft />
              </template>
              Cancel
            </VButton>
          </div>
        </div>
      </TransitionFade>
    </VContainer>

    <ConfirmModal
      v-model:open="confirmModalOpen"
      @confirmed="router.push({ name: returnRoute || 'addresses.index' })"
    />
  </div>
</template>

<script setup>
import ConfirmModal from '@/components/ui/modals/ConfirmModal.vue';
import AddressForm from '@/modules/addresses/components/AddressForm.vue';
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconCheckLg from '~icons/bi/check-lg';
import { ref } from 'vue';
import { useUpdateAddress } from '@/modules/addresses/api/put-address';
import { useAddress } from '@/modules/addresses/api/get-address';

const confirmModalOpen = ref(false);

const props = defineProps({
  id: {
    type: String,
    required: true,
  },

  addressableType: {
    type: String,
    required: false,
  },
  addressableId: {
    type: String,
    required: false,
  },
  self: {
    type: Boolean,
    default: false,
  },
});
const updateAddressMutation = useUpdateAddress({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Address Updated',
          message: props?.self
            ? `Your requested address was successfully updated.`
            : `Address for <b>${result.data?.addressable?.lookup}</b> was successfully updated.`,
        },
        'success'
      );
      router.push({ name: 'addresses.view', params: { id: result.data.locked_id } });
    },
  },
});

const {
  data: address,
  isLoading: isAddressLoading
} = useAddress({
  addressId: props.id
});

const submit = (form) => {
  form.submit((data) => updateAddressMutation.mutateAsync({
    addressId: props.id,
    data,
  }));
};

</script>
