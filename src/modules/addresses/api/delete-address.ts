import { useMutation, useQueryClient } from '@tanstack/vue-query';
import { MutationConfig } from '@/lib/vue-query';
import { getAddressesQueryOptions } from './get-addresses';

export const deleteAddress = ({ addressId }: { addressId: string }) => {
  return Reshape.http().delete(`/api/addresses/${addressId}`);
};

type UseDeleteAddressOptions = {
  mutationConfig?: MutationConfig<typeof deleteAddress>;
};

export const useDeleteAddress = ({ mutationConfig }: UseDeleteAddressOptions = {}) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['addresses', args[1].addressId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getAddressesQueryOptions().queryKey,
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteAddress,
  });
};
