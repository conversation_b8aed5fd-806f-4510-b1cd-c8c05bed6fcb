import { useMutation, useQueryClient } from '@tanstack/vue-query';
import { MutationConfig } from '@/lib/vue-query';
import { getAddressQueryOptions } from './get-address';
import { Address } from '@/types/api';

export const updateAddress = ({ addressId, data }: { addressId: string, data: UpdateAddressPayload }) => {
  return Reshape.http().put(`/api/addresses/${addressId}`, data);
};

type UpdateAddressPayload = Partial<Omit<Address, 'locked_id'>>;

type UseUpdateAddressOptions = {
  addressId: string;
  mutationConfig?: MutationConfig<typeof updateAddress>;
};

export const useUpdateAddress = ({ addressId, mutationConfig }: UseUpdateAddressOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['addresses', args[1].addressId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getAddressQueryOptions(args[1].addressId).queryKey,
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: updateAddress,
  });
};