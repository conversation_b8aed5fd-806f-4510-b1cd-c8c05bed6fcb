import { queryOptions, useQuery } from '@tanstack/vue-query';
import { QueryConfig } from '@/lib/vue-query';
import { Address, Meta } from '@/types/api';

export const getAddresses = (
  params,
  signal
): Promise<{
  data: Address[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/addresses`, {
    params,
    signal,
  });
};

export const getAddressesQueryOptions = ({ params }: { params?: unknown } = {}) => {
  return queryOptions({
    queryKey: params ? ['addresses', params] : ['addresses'],
    queryFn: ({ signal }) => getAddresses(params, signal),
  });
};

type UseAddressesOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getAddressesQueryOptions>;
};

export const useAddresses = ({ queryConfig, params }: UseAddressesOptions) => {
  return useQuery({
    ...getAddressesQueryOptions({ params }),
    ...queryConfig,
  });
};
