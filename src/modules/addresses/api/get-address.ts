import { useQuery, queryOptions } from '@tanstack/vue-query';
import { QueryConfig } from '@/lib/vue-query';
import { Address } from '@/types/api';

export const getAddress = ({ addressId }: { addressId: string }): Promise<{ data: Address }> => {
  return Reshape.http()
    .get(`/api/addresses/${addressId}`)
    .then(({ data }) => data);
};

export const getAddressQueryOptions = (addressId: string) => {

  return queryOptions({
    queryKey: ['addresses', addressId],
    queryFn: () => getAddress({ addressId }),
  });
};

type UseAddressOptions = {
  addressId: string;
  queryConfig?: QueryConfig<typeof getAddressQueryOptions>;
};

export const useAddress = ({ addressId, queryConfig }: UseAddressOptions) => {
  return useQuery({
    ...getAddressQueryOptions(addressId),
    ...queryConfig,
  });
};
