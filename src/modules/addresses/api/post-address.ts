import { useMutation, useQueryClient } from '@tanstack/vue-query';
import { MutationConfig } from '@/lib/vue-query';
import { Address } from '@/types/api';

export const createAddress = ({ data }: { data: CreateAddressPayload }) => {
  return Reshape.http().post(`/api/addresses`, data);
};

type CreateAddressPayload = Omit<Address, 'id' | 'locked_id'>;

type UseCreateAddressOptions = {
  mutationConfig?: MutationConfig<typeof createAddress>;
};

export const useCreateAddress = ({ mutationConfig }: UseCreateAddressOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ['addresses'],
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createAddress,
  });
};