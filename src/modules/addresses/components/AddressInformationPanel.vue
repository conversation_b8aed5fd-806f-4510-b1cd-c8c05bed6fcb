<template>
  <VInformationPanel class="rounded-md" :loading="isAddressLoading">
    <VDefaultField label="Name / Nickname" :value="address.name" />
    <VDefaultField label="Entity Type" :value="address.addressable_type" />
    <VDefaultField label="Entity Name">
      <span v-if="address.addressable">{{ address.addressable.lookup }}</span>
      <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
    </VDefaultField>
    <VDefaultField label="Address Line 1" :value="address.address_line_1" />
    <VDefaultField label="City" :value="address.city" />
    <VDefaultField label="State / County / Region" :value="address.state_county_region" />
    <VDefaultField label="Postcode / Zipcode" :value="address.postcode_zipcode" />
    <VCountryField
      label="Country"
      :value="address.country?.name"
      :countryId="address.country?.locked_id"
      :iso2="address.country?.iso_alpha_2"
    />
    <VTimeField label="Created At" :value="address.created_at" />
    <VTimeField label="Updated At" :value="address.updated_at" />
  </VInformationPanel>
</template>

<script setup>
import { useAddress } from '@/modules/addresses/api/get-address';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const { 
  data: address,
  isLoading: isAddressLoading
} = useAddress({ addressId: props.id });

</script>
