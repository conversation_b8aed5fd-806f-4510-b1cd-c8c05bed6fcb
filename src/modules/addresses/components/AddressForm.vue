<template>
  <form id="address-form" class="space-y-3" @submit.prevent="submit">
    <FormContainer
      v-if="!addressableType && !addressableId"
      title="Entity Reference"
      description="Detail whom or what this address should be tied to."
      :icon="BIconInfoCircleFill"
    >
      <template #body>
        <div class="grid grid-cols-6 gap-6">
          <VField
            class="col-span-6 lg:col-span-3 xl:col-span-2"
            :errors="form.errors.get('addressable_type')"
            label="Entity"
            help="Addresses are tied to specific entities, such as users or companies."
            required
          >
            <EntityCombobox
              v-model="form.addressable_type"
              value-prop="name"
              @update:modelValue="
                () => {
                  form.addressable_id = '';
                }
              "
            />
          </VField>

          <VField
            :label="form.addressable_type ? form.addressable_type + ' Reference' : 'Entity Reference'"
            class="col-span-6 lg:col-span-3 xl:col-span-4"
            required
            :errors="form.errors.get('addressable_id')"
            :help="form.addressable_type
              ? `Select a ${form.addressable_type} reference this record should be tied to.`
              : 'Based on the entity selected, select the record this address should be tied to.'
            "
          >
            <TransitionFadeLeftToRight>
              <div>
                <span
                  v-if="!form.addressable_type"
                  class="mt-2.5 inline-block select-none italic text-platinum-600 dark:text-midnight-200"
                >Select Entity to continue.</span>
                <UserCombobox v-else-if="form.addressable_type === 'User'" v-model="form.addressable_id" />
                <CompanyCombobox v-else-if="form.addressable_type === 'Company'" v-model="form.addressable_id" />
                <CustomerCombobox v-else-if="form.addressable_type === 'Customer'" v-model="form.addressable_id" />
                <span
                  v-else
                  class="mt-2 inline-block select-none italic text-platinum-500 dark:text-midnight-100"
                >Selection Invalid</span>
              </div>
            </TransitionFadeLeftToRight>
          </VField>
        </div>
      </template>
    </FormContainer>

    <TransitionFade>
      <FormContainer
        v-if="(addressableType && addressableId) || (form.addressable_type && form.addressable_id)"
        title="Address Details"
        description="Provide details about the address in this section."
        :icon="BIconJournalBookmarkFill"
      >
        <template #body>
          <TransitionFade>
            <div>
              <span v-if="!form.addressable_type" class="inline-block select-none text-platinum-600 dark:text-midnight-200">
                Entity selection is required to continue.
              </span>
              <div v-else class="grid grid-cols-6 gap-6">
                <VField
                  label="Address Name / Nickname"
                  class="col-span-6 sm:col-span-4 xl:col-span-5"
                  required
                  :errors="form.addressable_type && form.addressable_id ? form.errors.get('name') : []"
                >
                  <VInput v-model="form.name" placeholder="Enter Address Name / Nickname" />
                </VField>

                <VField
                  label="Primary Address"
                  class="col-span-6 sm:col-span-2 xl:col-span-1"
                  help="Toggling will switch any existing primary address to secondary."
                  required
                >
                  <VToggle v-model="form.is_primary" class="mt-2.5">
                    <span class="text-platinum-800 dark:text-midnight-100">
                      {{ form.is_primary ? 'Primary' : 'Secondary' }}</span
                    >
                  </VToggle>
                </VField>
                
                <VField
                  class="col-span-6"
                  label="Address Line 1"
                  required
                  :errors="form.addressable_type && form.addressable_id ? form.errors.get('address_line_1') : []"
                >
                  <VInput
                    v-model="form.address_line_1"
                    placeholder="Enter Address Line 1"
                    required
                  />
                </VField>

                <VField
                  class="col-span-6"
                  label="Address Line 2"
                  :errors="form.addressable_type && form.addressable_id ? form.errors.get('address_line_2') : []"
                >
                  <VInput
                    v-model="form.address_line_2"
                    placeholder="Enter Address Line 2"
                  />
                </VField>

                <TransitionFade>
                  <VField
                    v-if="form.address_line_2 || form.address_line_3"
                    class="col-span-6"
                    label="Address Line 3"
                    :errors="form.addressable_type && form.addressable_id ? form.errors.get('address_line_3') : []"
                  >
                    <VInput
                      v-model="form.address_line_3"
                      placeholder="Enter Address Line 3"
                    />
                  </VField>
                </TransitionFade>

                <TransitionFade>
                  <VField
                    v-if="form.address_line_3 || form.address_line_4"
                    class="col-span-6"
                    label="Address Line 4"
                    :errors="form.addressable_type && form.addressable_id ? form.errors.get('address_line_4') : []"
                  >
                    <VInput
                      v-model="form.address_line_4"
                      placeholder="Enter Address Line 4"
                    />
                  </VField>
                </TransitionFade>

                <VField
                  class="col-span-6 md:col-span-3"
                  label="City"
                  required
                  :errors="form.addressable_type && form.addressable_id ? form.errors.get('city') : []"
                >
                  <VInput v-model="form.city" placeholder="Enter City" />
                </VField>
                
                <VField
                  class="col-span-6 md:col-span-3"
                  label="State / County / Region"
                  :errors="form.addressable_type && form.addressable_id ? form.errors.get('state_county_region') : []"
                  required
                >
                  <VInput v-model="form.state_county_region" placeholder="Enter State / County / Region" />
                </VField>
                
                <VField
                  class="col-span-6 md:col-span-3"
                  label="Postcode / Zipcode"
                  required
                  :errors="form.addressable_type && form.addressable_id ? form.errors.get('postcode_zipcode') : []"
                >
                  <VInput v-model="form.postcode_zipcode" placeholder="Enter Postcode / Zipcode" required />
                </VField>
                
                <VField
                  class="col-span-6 md:col-span-3"
                  label="Country"
                  required
                  :errors="form.addressable_type && form.addressable_id ? form.errors.get('country_id') : []"
                >
                  <CountryCombobox v-model="form.country_id" />
                </VField>
              </div>
            </div>
          </TransitionFade>
        </template>
      </FormContainer>
    </TransitionFade>

    <TransitionFade>
      <FormContainer
        v-if="(addressableType && addressableId) || (form.addressable_type && form.addressable_id)"
        title="Admin Options"
        description="Additional admin values for the address."
        :icon="BIconBookmarkStarFill"
      >
        <template #body>
          <div class="grid grid-cols-6 gap-6">
            <VField
              class="col-span-6 md:col-span-3"
              label="Address Status"
              help="Defines whether the address has been verified, etc."
              :errors="form.addressable_type && form.addressable_id ? form.errors.get('status_id') : []"
              required
            >
              <StatusCombobox
                v-model="form.status_id"
                statusable="Address"
                :default-option="address ? null : 'Active'"
              />
            </VField>

            <VField
              class="col-span-6"
              label="Notes"
              help="Any related notes about the address."
              :errors="form.addressable_type && form.addressable_id ? form.errors.get('note') : []"
            >
              <VTextarea v-model="form.note" placeholder="Enter Notes" />
            </VField>
          </div>
        </template>
      </FormContainer>
    </TransitionFade>
  </form>
</template>

<script setup>
import { computed } from 'vue';
import { useForm } from '@/hooks/form';
import EntityCombobox from '@/components/ui/form/composables/EntityCombobox.vue';
import UserCombobox from '@/components/ui/form/composables/UserCombobox.vue';
import CompanyCombobox from '@/components/ui/form/composables/CompanyCombobox.vue';
import StatusCombobox from '@/components/ui/form/composables/StatusCombobox.vue';
import CountryCombobox from '@/components/ui/form/composables/CountryCombobox.vue';
import CustomerCombobox from '@/components/ui/form/composables/CustomerCombobox.vue';

import FormContainer from '@/components/ui/forms/FormContainer.vue';
import BIconInfoCircleFill from '~icons/bi/info-circle-fill';
import BIconJournalBookmarkFill from '~icons/bi/journal-bookmark-fill';
import BIconBookmarkStarFill from '~icons/bi/bookmark-star-fill';

const props = defineProps({
  address: {
    type: Object,
    default: null,
  },
  // Default values if any.
  addressableType: {
    type: String,
    default: null,
  },
  addressableId: {
    type: String,
    default: null,
  },
});

const emits = defineEmits(['submit']);

const submit = () => {
  emits('submit', form.value);
};

const addressableTypeSingular = computed(() => {
  if (props.addressableType === 'users') return 'User';
  if (props.addressableType === 'companies') return 'Company';
  if (props.addressableType === 'customers') return 'Customer';
  return null;
});

const form = useForm({
  addressable_type: (
    addressableTypeSingular.value ||
    props.address?.addressable_type
  ) ?? '',
  addressable_id: (
    props.addressableId ||
    props.address?.addressable?.locked_id
  ) ?? '',
  name: props.address?.name ?? '',
  is_primary: props.address?.is_primary ?? false,
  address_line_1: props.address?.address_line_1 ?? '',
  address_line_2: props.address?.address_line_2 ?? '',
  address_line_3: props.address?.address_line_3 ?? '',
  address_line_4: props.address?.address_line_4 ?? '',
  city: props.address?.city ?? '',
  state_county_region: props.address?.state_county_region ?? '',
  postcode_zipcode: props.address?.postcode_zipcode ?? '',
  country_id: props.address?.country?.locked_id ?? '',
  status_id: props.address?.status?.locked_id ?? '',
  note: props.address?.note ?? '',
});

</script>
