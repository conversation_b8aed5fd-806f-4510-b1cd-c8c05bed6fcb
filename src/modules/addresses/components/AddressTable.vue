<template>
  <Table
    v-bind="$attrs"
    :columns="columns"
    :default-sort="[{
      id: 'created_at',
      desc: true,
    }]"
    paginated
    sortable
    searchable
    controlled />
</template>

<script setup lang="ts">
import { computed, h } from 'vue';
import type { ColumnDef } from '@tanstack/vue-table';
import type { Address } from '@/types/api';
import Table from '@/components/ui/tables/Table.vue';
import ActionCell from './AddressTableActionCell.vue';
import AddressStatusBadge from '@/modules/addresses/components/AddressStatusBadge.vue';
import { humanizeTime } from '@/util/dateUtils';
import EntityLink from '@/components/ui/links/EntityLink.vue';
import TableCell from '@/components/ui/tables/TableCell.vue';


const props = defineProps({
  addressableType: {
    type: String,
    required: false,
  },
});

const columns = computed<ColumnDef<Address>[]>(() => {
  const baseColumns = [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: (info) => {
        return h(EntityLink, {
          value: info.row?.original?.name,
          entityType: 'Address',
          entityId: info.row?.original?.locked_id,
          showIcon: false,
          valueClass: 'max-w-32 sm:max-w-48 md:max-w-52 lg:max-w-64 xl:max-w-72 truncate',
        });
      },
    },
    {
      accessorKey: 'created_at',
      header: 'Created Date',
      cell: (info) => humanizeTime(info.getValue()),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: (info) => h(AddressStatusBadge, { status: info.row.original.status?.name, key: info.row.original.locked_id }),
    },
    {
      id: 'actions',
      header: () => h('span', { class: 'w-full flex justify-end' }),
      cell: (info) => h(ActionCell, { row: info.row.original, key: info.row.original.locked_id }),
      enableSorting: false,
      enableResizing: false,
      size: 50,
    },
  ];

  if (!props.addressableType) {
    baseColumns.splice(1, 0, {
      accessorKey: 'addressable.type',
      header: 'Owning Entity',
      cell: (info) => {
        return h(EntityLink, {
          value: info.row?.original?.addressable?.lookup,
          entityType: info.row?.original?.addressable?.type,
          entityId: info.row?.original?.addressable?.locked_id,
          valueClass: 'max-w-32 sm:max-w-48 md:max-w-52 lg:max-w-64 xl:max-w-72 truncate',
        });
      },
    });
  }

  return baseColumns;
});
</script>
