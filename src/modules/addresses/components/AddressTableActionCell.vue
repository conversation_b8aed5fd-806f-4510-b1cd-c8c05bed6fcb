<template>
  <div class="flex items-center justify-end space-x-1.5">
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <VButton intent="table">
          <BIconThreeDots class="h-5 w-5" />
        </VButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem as-child>
          <RouterLink
            :to="{
              name: 'addresses.view',
              params: { id: row.locked_id },
            }"
          >
            <BIconSearch />View
          </RouterLink>
        </DropdownMenuItem>
        <DropdownMenuGroup>
          <DropdownMenuLabel>Modify</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem as-child>
            <RouterLink
              :to="{
                name: 'addresses.update',
                params: { id: row.locked_id },
              }"
              class="w-full"
            >
              <BIconPencilSquare />Edit
            </RouterLink>
          </DropdownMenuItem>
          <DropdownMenuItem @click="openAddressDeleteConfirmationDialog">
            <BIconTrash />Delete
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>

    <VConfirmModal
      v-model:open="isAddressDeleteConfirmationDialog"
      @confirmed="deleteAddressMutation.mutate({
        addressId: props.row.locked_id
      })"
      title="Confirm Address Deletion"
      :description="`<p>You're about to delete the requested address <b>${props.row.name}</b> for the
        ${props.row.addressable.type.toLowerCase()} <b>${addressableLookup}</b>.</p><p>Please confirm you wish to proceed.</p>`"
      :icon="BIconTrashFill"
    />
  </div>
</template>

<script setup lang="ts">
import BIconSearch from '~icons/bi/search';
import BIconThreeDots from '~icons/bi/three-dots';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconTrashFill from '~icons/bi/trash-fill';
import { computed } from 'vue';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/shadcn/dropdown-menu';
import { useDisclosure } from '@/hooks';
import type { CellContext } from '@tanstack/vue-table';
import type { Address } from '@/types/api';
import { useDeleteAddress } from '../api/delete-address';
import { identity } from 'lodash';

export type ActionCellProps = {
  row: CellContext<Address, unknown>;
};

const props = defineProps<ActionCellProps>();

const addressableLookup = computed(() => {
  return props.row.addressable?.lookup || 'Undefined';
});

const deleteAddressMutation = useDeleteAddress({
  mutationConfig: {
    onSuccess: () => {
      Reshape.toast(
        {
          title: 'Address Deleted',
          message: `The requested address <b>${props.row?.name}</b> for the ${props.row?.addressable?.type.toLowerCase()} <b>${
            props.row?.addressable?.lookup
          }</b> was successfully deleted.`,
        },
        'success'
      );
    },
  },
});

const {
  isOpen: isAddressDeleteConfirmationDialog,
  open: openAddressDeleteConfirmationDialog
} = useDisclosure(false);
</script>