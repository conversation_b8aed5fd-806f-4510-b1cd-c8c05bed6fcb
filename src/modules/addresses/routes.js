import UserLayout from '@/layouts/UserLayout.vue';
import { auth } from '@/router/middleware';

const routes = [
  {
    path: '/addresses',
    component: UserLayout,
    meta: {
      middleware: auth,
    },
    children: [
      {
        path: '',
        name: 'addresses.index',
        component: () => import('./pages/AddressIndex.vue'),
        meta: {
          title: 'Addresses',
          headerTitle: 'Addresses',
          headerDescription: 'Overview of all relevant addresses.',
          breadcrumbs: [{ label: 'Addresses', name: 'addresses.index' }],
        },
      },
      {
        path: ':id/view',
        name: 'addresses.view',
        component: () => import('./pages/AddressView.vue'),
        props: true,
        meta: {
          title: 'View Address',
          headerTitle: 'View Address',
          headerDescription: 'Overview of selected address.',
          breadcrumbs: [
            { label: 'Addresses', name: 'addresses.index' },
            { label: 'View', name: 'addresses.view' },
          ],
        },
      },
      {
        path: 'add',
        name: 'addresses.create',
        component: () => import('./pages/AddressCreate.vue'),
        meta: {
          title: 'Add Address',
          headerTitle: 'Add Address',
          headerDescription: 'Define a new address.',
          breadcrumbs: [
            { label: 'Addresses', name: 'addresses.index' },
            { label: 'Add', name: 'addresses.create' },
          ],
        },
      },
      {
        path: ':id/edit',
        name: 'addresses.update',
        component: () => import('./pages/AddressUpdate.vue'),
        props: true,
        meta: {
          title: 'Update Address',
          headerTitle: 'Update Address',
          headerDescription: 'Update an existing address defined in the system.',
          breadcrumbs: [
            { label: 'Addresses', name: 'addresses.index' },
            { label: 'Edit', name: 'addresses.update' },
          ],
        },
      },
      {
        path: ':id/activity',
        name: 'addresses.activity',
        component: () => import('@/modules/activity/pages/ActivityIndex.vue'),
        props: (route) => ({
          activitableType: 'Address',
          activitableId: route.params.id,
        }),
        meta: {
          title: 'Address Activity',
          headerTitle: 'Address Activity',
          headerDescription: 'View the activity for this address.',
          breadcrumbs: [
            { label: 'Addresses', name: 'addresses.index' },
            { label: 'View', name: 'addresses.view' },
            { label: 'Activity', name: 'addresses.activity' },
          ],
        },
      },
    ],
  },
];

export default routes;
