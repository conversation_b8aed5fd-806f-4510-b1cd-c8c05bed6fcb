<template>
  <div class="flex items-center justify-end space-x-1.5">
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <VButton intent="table">
          <BIconThreeDots class="h-5 w-5" />
        </VButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem as-child>
          <RouterLink
            :to="{
              name: 'contacts.view',
              params: { id: row.locked_id },
            }"
          >
            <BIconSearch />View
          </RouterLink>
        </DropdownMenuItem>
        <DropdownMenuItem as-child>
          <RouterLink
            :to="{
              name: 'contacts.activity',
              params: { id: row.locked_id },
            }"
          >
            <BIconActivity />Activity
          </RouterLink>
        </DropdownMenuItem>
        <DropdownMenuGroup>
          <DropdownMenuLabel>Modify</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem as-child>
            <RouterLink
              :to="{
                name: 'contacts.update',
                params: { id: row.locked_id },
              }"
              class="w-full"
            >
              <BIconPencilSquare />Edit
            </RouterLink>
          </DropdownMenuItem>
          <DropdownMenuItem @click="openContactDeleteConfirmationDialog">
            <BIconTrash />Delete
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>

    <VConfirmModal
      v-model:open="isContactDeleteConfirmationDialog"
      @confirmed="deleteContactMutation.mutate({
        contactId: props.row.locked_id
      })"
      title="Confirm Contact Deletion"
      :description="`<p>You're about to delete the requested ${props.row.method.name} contact for the
        ${props.row.contactable.type.toLowerCase()}: <b>${contactableLookup}</b>.</p><p>Please confirm you wish to proceed.</p>`"
      :icon="BIconTrashFill"
    />
  </div>
</template>

<script setup lang="ts">
import BIconSearch from '~icons/bi/search';
import BIconThreeDots from '~icons/bi/three-dots';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconActivity from '~icons/bi/activity';
import { computed } from 'vue';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/shadcn/dropdown-menu';
import { useDisclosure } from '@/hooks';
import type { CellContext } from '@tanstack/vue-table';
import type { Contact } from '@/types/api';
import { useDeleteContact } from '../api/delete-contact';

export type ActionCellProps = {
  row: CellContext<Contact, unknown>;
};

const props = defineProps<ActionCellProps>();

const contactableLookup = computed(() => {
  return props.row.contactable?.lookup || 'Undefined';
});

const deleteContactMutation = useDeleteContact({
  mutationConfig: {
    onSuccess: () => {
      Reshape.toast(
        {
          title: 'Contact Deleted',
          message: `The requested contact for the ${props.row.contactable.type.toLowerCase()} <b>${
            props.row.contactable.lookup
          }</b> was successfully deleted.`,
        },
        'success'
      );
    },
  },
});

const {
  isOpen: isContactDeleteConfirmationDialog,
  open: openContactDeleteConfirmationDialog
} = useDisclosure(false);
</script>