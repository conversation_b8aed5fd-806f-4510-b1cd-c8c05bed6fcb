<template>
  <VInformationPanel class="rounded-md" :loading="isContactLoading">
    <VDefaultField label="Contact Method">
      <span v-if="contact.method">{{ contact.method.name }}</span>
      <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
    </VDefaultField>
    <VDefaultField label="Value" :value="contact.value" />
    <VBooleanField label="Is Primary" :value="contact.is_primary" />
    <VDefaultField label="Note" :value="contact.note" />
    <VTimeField label="Created At" :value="contact.created_at" />
    <VTimeField label="Updated At" :value="contact.updated_at" />
    <!-- <VDefaultField label="Contactable Reference">
      <template v-if="contact.contactable">
        <span class="italic">{{ contact.contactable.type }}</span> -
        <VTooltip class="inline-block">
          <RouterLink
            class="underline"
            :to="{
              name: { User: 'users.view' }[contact.contactable.type],
              params: {
                id: contact.contactable.locked_id,
              },
            }"
          >
            {{ contact.contactable.lookup }}
          </RouterLink>
          <template #content>
            <UserInformationPanel :id="contact.contactable.locked_id" />
          </template>
        </VTooltip>
      </template>
      <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
    </VDefaultField> -->
  </VInformationPanel>
</template>

<script setup>
import { useContact } from '@/modules/contacts/api/get-contact';
// import UserInformationPanel from '@/modules/users/components/UserInformationPanel.vue';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const { 
  data: contact,
  isLoading: isContactLoading
} = useContact({ contactId: props.id });


</script>
