<template>
  <form id="contact-form" class="space-y-3" @submit.prevent="submit">
    <FormContainer
      v-if="!contactableType && !contactableId"
      title="Entity Reference"
      description="Detail whom or what this contact / social media reference should be tied to."
      :icon="BIconInfoCircleFill"
    >
      <template #body>
        <div class="grid grid-cols-6 gap-6">
          <VField
            class="col-span-6 lg:col-span-3 xl:col-span-2"
            :errors="form.errors.get('contactable_type')"
            label="Entity"
            help="Contacts are tied to specific entities, such as users or companies."
            required
          >
            <EntityCombobox
              v-model="form.contactable_type"
              value-prop="name"
              @update:modelValue="
                () => {
                  form.contactable_id = '';
                  form.method_id = '';
                  isSocial = false;
                  if (editInitialLoad) {
                    form.is_primary = false;
                  }
                  if (contact !== null) {
                    editInitialLoad = true;
                  }
                }
              "
            />
          </VField>

          <VField
            :label="form.contactable_type ? form.contactable_type + ' Reference' : 'Entity Reference'"
            class="col-span-6 lg:col-span-3 xl:col-span-4"
            required
            :errors="form.contactable_type ? form.errors.get('contactable_id') : []"
            :help="
              form.contactable_type
                ? `Select a ${form.contactable_type} reference this record should be tied to.`
                : 'Based on the entity selected, select the record this contact should be tied to.'
            "
          >
            <TransitionFadeLeftToRight>
              <span v-if="!form.contactable_type" class="mt-2.5 inline-block select-none italic text-platinum-600 dark:text-midnight-200"
                >Select Entity to continue.</span
              >
              <UserCombobox v-else-if="form.contactable_type === 'User'" v-model="form.contactable_id" />
              <CompanyCombobox v-else-if="form.contactable_type === 'Company'" v-model="form.contactable_id" />
              <CustomerCombobox v-else-if="form.contactable_type === 'Customer'" v-model="form.contactable_id" />
              <span
                v-else
                class="mt-2 inline-block select-none italic text-platinum-500 dark:text-midnight-100"
              >Selection Invalid</span>
            </TransitionFadeLeftToRight>
          </VField>
        </div>
      </template>
    </FormContainer>

    <TransitionFade>
      <FormContainer
        v-if="(contactableType && contactableId) || (form.contactable_type && form.contactable_id)"
        title="Contact Details"
        description="Provide details about the contact in this section."
        :icon="BIconPersonVCardFill"
      >
        <template #body>
          <TransitionFade>
            <span v-if="!form.contactable_type" class="inline-block select-none text-platinum-600 dark:text-midnight-200">
              Entity selection is required to continue.
            </span>
            <div v-else class="grid grid-cols-6 gap-6">
              <VField
                :class="!isSocial ? 'col-span-6 sm:col-span-4 xl:col-span-5' : 'col-span-6'"
                required
                :errors="form.contactable_type && form.contactable_id ? form.errors.get('method_id') : []"
                label="Contact Method"
              >
                <ContactMethodCombobox
                  v-model="form.method_id"
                  @update:rawValue="
                    ($event) => {
                      if ($event.is_social) {
                        form.is_primary = false;
                      }
                      setIsSocialRef($event);
                    }
                  "
                  :contactable="form.contactable_type"
                  :social="null"
                />
              </VField>

              <VField
                v-if="!isSocial"
                label="Primary Contact"
                class="col-span-6 sm:col-span-2 xl:col-span-1"
                help="Toggling will switch any existing primary contact to secondary. Only non-social contacts can be primary contact methods."
                :errors="form.contactable_type && form.contactable_id ? form.errors.get('is_primary') : []"
                required
              >
                <VToggle v-model="form.is_primary" class="mt-2.5">
                  <span class="text-platinum-800 dark:text-midnight-100">
                    {{ form.is_primary ? 'Primary' : 'Secondary' }}
                  </span>
                </VToggle>
              </VField>

              <VField
                class="col-span-6"
                label="Contact Value"
                required
                :errors="form.contactable_type && form.contactable_id ? form.errors.get('value') : []"
              >
                <VInput v-model="form.value" placeholder="Enter Contact Value" label="Contact Value" />
              </VField>

              <VField
                class="col-span-6"
                label="Contact Note"
                help="Additional notes about a contact. Example; telephone line extension or receptionist name."
                :errors="form.contactable_type && form.contactable_id ? form.errors.get('note') : []"
              >
                <VTextarea v-model="form.note" placeholder="Enter Contact Value" />
              </VField>
            </div>
          </TransitionFade>
        </template>
      </FormContainer>
    </TransitionFade>

    <TransitionFade>
      <FormContainer
        v-if="(contactableType && contactableId) || (form.contactable_type && form.contactable_id)"
        title="Admin Options"
        description="Additional options specifically suited for admin usage."
        :icon="BIconBookmarkStarFill"
      >
        <template #body>
          <div class="grid grid-cols-6 gap-6">
            <VField
              class="col-span-6 sm:col-span-4 xl:col-span-3"
              label="Contact Status"
              help="Defines whether the contact has been verified, etc."
              :errors="form.contactable_type && form.contactable_id ? form.errors.get('status_id') : []"
              required
            >
              <StatusCombobox
                v-model="form.status_id"
                statusable="Contact"
                :default-option="contact ? null : 'Active'"
              />
            </VField>
          </div>
        </template>
      </FormContainer>
    </TransitionFade>
  </form>
</template>

<script setup>
import { useForm } from '@/hooks/form';
import { ref, computed } from 'vue';
import EntityCombobox from '@/components/ui/form/composables/EntityCombobox.vue';
import UserCombobox from '@/components/ui/form/composables/UserCombobox.vue';
import CompanyCombobox from '@/components/ui/form/composables/CompanyCombobox.vue';
import CustomerCombobox from '@/components/ui/form/composables/CustomerCombobox.vue';
import ContactMethodCombobox from '@/components/ui/form/composables/ContactMethodCombobox.vue';
import StatusCombobox from '@/components/ui/form/composables/StatusCombobox.vue';
import FormContainer from '@/components/ui/forms/FormContainer.vue';
import BIconInfoCircleFill from '~icons/bi/info-circle-fill';
import BIconPersonVCardFill from '~icons/bi/person-vcard-fill';
import BIconBookmarkStarFill from '~icons/bi/bookmark-star-fill';

const props = defineProps({
  contact: {
    type: Object,
    default: null,
  },
  contactableType: {
    type: String,
    required: false,
  },
  contactableId: {
    type: String,
    required: false,
  },
});

const isSocial = ref(false);
const editInitialLoad = ref(false);
const emits = defineEmits(['submit']);

const submit = () => emits('submit', form.value);

const contactableTypeSingular = computed(() => {
  if (props.contactableType === 'users') return 'User';
  if (props.contactableType === 'companies') return 'Company';
  if (props.contactableType === 'customers') return 'Customer';
  return null;
});

const form = useForm({
  contactable_type: (
    contactableTypeSingular.value ||
    props.contact?.contactable_type
  ) ?? '',
  contactable_id: (
    props.contactableId ||
    props.contact?.contactable?.locked_id
  ) ?? '',
  method_id: props.contact?.method.locked_id ?? '',
  is_primary: props.contact?.is_primary ?? false,
  value: props.contact?.value ?? '',
  note: props.contact?.note ?? '',
  status_id: props.contact?.status?.locked_id ?? '',
});

const setIsSocialRef = (data) => {
  isSocial.value = data?.is_social ?? false;
};
</script>
