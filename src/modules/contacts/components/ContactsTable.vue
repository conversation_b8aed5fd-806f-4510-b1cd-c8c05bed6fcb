<template>
  <Table
    v-bind="$attrs"
    :columns="columns"
    :default-sort="[{
      id: 'created_at',
      desc: true,
    }]"
    paginated
    sortable
    searchable
    controlled />
</template>

<script setup lang="ts">
import { computed, h } from 'vue';
import type { ColumnDef } from '@tanstack/vue-table';
import type { Contact } from '@/types/api';
import Table from '@/components/ui/tables/Table.vue';
import ActionCell from './ContactsTableActionCell.vue';
import ContactLink from '@/components/ui/links/ContactLink.vue';
import ContactStatusBadge from '@/modules/contacts/components/ContactStatusBadge.vue';
import { humanizeTime } from '@/util/dateUtils';
import EntityLink from '@/components/ui/links/EntityLink.vue';

const props = defineProps({
  contactableType: {
    type: String,
    required: true,
  },
});

const columns = computed<ColumnDef<Contact>[]>(() => {
  const baseColumns = [
    {
      accessorKey: 'method.name',
      id: 'method.name',
      header: () => h('span', { class: 'text-nowrap' }, 'Contact Method'),
    },
    {
      accessorKey: 'value',
      header: 'Value',
      cell: (info) => h(ContactLink, { value: info.row.original.value, method: info.row.original.method.name }),
    },
    {
      accessorKey: 'created_at',
      header: 'Created Date',
      cell: (info) => humanizeTime(info.getValue()),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: (info) => h(ContactStatusBadge, { status: info.row.original.status?.name, key: info.row.original.locked_id }),
    },
    {
      id: 'actions',
      header: () => h('span', { class: 'w-full flex justify-end' }),
      cell: (info) => h(ActionCell, { row: info.row.original, key: info.row.original.locked_id }),
      enableSorting: false,
      enableResizing: false,
      size: 50,
    },
  ];

  if (!props.contactableType) {
    baseColumns.unshift({
      accessorKey: 'contactable.type',
      header: 'Owning Entity',
      cell: (info) => {
        return h(EntityLink, {
          value: info.row?.original?.contactable?.lookup,
          entityType: info.row?.original?.contactable?.type,
          entityId: info.row?.original?.contactable?.locked_id,
          showAvatar: true,
          avatarSize: 'sm',
          showEntityType: true,
        });
      },
    });
  }

  return baseColumns;
});
</script>
