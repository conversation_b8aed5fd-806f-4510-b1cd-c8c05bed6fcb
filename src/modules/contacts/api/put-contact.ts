import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getContactQueryOptions } from './get-contact';

import { Contact } from '@/types/api';

export const updateContact = ({ contactId, data }: { contactId: string, data: UpdateContactPayload }) => {
  return Reshape.http().put(`/api/contacts/${contactId}`, data);
};

type UpdateContactPayload = Partial<Omit<Contact, 'locked_id'>>;

type UseUpdateContactOptions = {
  contactId: string;
  mutationConfig?: MutationConfig<typeof updateContact>;
};

export const useUpdateContact = ({ contactId, mutationConfig }: UseUpdateContactOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['contacts', args[1].contactId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getContactQueryOptions(args[1].contactId).queryKey,
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: updateContact,
  });
};