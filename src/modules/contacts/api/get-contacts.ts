import { queryOptions, useQuery } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Contact, Meta } from '@/types/api';

export const getContacts = (
  params,
  signal
): Promise<{
  data: Contact[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/contacts`, {
    params,
    signal,
  });
};

export const getContactsQueryOptions = ({ params }: { params?: unknown } = {}) => {
  return queryOptions({
    queryKey: params ? ['contacts', params] : ['contacts'],
    queryFn: ({ signal }) => getContacts(params, signal),
  });
};

type UseContactsOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getContactsQueryOptions>;
};

export const useContacts = ({ queryConfig, params }: UseContactsOptions) => {
  return useQuery({
    ...getContactsQueryOptions({ params }),
    ...queryConfig,
  });
};
