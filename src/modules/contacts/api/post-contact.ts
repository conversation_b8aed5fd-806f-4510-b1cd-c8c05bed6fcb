import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { Contact } from '@/types/api';

export const createContact = ({ data }: { data: CreateContactPayload }) => {
  return Reshape.http().post(`/api/contacts`, data);
};

type CreateContactPayload = Omit<Contact, 'id' | 'locked_id'>;

type UseCreateContactOptions = {
  mutationConfig?: MutationConfig<typeof createContact>;
};

export const useCreateContact = ({ mutationConfig }: UseCreateContactOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ['contacts'],
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createContact,
  });
};