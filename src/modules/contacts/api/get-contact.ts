import { useQuery, queryOptions } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Contact } from '@/types/api';

export const getContact = ({ contactId }: { contactId: string }): Promise<{ data: Contact }> => {
  return Reshape.http()
    .get(`/api/contacts/${contactId}`)
    .then(({ data }) => data);
};

export const getContactQueryOptions = (contactId: string) => {

  return queryOptions({
    queryKey: ['contacts', contactId],
    queryFn: () => getContact({ contactId }),
  });
};

type UseContactOptions = {
  contactId: string;
  queryConfig?: QueryConfig<typeof getContactQueryOptions>;
};

export const useContact = ({ contactId, queryConfig }: UseContactOptions) => {
  return useQuery({
    ...getContactQueryOptions(contactId),
    ...queryConfig,
  });
};
