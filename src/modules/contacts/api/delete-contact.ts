import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getContactsQueryOptions } from './get-contacts';

export const deleteContact = ({ contactId }: { contactId: string }) => {
  return Reshape.http().delete(`/api/contacts/${contactId}`);
};

type UseDeleteContactOptions = {
  mutationConfig?: MutationConfig<typeof deleteContact>;
};

export const useDeleteContact = ({ mutationConfig }: UseDeleteContactOptions = {}) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['contacts', args[1].contactId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getContactsQueryOptions().queryKey,
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteContact,
  });
};
