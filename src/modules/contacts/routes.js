import UserLayout from '@/layouts/UserLayout.vue';
import { auth } from '@/router/middleware';

const routes = [
  {
    path: '/contacts',
    component: UserLayout,
    meta: {
      middleware: auth,
    },
    children: [
      {
        path: '',
        name: 'contacts.index',
        component: () => import('./pages/ContactsIndex.vue'),
        meta: {
          title: 'Contacts',
          headerTitle: 'Contacts',
          headerDescription: 'Overview of all relevant contacts.',
          breadcrumbs: [{ label: 'Contacts', name: 'contacts.index' }],
        },
      },
      {
        path: ':id/view',
        name: 'contacts.view',
        component: () => import('./pages/ContactsView.vue'),
        props: true,
        meta: {
          title: 'View Contact',
          headerTitle: 'View Contact',
          headerDescription: 'Overview of selected contact.',
          breadcrumbs: [
            { label: 'Contacts', name: 'contacts.index' },
            { label: 'View', name: 'contacts.view' },
          ],
        },
      },
      {
        path: 'add',
        name: 'contacts.create',
        component: () => import('./pages/ContactsCreate.vue'),
        meta: {
          title: 'Add Contact',
          headerTitle: 'Add Contact',
          headerDescription: 'Define a new contact for a related entity.',
          breadcrumbs: [
            { label: 'Contacts', name: 'contacts.index' },
            { label: 'Add', name: 'contacts.create' },
          ],
        },
      },
      {
        path: ':id/edit',
        name: 'contacts.update',
        component: () => import('./pages/ContactsUpdate.vue'),
        props: true,
        meta: {
          title: 'Edit Contact',
          headerTitle: 'Edit Contact',
          headerDescription: 'Update an existing contact defined in the system.',
          breadcrumbs: [
            { label: 'Contacts', name: 'contacts.index' },
            { label: 'Edit', name: 'contacts.update' },
          ],
        },
      },
      {
        path: ':id/activity',
        name: 'contacts.activity',
        component: () => import('@/modules/activity/pages/ActivityIndex.vue'),
        props: (route) => ({
          activitableType: 'Contact',
          activitableId: route.params.id,
        }),
        meta: {
          title: 'Contact Activity',
          headerTitle: 'Contact Activity',
          headerDescription: 'View the activity for this contact.',
          breadcrumbs: [
            { label: 'Contacts', name: 'contacts.index' },
            { label: 'View', name: 'contacts.view' },
            { label: 'Activity', name: 'contacts.activity' },
          ],
        },
      },
    ],
  },
];

export default routes;
