<template>
  <div>
    <VPageHeader
      :title="headerTitle || 'Contacts'"
      :description="headerDescription || 'Overview of all relevant contacts.'"
    >
      <VButton
        as="router-link"
        :to="createRoute"
        intent="secondary"
      >
        <template #start>
          <BIconPlusLg />
        </template>
        Add Contact
      </VButton>
    </VPageHeader>
    <VContainer class="py-2 lg:py-6">
      <ContactsTable
        :contactable-type="contactableType"
        title="Contacts & Social References"
        :records="contacts?.data"
        :total-records="contacts?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
      />
    </VContainer>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useFilterParams } from '@/hooks/query';
import ContactsTable from '@/modules/contacts/components/ContactsTable.vue';
import BIconPlusLg from '~icons/bi/plus-lg';
import { useContacts } from '@/modules/contacts/api/get-contacts';
import { useUserContacts } from '@/modules/users/api/get-user-contacts';
import { useCompanyContacts } from '@/modules/companies/api/get-company-contacts';
import { useCustomerContacts } from '@/modules/customers/api/get-customer-contacts';
import { useAuth } from '@/store';

const props = defineProps({
  contactableType: {
    type: String,
    required: false,
  },
  contactableId: {
    type: String,
    required: false,
  },
  self: {
    type: Boolean,
    default: false,
  },
});

const createRoute = computed(() => {
  if (props?.contactableType === 'users' && props?.self) {
    // User is creating a contact for themselves.
    return { name: 'profile.contacts.create' };
  } else if (props?.contactableType && props?.contactableId) {
    // User is creating a contact for another entity.
    return { name: `${props.contactableType}.contacts.create`, params: { id: props.contactableId } };
  }
  // Fallback to the default create route.
  return { name: 'contacts.create' };
});

const headerTitle = computed(() => {
  if (!props?.contactableType) return null;
  if (props?.contactableType.toLowerCase() === 'users') {
    if (props?.self) return 'My Contacts';
    return 'User Contacts';
  }
  if (props?.contactableType.toLowerCase() === 'companies') {
    return 'Company Contacts';
  }
  if (props?.contactableType.toLowerCase() === 'customers') {
    return 'Customer Contacts';
  }
  return null;
});

const headerDescription = computed(() => {
  if (!props?.contactableType) return null;
  if (props?.contactableType.toLowerCase() === 'users') {
    if (props?.self) return 'All contacts you have created.';
    return 'All contacts for the relevant user.';
  }
  if (props?.contactableType.toLowerCase() === 'companies') {
    return 'All contacts for the relevant company.';
  }
  if (props?.contactableType.toLowerCase() === 'customers') {
    return 'All contacts for the relevant customer.';
  }
  return null;
});

// Return the contactable ID based on the self prop and contactableType
const contactableId = computed(() => {
  if (!props?.self && !props?.contactableId) {
    return null;
  }
  if (props?.self && props?.contactableType.toLowerCase() === 'users') {
    const auth = useAuth(); // Only needed if self is true.
    return auth.user.locked_id;
  }
  return props.contactableId;
});

const { params, search, sort, paginate } = useFilterParams();

const { data: contacts, isLoading } =  ({
  'users': useUserContacts,
  'companies': useCompanyContacts,
  'customers': useCustomerContacts,
}[props?.contactableType?.toLowerCase()] || useContacts)({
    params: params.value,
    id: contactableId.value,
})

</script>
