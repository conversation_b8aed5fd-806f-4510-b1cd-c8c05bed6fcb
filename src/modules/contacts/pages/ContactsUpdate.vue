<template>
  <div>
    <VPageHeader title="Update Contact" description="Update an existing contact in the system.">
      <template #actions>
        <VButton
          type="submit"
          form="contact-form"
          intent="secondary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Update
        </VButton>
        <VButton
          @click="confirmModalOpen = true"
          intent="danger"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <TransitionFade>
        <div v-if="isContactLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>
        <div v-else class="space-y-3">
          <ContactForm :contact="contact" @submit="submit" />
          <div class="flex w-full justify-start lg:justify-end space-x-3">
            <VButton
              type="submit"
              form="contact-form"
              intent="primary"
            >
              <template #start>
                <BIconCheckLg />
              </template>
              Confirm
            </VButton>

            <VButton intent="danger" @click="confirmModalOpen = true">
              <template #start>
                <BIconArrowReturnLeft />
              </template>
              Cancel
            </VButton>
          </div>
        </div>
      </TransitionFade>
    </VContainer>

    <ConfirmModal
      v-model:open="confirmModalOpen"
      @confirmed="router.push({ name: 'contacts.index' })"
    />
  </div>
</template>

<script setup>
import ConfirmModal from '@/components/ui/modals/ConfirmModal.vue';
import ContactForm from '@/modules/contacts/components/ContactForm.vue';
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconCheckLg from '~icons/bi/check-lg';
import { ref } from 'vue';
import { useUpdateContact } from '@/modules/contacts/api/put-contact';
import { useContact } from '@/modules/contacts/api/get-contact';

const confirmModalOpen = ref(false);

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const updateContactMutation = useUpdateContact({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast({
        title: 'Contact Updated',
        message: `Contact for <b>${result.data?.contactable?.lookup}</b> was successfully updated.`,
      }, 'success');
      router.push({ name: 'contacts.view', params: { id: result.data.locked_id } });
    },
  },
});

const {
  data: contact,
  isLoading: isContactLoading
} = useContact({
  contactId: props.id
});

const submit = (form) => {
  form.submit((data) => updateContactMutation.mutateAsync({
    contactId: props.id,
    data,
  }));
};

</script>
