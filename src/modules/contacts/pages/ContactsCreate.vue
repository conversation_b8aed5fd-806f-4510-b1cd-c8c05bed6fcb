<template>
  <div>
    <VPageHeader title="Add Contact" description="Define a new contact for a related entity.">
      <template #actions>
        <VButton
          type="submit"
          form="contact-form"
          intent="secondary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>
        <VButton
          @click="confirmModalOpen = true"
          intent="danger"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <ContactForm
        :contactable-type="contactableType"
        :contactable-id="contactableId"
        @submit="submit"
      />

      <div class="flex w-full justify-start lg:justify-end space-x-3">
        <VButton
          type="submit"
          form="contact-form"
          intent="primary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>

        <VButton intent="danger" @click="confirmModalOpen = true">
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </div>
    </VContainer>

    <VConfirmModal
      v-model:open="confirmModalOpen"
      @confirmed="router.push(returnRoute)"
    />
  </div>
</template>

<script setup>
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconPlusLg from '~icons/bi/plus-lg';
import ContactForm from '@/modules/contacts/components/ContactForm.vue';
import { ref, computed } from 'vue';
import { useCreateContact } from '@/modules/contacts/api/post-contact';
import { useAuth } from '@/store/auth';

// Get auth store.
const auth = useAuth();

const props = defineProps({
  contactableType: {
    type: String,
    required: false,
  },
  contactableId: {
    type: String,
    required: false,
  },
  self: {
    type: Boolean,
    default: false,
  },
});

const user = ref(null);
// We only need the user id if contact is for user and self.
if (props?.self && props?.contactableType === 'users') {
  user.value = auth.user;
}

// Override the addressableId. Supports self for user and addressableId for other entities.
const contactableId = computed(() => {
  if (props?.self && props?.contactableType === 'users') {
    return user.value.locked_id;
  }
  return props?.contactableId;
});

const confirmModalOpen = ref(false);

const returnRoute = computed(() => {
  if (props?.contactableType === 'users' && props?.self) {
    return { name: 'profile.contacts.index' };
  } else if (props?.contactableType && props?.contactableId) {
    return { name: `${props.contactableType}.contacts.index`, params: { id: props.contactableId } };
  } else {
    return { name: 'contacts.index' };
  }
});

const createContactMutation = useCreateContact({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Contact Successfully Created',
          message: `Contact for <b>${result.data?.contactable?.lookup}</b> was successfully created.`,
        },
        'success'
      );
      router.push({ name: 'contacts.view', params: { id: result.data.locked_id } });
    },
  },
});

const submit = (form) => {
  form.submit((data) => createContactMutation.mutateAsync({
    data,
  }));
};

</script>
