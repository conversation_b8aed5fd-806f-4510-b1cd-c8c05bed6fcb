<template>
  <div>
    <VPageHeader title="View Contact" description="Overview of selected contact." />
    <VContainer class="my-3">
      <TransitionFade>
        <div v-if="isContactLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>
        <div v-else-if="contact" class="grid gap-3 lg:grid-cols-5 items-start">
          <ViewPanel class="col-span-5 xl:col-span-4 order-2 xl:order-1">
            <PanelDefaultField label="Related Entity" :value="contact.contactable?.lookup" allowCopy>
              <template #default>
                <VEntityLink
                  :entityType="contact.contactable_type"
                  :entityId="contact.contactable?.locked_id"
                  :value="contact.contactable?.lookup"
                />
              </template>
            </PanelDefaultField>
            <PanelDefaultField label="Contact Method" :value="contact.method?.name" />
            <PanelContactField label="Contact Value" :value="contact.value" :method="contact.method?.name" allowCopy />
            <PanelBooleanField label="Is Primary" :value="contact.is_primary" showTextValues />
            <PanelDefaultField label="Contact Note(s)" :value="contact.note" allowCopy />
            <PanelDefaultField label="Status" :value="contact.status?.name">
              <template #default>
                <ContactStatusBadge :status="contact?.status?.name" />
              </template>
            </PanelDefaultField>
            <PanelDateTimeField label="Date Created" :value="contact.created_at" />
            <PanelDateTimeField label="Date Updated" :value="contact.updated_at" />
          </ViewPanel>

          <VCard class="col-span-5 xl:col-span-1 order-1 xl:order-2">
            <VCardHeader>Actions</VCardHeader>
            <VCardBody class="flex gap-3 justify-start flex-wrap">
              <VButton
                v-if="entityRouteName"
                intent="primary"
                as="router-link"
                :to="{
                  name: entityRouteName,
                  params: { id: contact.contactable?.locked_id },
                }"
              >View {{ contact.contactable_type }}
                <template #start>
                  <BIconSearch />
                </template>
              </VButton>
              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'contacts.index' }"
              >View All Contacts
                <template #start>
                  <BIconListUl />
                </template>
              </VButton>

              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'contacts.activity', params: { id: contact.locked_id } }"
              >View Activity
                <template #start>
                  <BIconActivity />
                </template>
              </VButton>

              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <VButton intent="danger">
                    <template #start>
                      <BIconPencilSquare />
                    </template>
                    <template #end>
                      <BIconChevronDown />
                    </template>
                    Modify
                  </VButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{
                        name: 'contacts.update',
                        params: { id: contact.locked_id },
                      }"
                      class="w-full"
                    >
                      <BIconPencilSquare />Edit
                    </RouterLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem @click="openContactDeleteConfirmationDialog">
                    <BIconTrash />Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <VConfirmModal
                v-model:open="isContactDeleteConfirmationDialog"
                @confirmed="deleteContactMutation.mutate({ contactId: contact.locked_id })"
                title="Confirm Contact Deletion"
                :description="`<p>You're about to delete the requested ${contact.method.name} contact for the
                  ${contact.contactable.type.toLowerCase()}: <b>${contactableLookup}</b>.</p><p>Please confirm you wish to proceed.</p>`"
                :icon="BIconTrashFill"
              />

            </VCardBody>
          </VCard>
        </div>
        <template v-else>
          <VEmptyState
            title="Contact Not Found"
            description="The requested contact was not found."
            :contact="true"
          />
        </template>
      </TransitionFade>
    </VContainer>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconSearch from '~icons/bi/search';
import BIconChevronDown from '~icons/bi/chevron-down';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconListUl from '~icons/bi/list-ul';
import BIconActivity from '~icons/bi/activity';
import ViewPanel from '@/components/ui/panels/ViewPanel.vue';
import PanelDefaultField from '@/components/ui/panels/fields/ViewPanel/PanelDefaultField.vue';
import PanelBooleanField from '@/components/ui/panels/fields/ViewPanel/PanelBooleanField.vue';
import PanelDateTimeField from '@/components/ui/panels/fields/ViewPanel/PanelDateTimeField.vue';
import PanelContactField from '@/components/ui/panels/fields/ViewPanel/PanelContactField.vue';
import ContactStatusBadge from '@/modules/contacts/components/ContactStatusBadge.vue';

import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';

import { useDisclosure } from '@/hooks';
import { Contact } from '@/types/api';
import { useRouter } from 'vue-router';
import { useDeleteContact } from '@/modules/contacts/api/delete-contact';
import { useContact } from '@/modules/contacts/api/get-contact';

const { isOpen: isContactDeleteConfirmationDialog, open: openContactDeleteConfirmationDialog } = useDisclosure(false);

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const router = useRouter();

const { 
  data: contact,
  isLoading: isContactLoading
} = useContact({ contactId: props.id });

const deleteContactMutation = useDeleteContact({
  mutationConfig: {
    onSuccess: () => {
      router.push({
        name: 'contacts.index',
      });
      Reshape.toast(
        {
          title: 'Contact Deleted',
          message: `The requested contact for the ${contact.value.contactable.type.toLowerCase()} <b>${
            contact.value.contactable.lookup
          }</b> was successfully deleted.`,
        },
        'success'
      );
    },
  },
});

// Computed property to return an entity route name based on the entity type.
const entityRouteName = computed(() => {
  if (!contact.value?.contactable_type) return false;

  const entityType = contact.value.contactable_type.toLowerCase();
  if (entityType === 'user') {
    return 'users.view';
  } else if (entityType === 'company') {
    return 'companies.view';
  } else if (entityType === 'customer') {
    return 'customers.view';
  }
  return false;
});

const contactableLookup = computed(() => {
  return contact.value.contactable?.lookup || 'Undefined';
});
</script>
