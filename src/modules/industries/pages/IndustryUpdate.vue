<template>
  <div>
    <VPageHeader title="Update Industry" description="Update an existing industry in the system.">
      <template #actions>
        <VButton type="submit"
          form="industry-form"
          intent="secondary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Confirm
        </VButton>
        <VButton
          intent="danger"
          @click="confirmModalOpen = true"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <TransitionFade>
        <IndustryForm v-if="!isIndustryLoading" :industry="industry" @submit="submit" />
        <div v-else class="flex w-full justify-center p-6">
          <VSpinner size="xl" />
        </div>
      </TransitionFade>

      <div class="flex w-full justify-start lg:justify-end space-x-3">
        <VButton
          type="submit"
          form="industry-form"
          intent="primary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Confirm
        </VButton>

        <VButton intent="danger" @click="confirmModalOpen = true">
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </div>
    </VContainer>
    
    <VConfirmModal
      @confirmed="router.push({ name: 'settings.industries.index' })"
      v-model:open="confirmModalOpen"
    />
  </div>
</template>

<script setup>
import IndustryForm from '@/modules/industries/components/IndustryForm.vue';
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconCheckLg from '~icons/bi/check-lg';
import { ref } from 'vue';
import { useUpdateIndustry } from '@/modules/industries/api/put-industry';
import { useIndustry } from '@/modules/industries/api/get-industry';

const confirmModalOpen = ref(false);

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const updateIndustryMutation = useUpdateIndustry({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Industry Updated',
          message: `The industry <b>${result.data?.name}</b> was successfully updated.`,
        },
        'success'
      );
      router.push({ name: 'settings.industries.view', params: { id: result.data.locked_id } });
    },
  },
});

const { data: industry, isLoading: isIndustryLoading } = useIndustry({
  industryId: props.id
});

const submit = (form) => {
  form.submit((data) => updateIndustryMutation.mutateAsync({
    industryId: props.id,
    data,
  }));
};
</script>
