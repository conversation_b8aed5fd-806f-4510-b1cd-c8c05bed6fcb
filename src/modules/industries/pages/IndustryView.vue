<template>
  <div>
    <VPageHeader title="View Industry" description="Overview of selected industry." />
    <VContainer class="my-3">
      <TransitionFade>
        <div v-if="isLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>
        <div v-else-if="industry" class="grid gap-3 lg:grid-cols-5 items-start">
          <ViewPanel class="col-span-5 xl:col-span-4 order-2 xl:order-1">
            <PanelDefaultField label="Industry Name" :value="industry.name" allowCopy />
            <PanelDefaultField label="Description" :value="industry.description" allowCopy />
            <PanelDefaultField label="Status" :value="industry?.status?.name">
              <template #default>
                <IndustryStatusBadge :status="industry?.status?.name" />
              </template>
            </PanelDefaultField>
            <PanelDateTimeField label="Date Created" :value="industry.created_at" />
            <PanelDateTimeField label="Date Updated" :value="industry.updated_at" />
          </ViewPanel>

          <VCard class="col-span-5 xl:col-span-1 order-1 xl:order-2">
            <VCardHeader>Actions</VCardHeader>
            <VCardBody class="flex gap-3 justify-start flex-wrap">
              <!-- Button that returns the user to the referring page. -->
              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'settings.industries.index' }"
              >All Industries
                <template #start>
                  <BIconListUl />
                </template>
              </VButton>

              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'settings.industries.activity', params: { id: industry.locked_id } }"
              >View Activity
                <template #start>
                  <BIconActivity />
                </template>
              </VButton>

              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <VButton intent="danger">
                    <template #start>
                      <BIconPencilSquare />
                    </template>
                    <template #end>
                      <BIconChevronDown />
                    </template>
                    Modify
                  </VButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{
                        name: 'settings.industries.update',
                        params: { id: industry.locked_id },
                      }"
                      class="w-full"
                    >
                      <BIconPencilSquare />Edit
                    </RouterLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem @click="openIndustryDeleteConfirmationDialog">
                    <BIconTrash />Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </VCardBody>
          </VCard>
        </div>
        <template v-else>
          <VEmptyState
            title="Industry Not Found"
            description="The requested industry was not found."
            :contact="true"
          />
        </template>
      </TransitionFade>
    </VContainer>

    <VConfirmModal
      v-model:open="isIndustryDeleteConfirmationDialog"
      @confirmed="deleteIndustryMutation.mutate({
        industryId: props.id
      })"
      title="Confirm Industry Deletion"
      :description="`<p>You're about to delete the requested industry: <b>${name}</b>.</p><p>Please confirm you wish to proceed.</p>`"
      :icon="BIconTrashFill"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BIconListUl from '~icons/bi/list-ul';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconChevronDown from '~icons/bi/chevron-down';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconArrowRight from '~icons/bi/arrow-right';
import BIconActivity from '~icons/bi/activity';
import ViewPanel from '@/components/ui/panels/ViewPanel.vue';
import PanelDefaultField from '@/components/ui/panels/fields/ViewPanel/PanelDefaultField.vue';
import PanelBooleanField from '@/components/ui/panels/fields/ViewPanel/PanelBooleanField.vue';
import PanelDateTimeField from '@/components/ui/panels/fields/ViewPanel/PanelDateTimeField.vue';
import PanelContactField from '@/components/ui/panels/fields/ViewPanel/PanelContactField.vue';
import IndustryStatusBadge from '@/modules/industries/components/IndustryStatusBadge.vue'
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';

import { useDisclosure } from '@/hooks';
import { useRouter } from 'vue-router';
import { useDeleteIndustry } from '@/modules/industries/api/delete-industry';
import { useIndustry } from '@/modules/industries/api/get-industry';
import { ref } from 'vue';
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const router = useRouter();

const flagLoaded = ref(false);

const {
  isOpen: isIndustryDeleteConfirmationDialog,
  open: openIndustryDeleteConfirmationDialog
} = useDisclosure(false);

const { data: industry, isLoading } = useIndustry({
  industryId: props.id,
});

// Computed property to return the industry name if set.
const name = computed(() => {
  return industry.value?.name ? industry.value.name : 'Undefined';
});

// Computed property to return the industry emoji if set.
const emojiValue = computed(() => {
  try {
    return JSON.parse(industry.value?.emoji);
  } catch (error) {
    return industry.value?.emoji ? industry.value.emoji : 'Undefined';
  }
});

// Computed property to convert three character codes to actual language string.
const languages = computed(() => {
  try {
    const languagesArray = JSON.parse(industry.value?.languages);
    const languages = languagesArray.map((language) => {
      return new Intl.DisplayNames(['en'], { type: 'language' }).of(language);
    });
    return languages.length > 0 ? languages.join(', ') : 'Undefined';
  } catch (error) {
    return industry.value?.languages ? industry.value.languages : 'Undefined';
  }
});

// Computed property converting the industries array to a readable string.
const industries = computed(() => {
  return industry.value?.industries
    ? industry.value.industries.map((industry) => industry.name).join(', ')
    : null;
});

// Computed property converting the tld array to a readable string.
const tld = computed(() => {
  if (!industry.value?.tld) return null;
  const arr = JSON.parse(industry.value?.tld);
  return arr.length > 0 ? arr.map((tld) => tld).join(', ') : null;
});

const deleteIndustryMutation = useDeleteIndustry({
  mutationConfig: {
    onSuccess: () => {
      Reshape.toast(
        {
          title: 'Industry Deleted',
          message: `The requested industry <b>${name.value}</b> was successfully deleted.`,
        },
        'success'
      );
      router.push({
        name: 'settings.industries.index',
      });
    },
  },
});

</script>
