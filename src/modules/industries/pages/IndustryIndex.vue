<template>
  <div>
    <VPageHeader
      title="Industries"
      description="Overview of all industries defined within the application."
    >
      <template #actions>
        <VButton
          as="router-link"
          intent="secondary"
          :to="{ name: 'settings.industries.create' }"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Add Industry
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="py-2 lg:py-6">
      <IndustryTable
        title="All Industries"
        :records="industries?.data"
        :total-records="industries?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
      />
    </VContainer>
  </div>
</template>

<script setup lang="ts">
import { useFilterParams } from '@/hooks/query';
import IndustryTable from '@/modules/industries/components/IndustryTable.vue';
import BIconPlusLg from '~icons/bi/plus-lg';
import { useIndustries } from '../api/get-industries';

const { params, search, sort, paginate } = useFilterParams();

const { data: industries, isLoading } = useIndustries({
  params: params.value,
});
</script>
