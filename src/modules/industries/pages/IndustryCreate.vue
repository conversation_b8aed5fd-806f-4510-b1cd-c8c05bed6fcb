<template>
  <div>
    <VPageHeader
      title="Add Industry"
      description="Define a new industry for use throughout the application."
    >
      <template #actions>
        <VButton type="submit"
          form="industry-form"
          intent="secondary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>
        <VButton
          intent="danger"
          @click="confirmModalOpen = true"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <IndustryForm @submit="submit" />

      <div class="flex w-full justify-start lg:justify-end space-x-3">
        <VButton
          type="submit"
          form="industry-form"
          intent="primary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>

        <VButton intent="danger" @click="confirmModalOpen = true">
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </div>
    </VContainer>
    
    <VConfirmModal
      @confirmed="router.push({ name: 'settings.industries.index' })"
      v-model:open="confirmModalOpen"
    />
  </div>
</template>

<script setup>
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconPlusLg from '~icons/bi/plus-lg';
import IndustryForm from '@/modules/industries/components/IndustryForm.vue';
import { ref } from 'vue';
import { useCreateIndustry } from '@/modules/industries/api/post-industry';
const confirmModalOpen = ref(false);

const createIndustryMutation = useCreateIndustry({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Industry Successfully Created',
          message: `The industry <b>${result.data?.name}</b> was successfully created.`,
        },
        'success'
      );
      router.push({
        name: 'settings.industries.view',
        params: { id: result.data.locked_id }
      });
    },
  },
});

const submit = (form) => {
  form.submit((data) => createIndustryMutation.mutateAsync({
    data,
  }));
};

</script>
