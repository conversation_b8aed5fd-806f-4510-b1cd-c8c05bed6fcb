import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { Industry } from '@/types/api';

export const createIndustry = ({ data }: { data: CreateIndustryPayload }) => {
  return Reshape.http().post(`/api/companies/industries`, data);
};

type CreateIndustryPayload = Omit<Industry, 'id' | 'locked_id'>;

type UseCreateIndustryOptions = {
  mutationConfig?: MutationConfig<typeof createIndustry>;
};

export const useCreateIndustry = ({ mutationConfig }: UseCreateIndustryOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ['industries'],
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createIndustry,
  });
};