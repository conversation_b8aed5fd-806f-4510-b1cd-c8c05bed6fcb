import { useQuery, queryOptions } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Industry } from '@/types/api';

export const getIndustry = ({
  industryId,
}: {
  industryId: string;
}): Promise<{ data: Industry }> => {
  return Reshape.http()
    .get(`/api/companies/industries/${industryId}`)
    .then(({ data }) => data);
};

export const getIndustryQueryOptions = (industryId: string) => {
  return queryOptions({
    queryKey: ['industries', industryId],
    queryFn: () => getIndustry({ industryId }),
  });
};

type UseIndustryOptions = {
  industryId: string;
  queryConfig?: QueryConfig<typeof getIndustryQueryOptions>;
};

export const useIndustry = ({ industryId, queryConfig }: UseIndustryOptions) => {
  return useQuery({
    ...getIndustryQueryOptions(industryId),
    ...queryConfig,
  });
}; 