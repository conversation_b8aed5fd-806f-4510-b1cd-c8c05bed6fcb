import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getIndustryQueryOptions } from './get-industry';

import { Industry } from '@/types/api';

export const updateIndustry = ({ industryId, data }: { industryId: string, data: UpdateIndustryPayload }) => {
  return Reshape.http().put(`/api/companies/industries/${industryId}`, data);
};

type UpdateIndustryPayload = Partial<Omit<Industry, 'locked_id'>>;

type UseUpdateIndustryOptions = {
  industryId: string;
  mutationConfig?: MutationConfig<typeof updateIndustry>;
};

export const useUpdateIndustry = ({ industryId, mutationConfig }: UseUpdateIndustryOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['industries', args[1].industryId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getIndustryQueryOptions(args[1].industryId).queryKey,
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: updateIndustry,
  });
};