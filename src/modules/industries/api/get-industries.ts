import { queryOptions, useQuery } from '@tanstack/vue-query';
import { Industry, Meta } from '@/types/api';

import { QueryConfig } from '@/lib/vue-query';

export const getIndustries = (
  params,
  signal
): Promise<{
  data: Industry[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/companies/industries`, {
    params,
    signal,
  });
};

export const getIndustriesQueryOptions = ({ params }: { params?: unknown } = {}) => {
  return queryOptions({
    queryKey: params ? ['industries', params] : ['industries'],
    queryFn: ({ signal }) => getIndustries(params, signal),
  });
};

type UseIndustriesOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getIndustriesQueryOptions>;
};

export const useIndustries = ({ queryConfig, params }: UseIndustriesOptions = {}) => {
  return useQuery({
    ...getIndustriesQueryOptions({ params }),
    ...queryConfig,
  });
}; 