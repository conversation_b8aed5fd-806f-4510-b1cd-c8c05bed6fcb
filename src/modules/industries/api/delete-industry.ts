import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getIndustriesQueryOptions } from './get-industries';

// Declare the Reshape global variable
declare const Reshape: {
  http: () => {
    delete: (url: string) => Promise<any>;
  };
};

export const deleteIndustry = ({ industryId }: { industryId: string }) => {
  return Reshape.http().delete(`/api/companies/industries/${industryId}`);
};

type UseDeleteIndustryOptions = {
  mutationConfig?: {
    onSuccess?: (...args: any[]) => void;
    [key: string]: any;
  };
};

export const useDeleteIndustry = ({ mutationConfig }: UseDeleteIndustryOptions = {}) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['industries', args[1].industryId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getIndustriesQueryOptions().queryKey,
      });
      if (onSuccess) {
        onSuccess(...args);
      }
    },
    ...restConfig,
    mutationFn: deleteIndustry,
  });
}; 