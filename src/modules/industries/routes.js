import UserLayout from '@/layouts/UserLayout.vue';
import { auth } from '@/router/middleware';

const routes = [
  {
    path: '/settings/industries',
    component: UserLayout,
    meta: {
      middleware: auth,
    },
    children: [
      {
        path: '',
        name: 'settings.industries.index',
        component: () => import('./pages/IndustryIndex.vue'),
        meta: {
          title: 'Industries',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Industries', name: 'settings.industries.index' },
          ],
        },
      },
      {
        path: ':id/view',
        name: 'settings.industries.view',
        component: () => import('./pages/IndustryView.vue'),
        props: true,
        meta: {
          title: 'View Industry',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Industries', name: 'settings.industries.index' },
            { label: 'View', name: 'settings.industries.view' },
          ],
        },
      },
      {
        path: 'add',
        name: 'settings.industries.create',
        component: () => import('./pages/IndustryCreate.vue'),
        meta: {
          title: 'Add Industry',
          breadcrumbs: [
            { label: 'Industries', name: 'settings.industries.index' },
            { label: 'Add', name: 'settings.industries.create' },
          ],
        },
      },
      {
        path: ':id/update',
        name: 'settings.industries.update',
        component: () => import('./pages/IndustryUpdate.vue'),
        props: true,
        meta: {
          title: 'Edit Industry',
          breadcrumbs: [
            { label: 'Industries', name: 'settings.industries.index' },
            { label: 'Edit', name: 'settings.industries.update' },
          ],
        },
      },
      {
        path: ':id/activity',
        name: 'settings.industries.activity',
        component: () => import('@/modules/activity/pages/ActivityIndex.vue'),
        props: (route) => ({
          activitableType: 'CompanyIndustry',
          activitableId: route.params.id,
        }),
        meta: {
          title: 'Industry Activity',
          headerTitle: 'Industry Activity',
          headerDescription: 'View the activity for the selected industry.',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Industries', name: 'settings.industries.index' },
            { label: 'View', name: 'settings.industries.view' },
            { label: 'Activity', name: 'settings.industries.activity' },
          ],
        },
      },
    ],
  },
];

export default routes; 