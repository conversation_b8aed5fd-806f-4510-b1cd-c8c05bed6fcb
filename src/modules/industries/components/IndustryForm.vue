<template>
  <form id="industry-form" class="space-y-4" @submit.prevent="submit">
    <FormContainer
      title="Industry Details"
      description="Basic values that define an industry."
      :icon="BIconBriefcaseFill"
    >
      <template #body>
        <div class="grid grid-cols-6 gap-6">
          <VField
            class="col-span-6 lg:col-span-3 order-1"
            label="Name"
            required
            :errors="form.errors.get('name')"
          >
            <VInput
              v-model="form.name"
              placeholder="Enter Industry Name"
            />
          </VField>

          <VField
            class="col-span-6 lg:col-span-3 order-3 lg:order-2"
            label="Status"
            help="Selecting anything other than 'Active' will prevent the industry from being used in the application."
            required
            :errors="form.errors.get('status_id')"
          >
            <StatusCombobox
              v-model="form.status_id"
              statusable="CompanyIndustry"
              placeholder="Select a Status"
              :default-option="props.industry ? null : 'Active'"
              required
            />
          </VField>

          <VField
            class="col-span-6 order-2 lg:order-3"
            label="Description"
            required
            :errors="form.errors.get('description')"
          >
            <VTextarea
              v-model="form.description"
              placeholder="Enter Industry Description"
            />
          </VField>
        </div>
      </template>
    </FormContainer>
  </form>
</template>

<script setup>
import FormContainer from '@/components/ui/forms/FormContainer.vue';
import { useForm } from '@/hooks/form';
import BIconBriefcaseFill from '~icons/bi/briefcase-fill';
import StatusCombobox from '@/components/ui/form/composables/StatusCombobox.vue';

const props = defineProps({
  industry: {
    type: Object,
    default: null,
  },
});

const emits = defineEmits(['submit']);

const submit = () => emits('submit', form.value);

const form = useForm({
  name: props.industry?.name ?? '',
  description: props.industry?.description ?? '',
  status_id: props.industry?.status?.locked_id ?? '',
});
</script>