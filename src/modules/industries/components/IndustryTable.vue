<template>
  <Table
    v-bind="$attrs"
    :columns="columns"
    :default-sort="[{
      id: 'name',
      desc: true,
    }]"
    paginated
    sortable
    searchable
    controlled
  />
</template>

<script setup>
import { h, ref } from 'vue';

import Table from '@/components/ui/tables/Table.vue';

import ActionCell from './IndustryTableActionCell.vue';
import IndustryStatusBadge from './IndustryStatusBadge.vue';
import TableCell from '@/components/ui/tables/TableCell.vue';
import { humanizeTime } from '@/util/dateUtils';

const flagLoaded = ref(false);

const columns = ref([
  {
    accessorFn: (row) => row?.name,
    id: 'name',
    header: 'Name',
    cell: (info) => h(TableCell, { value: info.getValue() }),
  },
  {
    accessorFn: (row) => row?.description,
    id: 'description',
    header: 'Description',
    cell: (info) => {
      const desc = info.getValue();
      if (!desc) return null;
      return desc.length > 50 ? desc.substring(0, 50) + '...' : desc;
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created Date',
    cell: (info) => humanizeTime(info.row.original.created_at),
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: (info) => h(IndustryStatusBadge, {
      status: info.row.original.status?.name,
      key: info.row.original.locked_id,
    }),
  },
  {
    id: 'actions',
    header: () => h('span', { class: 'w-full flex justify-end' }),
    cell: (info) => h(ActionCell, {
      row: info.row.original,
      key: info.row.original.locked_id,
    }),
    enableSorting: false,
    enableResizing: false,
    size: 50,
  },
]);
</script>
