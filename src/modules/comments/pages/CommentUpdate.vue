<template>
  <div>
    <VPageHeader title="Update Subscription Tier" description="Update an existing subscription tier in the system.">
      <template #actions>
        <VButton type="submit"
          form="subscription-tier-form"
          intent="secondary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Confirm
        </VButton>
        <VButton
          intent="danger"
          @click="confirmModalOpen = true"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <TransitionFade>
        <SubscriptionTierForm
          v-if="!isSubscriptionTierLoading"
          :subscriptionTier="subscriptionTier"
          @submit="submit"
        />
        <div v-else class="flex w-full justify-center p-6">
          <VSpinner size="xl" />
        </div>
      </TransitionFade>

      <div class="flex w-full justify-start lg:justify-end space-x-3">
        <VButton
          type="submit"
          form="subscription-tier-form"
          intent="primary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Confirm
        </VButton>

        <VButton intent="danger" @click="confirmModalOpen = true">
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </div>
    </VContainer>
    
    <VConfirmModal
      @confirmed="router.push({ name: 'settings.tiers.index' })"
      v-model:open="confirmModalOpen"
    />
  </div>
</template>

<script setup>
import SubscriptionTierForm from '@/modules/subscriptions/components/tiers/SubscriptionTierForm.vue';
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconCheckLg from '~icons/bi/check-lg';
import { ref } from 'vue';
import { useUpdateSubscriptionTier } from '@/modules/subscriptions/api/tiers/put-subscription-tier';
import { useSubscriptionTier } from '@/modules/subscriptions/api/tiers/get-subscription-tier';

const confirmModalOpen = ref(false);

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const updateSubscriptionTierMutation = useUpdateSubscriptionTier({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Subscription Tier Updated',
          message: `The subscription tier <b>${result.data?.name}</b> was successfully updated.`,
        },
        'success'
      );
      router.push({ name: 'settings.tiers.view', params: { id: result.data.locked_id } });
    },
  },
});

const { data: subscriptionTier, isLoading: isSubscriptionTierLoading } = useSubscriptionTier({
  subscriptionTierId: props.id
});

const submit = (form) => {
  form.submit((data) => updateSubscriptionTierMutation.mutateAsync({
    subscriptionTierId: props.id,
    data,
  }));
};
</script>
