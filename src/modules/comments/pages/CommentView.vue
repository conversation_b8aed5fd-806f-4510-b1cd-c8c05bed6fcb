<template>
  <div>
    <VPageHeader title="View Comment / Post" description="Overview of selected comment or post." />
    <VContainer class="my-3">
      <TransitionFade>
        <div v-if="isLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>
        <div v-else-if="comment" class="grid gap-3 lg:grid-cols-5 items-start">
          <ViewPanel class="col-span-5 lg:col-span-4 order-2 xl:order-1">
            <PanelDefaultField label="Commented On" :value="comment.commentable.name" allowCopy>
              <template #default>
                <template v-if="['User', 'Company', 'Customer'].includes(comment?.commentable?.type || [])">
                  <VEntityLink
                    :entity-type="comment?.commentable?.type"
                    :entity-id="comment?.commentable?.locked_id"
                    :value="comment?.commentable?.name"
                    :show-icon="false"
                    show-avatar
                    avatar-size="sm"
                  />
                </template>
                <template v-else>
                  {{ comment?.commentable?.type }}:
                  <VEntityLink
                    :entity-type="comment?.commentable?.type"
                    :entity-id="comment?.commentable?.locked_id"
                    :value="comment?.commentable?.name"
                  />
                </template>
              </template>
            </PanelDefaultField>
            <PanelDefaultField label="Comment Body" :value="comment.body" allowCopy />
            <PanelDefaultField
              label="Reactions"
              help="Reactions that users have given to the comment. Note that this is not always applicable depending on the location."
              :value="totalReactions"
              allow-copy
            >
              <template #default>
                <div class="flex gap-2 items-center">
                  <ReactionMenu
                    :reactions="comment?.reactions"
                    :disabled="true"
                  />
                  <VBadge color="primary">Total Reactions: {{ totalReactions }}</VBadge>
                </div>
              </template>
            </PanelDefaultField>
            <PanelDefaultField
              label="Ratings"
              :value="ratings.score"
              help="Ratings that users have given to the comment. Note that this is not always applicable depending on the location."
              allow-copy
            >
              <template #default>
                <div v-if="comment?.ratings?.length" class="flex gap-2">
                  <VBadge color="success">Upvotes: {{ ratings.upvotes }}</VBadge>
                  <VBadge color="danger">Downvotes: {{ ratings.downvotes }}</VBadge>
                  <VTooltip content="Note that the rating displayed always starts at 1.">
                    <VBadge color="info">Displayed Rating: {{ ratings.score }}</VBadge>
                  </VTooltip>
                </div>
                <template v-else>
                  1 <span class="text-platinum-600 dark:text-midnight-200">(Default)</span>
                </template>
              </template>
            </PanelDefaultField>
            <PanelDefaultField label="Created By" :value="comment?.creator?.full_name" allowCopy>
              <template #default>
                <VEntityLink
                  v-if="comment?.creator?.locked_id"
                  entityType="user"
                  :entityId="comment?.creator?.locked_id"
                  :value="comment?.creator?.full_name"
                  show-avatar
                  avatar-size="sm"
                />
                <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
              </template>
            </PanelDefaultField>
            <PanelDefaultField label="Status" :value="comment?.status?.name">
              <template #default>
                <CommentStatusBadge :status="comment?.status?.name" />
              </template>
            </PanelDefaultField>
            <PanelDateTimeField label="Date Created" :value="comment.created_at" />
            <PanelDateTimeField label="Date Updated" :value="comment.updated_at" />
          </ViewPanel>

          <VCard class="col-span-5 lg:col-span-1 order-1 xl:order-2">
            <VCardHeader>Actions</VCardHeader>
            <VCardBody class="flex gap-3 justify-start flex-wrap">
              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'comments.index' }"
              >All Comments
                <template #start>
                  <BIconListUl />
                </template>
              </VButton>

              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'comments.activity', params: { id: comment.locked_id } }"
              >View Activity
                <template #start>
                  <BIconActivity />
                </template>
              </VButton>

              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <VButton intent="danger">
                    <template #start>
                      <BIconPencilSquare />
                    </template>
                    <template #end>
                      <BIconChevronDown />
                    </template>
                    Modify
                  </VButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <!-- <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{
                        name: 'comments.update',
                        params: { id: comment.locked_id },
                      }"
                      class="w-full"
                    >
                      <BIconPencilSquare />Edit
                    </RouterLink>
                  </DropdownMenuItem> -->
                  <DropdownMenuItem @click="openCommentStatusUpdateDialog">
                    <BIconArrowLeftRight />Set Status
                  </DropdownMenuItem>
                  <DropdownMenuItem @click="openCommentDeleteConfirmationDialog">
                    <BIconTrash />Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </VCardBody>
          </VCard>
        </div>
        <template v-else>
          <VEmptyState
            title="Subscription Tier Not Found"
            description="The requested subscription tier was not found."
            :contact="true"
          />
        </template>
      </TransitionFade>
    </VContainer>

    <VConfirmModal
      v-model:open="isCommentDeleteConfirmationDialog"
      @confirmed="deleteCommentMutation.mutate({
        commentId: props.id
      })"
      title="Confirm Subscription Tier Deletion"
      :description="`<p>You're about to delete the requested subscription tier: <b>${name}</b>.</p><p>Please confirm you wish to proceed.</p>`"
      :icon="BIconTrashFill"
    />
    
    <VModal
      v-model:open="isCommentStatusUpdateDialog"
      title="Update Comment Status"
      :icon="BIconArrowLeftRight"
      disableOverflow
      @confirmed="submitStatusUpdateConfirmed"
      :close-on-confirm="false"
    >
      <template #description>
        <CommentStatusUpdateForm
          :comment="comment"
        />
      </template>
    </VModal>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BIconListUl from '~icons/bi/list-ul';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconChevronDown from '~icons/bi/chevron-down';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconArrowRight from '~icons/bi/arrow-right';
import BIconArrowLeftRight from '~icons/bi/arrow-left-right';
import BIconActivity from '~icons/bi/activity';
import ViewPanel from '@/components/ui/panels/ViewPanel.vue';
import PanelDefaultField from '@/components/ui/panels/fields/ViewPanel/PanelDefaultField.vue';
import PanelBooleanField from '@/components/ui/panels/fields/ViewPanel/PanelBooleanField.vue';
import PanelDateTimeField from '@/components/ui/panels/fields/ViewPanel/PanelDateTimeField.vue';
import PanelContactField from '@/components/ui/panels/fields/ViewPanel/PanelContactField.vue';
import CommentStatusBadge from '@/modules/comments/components/CommentStatusBadge.vue';
import StatusCombobox from '@/components/ui/form/composables/StatusCombobox.vue';
import ReactionMenu from '@/components/ui/reactions/ReactionMenu.vue';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';

import { useDisclosure } from '@/hooks';
import { useRouter } from 'vue-router';
import { useDeleteComment } from '@/modules/comments/api/delete-comment';
import { useComment } from '@/modules/comments/api/get-comment';
import CommentStatusUpdateForm from '@/modules/comments/components/CommentStatusUpdateForm.vue';
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  management: {
    type: Boolean,
    default: false,
  },
});

const { data: comment, isLoading } = useComment({
  commentId: props.id,
  management: props.management,
});

const router = useRouter();

const {
  isOpen: isCommentDeleteConfirmationDialog,
  open: openCommentDeleteConfirmationDialog
} = useDisclosure(false);

const {
  isOpen: isCommentStatusUpdateDialog,
  open: openCommentStatusUpdateDialog
} = useDisclosure(false);

// Computed property to return the total number of reactions.
const totalReactions = computed(() => {
  return comment.value?.reactions?.reduce((acc, reaction) => acc + reaction.count, 0) || 0;
});

// Computed property to return the industry name if set.
const name = computed(() => {
  return comment.value?.name ? comment.value.name : 'Undefined';
});

const deleteCommentMutation = useDeleteComment({
  mutationConfig: {
    onSuccess: () => {
      Reshape.toast(
        {
          title: 'Comment Deleted',
          message: `The requested comment <b>${comment.value?.body}</b> was successfully deleted.`,
        },
        'success'
      );
      router.push({
        name: 'comments.index',
      });
    },
  },
});

/**
 * Computed property to return the ratings for the comment.
 */
const ratings = computed(() => {
  const upvotes = comment?.ratings?.find(rating => rating.name === 'Upvote')?.count || 0;
  const downvotes = comment?.ratings?.find(rating => rating.name === 'Downvote')?.count || 0;
  // Start point is 1.
  const score = upvotes - downvotes + 1;

  return {
    upvotes,
    downvotes,
    score
  }
});

/**
 * User has clicked submit button (modal).
 */
const submitStatusUpdateConfirmed = () => {
  // Submit the form.

  // If the submission is successful, close the modal.
};

/**
 * Do some hocus pocus.
 */
const submitStatusUpdate = () => {
  console.log('submitStatusUpdate');
};

</script>
