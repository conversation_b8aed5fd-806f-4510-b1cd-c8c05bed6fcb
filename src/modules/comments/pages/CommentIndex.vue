<template>
  <div>
    <VPageHeader
      title="Comments"
      description="Overview of all comments defined within the application."
    />

    <VContainer class="py-2 lg:py-6">
      <CommentTable
        title="Comments"
        :records="comments?.data"
        :total-records="comments?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
        @update:filter="onFilterUpdate"
      />
    </VContainer>
  </div>
</template>

<script setup lang="ts">
import { useFilterParams } from '@/hooks/query';
import CommentTable from '@/modules/comments/components/CommentTable.vue';
import { useComments } from '@/modules/comments/api/get-comments';

const props = withDefaults(defineProps<{
  management?: boolean;
}>(), {
  management: false,
});

const { params, search, sort, paginate } = useFilterParams();

params.value.all = true;
params.value.sort = '-created_at';

function onFilterUpdate(filters: Object) {
  // First, check if any values are false.
  let hasFalse = Object.values(filters).some((value) => value === false);

  if (hasFalse) {
    let statusFilters = [];
    // For each of the filters, if the value is true, add it to the new array.
    for (const [key, value] of Object.entries(filters)) {
      if (value) {
        statusFilters.push(key);
      }
    }
    // Set the filter param (converting to string).
    params.value.filter = {
      'exact:multiple:status': statusFilters.join(','),
    };
  } else if (params.value.filter) {
    // Remove the filter param if no filtering required.
    delete params.value.filter;
  }
}

const {
  data: comments,
  isLoading,
} = useComments({
  params: params.value,
});
</script>
