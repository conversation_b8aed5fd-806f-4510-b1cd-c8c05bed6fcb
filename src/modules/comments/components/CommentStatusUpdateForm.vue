<template>
  <div>
    <form
      id="comment-status-form"
      class="space-y-4"
      @submit.prevent="submitStatusUpdate"
    >
      <VField
        label="Status"
        :errors="statusUpdateForm.errors.get('status_id')"
        required
      >
        <StatusCombobox
          v-model="statusUpdateForm.status_id"
          statusable="Comment"
        />
      </VField>

      <VField
        label="Note"
        :errors="statusUpdateForm.errors.get('note')"
      >
        <VTextarea
          v-model="statusUpdateForm.note"
          rows="2"
          placeholder="Enter a reason or note"
        />
      </VField>
    </form>
  </div>
</template>

<script setup>
import { useForm } from '@/hooks/form';
import { provide } from 'vue';
import StatusCombobox from '@/components/ui/form/composables/StatusCombobox.vue';
const emits = defineEmits(['submitted']);

const props = defineProps({
  comment: {
    type: Object,
  },
});

const statusUpdateForm = useForm({
  status_id: props.comment?.status?.locked_id ?? '',
  note: '',
});

const submitStatusUpdate = () => {
  emits('submitted', statusUpdateForm.value);
};

// Expose the submitStatusUpdate function to the parent component.
provide('submitStatusUpdate', submitStatusUpdate);

</script>
