<template>
  <Table
    v-bind="$attrs"
    :columns="columns"
    :default-sort="[{
      id: 'created_at',
      desc: true,
    }]"
    paginated
    sortable
    searchable
    controlled
  >
    <template #actions>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <VButton intent="primary" class="text-center px-3">
            <BIconFilter class="w-5 h-5" />
          </VButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          <DropdownMenuLabel>Status</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuCheckboxItem
            v-for="(status, key) in filterStatus"
            :key="key"
            
            v-model:model-value="filterStatus[key]"
          >{{ key }}</DropdownMenuCheckboxItem>


        </DropdownMenuContent>
      </DropdownMenu>
    </template>
  </Table>
</template>

<script setup lang="ts">
import { h, ref, watch } from 'vue';
import Table from '@/components/ui/tables/Table.vue';
import ActionCell from '@/modules/comments/components/CommentTableActionCell.vue';
import CommentTableStatusBadge from '@/modules/comments/components/CommentStatusBadge.vue';
import { humanizeTime } from '@/util/dateUtils';
import CommentTableBodyCell from '@/modules/comments/components/CommentTableBodyCell.vue';
import EntityLink from '@/components/ui/links/EntityLink.vue';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/shadcn/dropdown-menu';
import type { DropdownMenuCheckboxItemProps } from 'reka-ui'
import BIconFilter from '~icons/bi/filter';

const emit = defineEmits(['update:filter']);

type Checked = DropdownMenuCheckboxItemProps['modelValue']

// Statuses. We could pull this from the db, but would be an extra api call.
const filterStatus = ref({
  'Active': ref<Checked>(true),
  'Automatically Flagged': ref<Checked>(true),
  'Flagged Active': ref<Checked>(true),
  'Flagged Inactive': ref<Checked>(true),
  'Inactive': ref<Checked>(true),
  'Manually Flagged': ref<Checked>(true),
  'Pending Approval': ref<Checked>(true),
  'Soft Deleted': ref<Checked>(true),
});

// Watch for changes.
watch(filterStatus, (newVal) => {
  emit('update:filter', newVal);
}, { deep: true, immediate: true });

const columns = ref([
  {
    accessorKey: 'created_at',
    header: 'Created Date',
    cell: (info) => humanizeTime(info.row.original.created_at),
  },
  {
    accessorKey: 'creator',
    header: 'Creator',
    cell: (info) => {
      return h(EntityLink, {
        value: info.row.original?.creator?.full_name,
        entityType: 'User',
        entityId: info.row.original?.creator?.locked_id,
        showAvatar: true,
        avatarSize: 'sm',
      });
    }
  },
  {
    accessorFn: (row) => row?.body,
    id: 'body',
    header: 'Body',
    cell: (info) => h(CommentTableBodyCell, { value: info.getValue() }),
  },
  {
    accessorKey: 'commentable.type',
    header: 'Commentable',
    cell: (info) => {
      return h(EntityLink, {
        value: info.row.original.commentable.name || info.row.original.commentable.title || false,
        entityType: info.row.original.commentable.type,
        entityId: info.row.original.commentable.locked_id,
        showAvatar: true,
        avatarSize: 'sm',
        showEntityType: true,
        valueLimit: 30,
      });
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: (info) => h(CommentTableStatusBadge, {
      status: info.row.original.status?.name,
      key: info.row.original.locked_id,
    }),
  },
  {
    id: 'actions',
    header: () => h('span', { class: 'w-full flex justify-end' }),
    cell: (info) => h(ActionCell, {
      row: info.row.original,
      key: info.row.original.locked_id,
    }),
    enableSorting: false,
    enableResizing: false,
    size: 50,
  },
]);
</script>
