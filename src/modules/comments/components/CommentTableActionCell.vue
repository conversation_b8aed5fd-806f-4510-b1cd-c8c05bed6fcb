<template>
  <div class="flex items-center justify-end space-x-1.5">
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <VButton intent="table">
          <BIconThreeDots class="h-5 w-5" />
        </VButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem as-child>
          <RouterLink
            :to="{
              name: 'comments.view',
              params: { id: row.locked_id },
            }"
          >
            <BIconSearch />View
          </RouterLink>
        </DropdownMenuItem>

        <DropdownMenuItem as-child>
          <RouterLink
            :to="{
              name: 'comments.activity',
              params: { id: row.locked_id },
            }"
          >
            <BIconActivity />Activity
          </RouterLink>
        </DropdownMenuItem>

        <DropdownMenuGroup>
          <DropdownMenuLabel>Modify</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem @click="openCommentDeleteConfirmationDialog">
            <BIconTrash />Delete
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>

    <VConfirmModal
      v-model:open="isCommentDeleteConfirmationDialog"
      @confirmed="deleteCommentMutation.mutate({
        commentId: props.row.locked_id
      })"
      title="Confirm Comment Deletion"
      :description="`<p>You're about to delete the comment: '<b>${commentBodyString}</b>' from the user <b>${props.row?.commentable?.name ?? 'undefined'}</b>.</p><p>Please confirm you wish to proceed.</p>`"
      :icon="BIconTrashFill"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BIconSearch from '~icons/bi/search';
import BIconThreeDots from '~icons/bi/three-dots';
import BIconTrash from '~icons/bi/trash';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconActivity from '~icons/bi/activity';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/shadcn/dropdown-menu';
import { useDisclosure } from '@/hooks';
import { useDeleteComment } from '@/modules/comments/api/delete-comment';
import { Comment } from '@/types/api/comment';

// Declare the Reshape global variable
declare const Reshape: {
  toast: (options: { title: string; message: string }, type: string) => void;
};

const props = defineProps<{
  row: Comment;
}>();

const commentBodyString = computed(() => {
  return props.row?.body ? props.row.body.slice(0, 120) + '...' : null;
});

const deleteCommentMutation = useDeleteComment({
  mutationConfig: {
    onSuccess: () => {
      Reshape.toast(
        {
          title: 'Comment Deleted',
          message: commentBodyString.value
            ? `The comment <b>${commentBodyString.value}</b> was successfully deleted.`
            : 'The selected comment was successfully deleted.',
        },
        'success'
      );
    },
  },
});

const {
  isOpen: isCommentDeleteConfirmationDialog,
  open: openCommentDeleteConfirmationDialog,
} = useDisclosure(false);
</script>
