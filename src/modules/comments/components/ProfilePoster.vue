<template>
  <div class="rounded-lg bg-platinum-50 p-1 shadow dark:bg-midnight-600 ring-1 ring-platinum-900/5 dark:ring-midnight-900/5" ref="container" tabindex="0">
    <form @submit.prevent="submitPost">
      <TabGroup>
        <TabPanels class="px-2 pt-2">
          <TabPanel class="rounded-lg">
            <VTextarea
              v-model="form.body"
              rows="1"
              class="border-primary-400 h-auto min-h-6"
              :placeholder="self && posterPlaceholderSelf ? posterPlaceholderSelf : posterPlaceholder"
              @focus="open($event)"
              @blur="close($event)"
            ></VTextarea>
          </TabPanel>
        </TabPanels>
        <div
          class="overflow-hidden px-2 pb-2 transition-all duration-1000"
          :class="opened ? 'max-h-[750px] opacity-100' : 'max-h-0 opacity-0'"
        >
          <div class="mt-3 flex justify-between">
            <div class="flex align-middle">
              <TabList class="flex items-center gap-3">
                <!-- <Tab v-slot="{ selected }" as="template">
                  <VButton
                    size="xs"
                    intent="secondary"
                    :class="selected ? 'bg-primary-400 dark:bg-primary-600' : ''"
                    outline
                  >Default Post</VButton>
                </Tab> -->
              </TabList>
            </div>
            <div class="flex">
              <div class="ml-auto flex items-center space-x-1">
                <VTooltip content="Insert link">
                  <button
                    type="button"
                    class="inline-flex h-8 w-8 items-center justify-center rounded-full text-primary-400 hover:text-primary-500 transition-colors"
                  >
                    <BIconLink45deg class="h-5 w-5" aria-hidden="true" />
                  </button>
                </VTooltip>
                <VTooltip content="Insert code">
                  <button
                    type="button"
                    class="inline-flex h-8 w-8 items-center justify-center rounded-full text-primary-400 hover:text-primary-500 transition-colors"
                  >
                    <BIconCodeSlash class="h-5 w-5" aria-hidden="true" />
                  </button>
                </VTooltip>
              
                <VTooltip content="Mention someone">
                  <button
                    type="button"
                    class="inline-flex h-8 w-8 items-center justify-center rounded-full text-primary-400 hover:text-primary-500 transition-colors"
                  >
                    <BIconAt class="h-5 w-5" aria-hidden="true" />
                  </button>
                </VTooltip>
              </div>
              <VButton
                size="sm" class="ml-1" type="submit" outline>
                <template #start>
                  <BIconSendFill />
                </template>
                Post
              </VButton>
            </div>
          </div>
        </div>
      </TabGroup>
    </form>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/vue';
import { useForm } from '@/hooks/form';
import BIconAt from '~icons/bi/at';
import BIconSendFill from '~icons/bi/send-fill';
import BIconCodeSlash from '~icons/bi/code-slash';
import BIconLink45deg from '~icons/bi/link-45deg';

import { useCreateComment } from '@/modules/comments/api/post-comment';
import { useCreateUserComment } from '@/modules/users/api/post-user-comment';
import { useCreateCompanyComment } from '@/modules/companies/api/post-company-comment';

const props = defineProps({
  commentableType: {
    type: String,
    default: null,
  },
  commentableId: {
    type: String,
    default: null,
  },
  self: {
    type: Boolean,
    default: false,
  },
  posterPlaceholder: {
    type: String,
    default: 'What\'s on your mind?',
  },
  posterPlaceholderSelf: {
    type: String,
    default: 'What\'s on your mind?',
  },
});

const opened = ref(false);

function open(element) {
  this.opened = true;
}

const isCheckingClose = ref(false);
const container = ref(null);

function close() {
  if (isCheckingClose.value) return;
  isCheckingClose.value = true;

  // After 2 seconds, if the focus is outside the container, close.
  const interval = setInterval(() => {
    const active = document.activeElement;
    if (!container?.value?.contains(active)) {
      // Focus is outside the container
      this.opened = false;
      clearInterval(interval);
      isCheckingClose.value = false;
    }
  }, 2000);
}

const commentableLowercase = props.commentableType.toLowerCase();

const form = useForm({
  commentable_id: props.commentableId,
  commentable_type: props.commentableType,
  parent_id: null,
  body: '',
});

const createCommentMutation = ({
  'user': useCreateUserComment,
  'company': useCreateCompanyComment,
  // Add other commentable types here
}[commentableLowercase] || useCreateComment)({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Post Added',
          message: `Your post was successfully added.`,
        },
        'success'
      );
    },
  },
});

const submitPost = () => {
  form.value.submit((data) => createCommentMutation.mutateAsync({ data }));
};

</script>
