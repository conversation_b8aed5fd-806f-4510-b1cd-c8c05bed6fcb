<template>
  <div>
    <TransitionFade>
      <div>
        <div v-if="isLoading">
          <div v-for="i in 3" :key="i">
            <div class="flex gap-3 mb-3 items-center">
              <VSkeleton class="shrink-0 w-14 h-14 rounded-full" />
              <div class="flex flex-col space-y-2 w-full">
                <VSkeleton class="h-3 w-full sm:w-2/3 md:w-1/3" />
                <VSkeleton class="w-full h-5" />
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="comments?.data?.length > 0" class="space-y-3">
          <CommentItem
            v-for="comment in comments.data"
            :key="comment.id"
            :comment="comment"
            :parent-commentable-type="commentableType"
            :parent-commentable-id="commentableId"
            :can-react="canReact"
            :can-rate="canRate"
            @reply="replyComment"
            @delete="deleteComment"
            @edit="editComment"
            @report="reportComment"
          />
          <VPaginator
            class="mt-6 inline-flex justify-end w-full"
            @change="(page) => paginate(page)"
            :total="comments?.meta?.total"
            :per-page="comments?.meta?.per_page"
            :current="comments?.meta?.current_page"
          />
        </div>

        <div v-else-if="!isLoading && comments?.data?.length === 0">
          <slot name="empty" />
        </div>
      </div>
    </TransitionFade>

    <!-- Report Comment Modal -->
    <AlertDialog v-model:open="reportModalOpen">
      <AlertDialogContent @pointerDownOutside="reportModalOpen = false" class="overflow-visible">
        <AlertDialogHeader :icon="BIconFlagFill">
          <AlertDialogTitle>Flag / Report Comment</AlertDialogTitle>
          <AlertDialogDescription>
            <div class="space-y-3">
              <p>Thanks for doing your part to help keep the community safe and respectful. Let us know what's wrong with this comment, and we'll take a look as soon as possible.</p>
              <p v-html="reportModalDescription"></p>
              <hr>
              <VField
                label="Reason"
                required
                :errors="reportForm.errors.get('type_id')"
              >
                <TypeCombobox
                  v-model="reportForm.type_id"
                  typeable="ReportReason"
                  placeholder="Select Report Reason"
                  required
                />
              </VField>
              <VField
                label="Additional Details"
                :errors="reportForm.errors.get('reason')"
              >
                <VTextarea
                  v-model="reportForm.reason"
                  rows="3"
                  placeholder="Enter Additional Details"
                />
              </VField>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <VButton
            intent="primary"
            @click="confirmReport"
            :disabled="reportFormIsSubmitting"
          >
            <template #start>
              <BIconCheckLg />
            </template>
            Confirm
          </VButton>
          <AlertDialogCancel>
            <BIconArrowReturnLeft />Cancel
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>

<script setup>
import { useFilterParams } from '@/hooks/query';
import CommentItem from '@/modules/comments/components/Feed/CommentItem.vue';
import { ref, watch } from 'vue';
import { useForm } from '@/hooks/form';
import { axios } from '@/lib/axios';

import TypeCombobox from '@/components/ui/form/composables/TypeCombobox.vue';
import { useUpdateComment } from '@/modules/comments/api/put-comment';
import { useCreateUserComment } from '@/modules/users/api/post-user-comment';
import { useDeleteComment } from '@/modules/comments/api/delete-comment';
import { useUserComments } from '@/modules/users/api/get-user-comments';
import { useAnnouncementComments } from '@/modules/announcements/api/get-announcement-comments';
import { useCreateAnnouncementComment } from '@/modules/announcements/api/post-announcement-comment';
import { useCompanyComments } from '@/modules/companies/api/get-company-comments';
import { useCreateCompanyComment } from '@/modules/companies/api/post-company-comment';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/shadcn/alert-dialog';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconCheckLg from '~icons/bi/check-lg';
import BIconFlagFill from '~icons/bi/flag-fill';

const props = defineProps({
  commentableType: {
    type: String,
    required: true,
  },
  commentableId: {
    type: String,
    required: true,
  },
  self: {
    type: Boolean,
    default: false,
  },
  canReact: {
    type: Boolean,
    default: false,
  },
  canRate: {
    type: Boolean,
    default: false,
  },
  perPage: {
    type: Number,
    default: 5,
  },
});

const reportModalOpen = ref(false);
const reportModalDescription = ref(null);
const reportFormIsSubmitting = ref(false);

const reportForm = useForm({
  commentable_type: props.commentableType,
  commentable_id: props.commentableId,
  comment_id: '',
  type_id: '',
  reason: '',
}, { resetOnSuccess: false });

const commentableTypeLower = props.commentableType?.toLowerCase() ?? 'user';

const emits = defineEmits(['reply', 'total']);

const { params, paginate } = useFilterParams({
  per_page: props.perPage,
});

const { data: comments, isLoading } = ({
  'user': useUserComments,
  'announcement': useAnnouncementComments,
  'company': useCompanyComments,
}[commentableTypeLower])({
  params: params.value,
  id: props.commentableId,
});

watch(comments, (newVal) => {
  emits('total', newVal?.meta?.total);
});

const createCommentMutation = ({
  'user': useCreateUserComment,
  'announcement': useCreateAnnouncementComment,
  'company': useCreateCompanyComment,
}[commentableTypeLower])({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast({
        title: 'Comment Added',
        message: `Your comment was successfully added.`,
      }, 'success');
    },
  },
});

const replyComment = (form) => {
  if (!form) {
    Reshape.toast({
        title: 'Error',
        message: `Failed to submit reply. Please contact support if this issue persists.`,
      }, 'danger');
  } else {
    form.submit((data) => createCommentMutation.mutateAsync({ data }));
  }
};

const deleteCommentMutation = useDeleteComment({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast({
        title: 'Comment Deleted',
        message: `The comment was successfully deleted.`,
      }, 'success');
    },
  },
});

const deleteComment = (id) => {
  deleteCommentMutation.mutateAsync({ commentId: id });
};

const editCommentMutation = useUpdateComment({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast({
        title: 'Comment Updated',
        message: `Your comment was successfully updated.`,
      }, 'success');
    },
  },
});

const editComment = (form) => {
  form.submit((data) => editCommentMutation.mutateAsync({ commentId: form.id, data }));
};

const reportComment = ({ id, body, creator }) => {
  reportForm.value.comment_id = id;
  reportModalOpen.value = true; // Open report modal.
  reportModalDescription.value = '<p><b>Comment:</b> "' + body + '"</p><p><b>Created by: </b>' + creator + '.</p>';
};

const confirmReport = async () => {
  reportFormIsSubmitting.value = true;
  try {
    await reportForm.value.submit(async (formData) => {
      const response = await axios.post('/api/comments/report', formData);
      if (response?.data?.success) {
        Reshape.toast({
          title: 'Report Submitted',
          message: 'Thank you for your report. We will review it shortly.',
        }, 'success');
        reportForm.value.reset(); // Reset form
        reportModalOpen.value = false; // Close modal
      } else {
        throw new Error('Failed to submit report.');
      }
    });
  } catch (error) {
    Reshape.toast({
      title: 'Error',
      message:
        error?.response?.data?.message ||
        'Unable to submit your report. Please try again later.',
    }, 'danger');
  } finally {
    reportFormIsSubmitting.value = false;
  }
};


</script>

<style scoped>
.comment-item {
  animation: fadeInUp 0.3s ease-out forwards;
  opacity: 0;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>