<template>
  <div 
    class="rounded-lg bg-platinum-100 dark:bg-midnight-600 p-3 shadow transition-all duration-300" 
    ref="container"
  >
    <form @submit.prevent="submitPost">
      <div class="relative">
        <VTextarea
          v-model="form.body"
          rows="1"
          class="border-primary-400 h-auto transition-all duration-300 pr-16"
          :class="[
            opened || form.body.trim() ? 'min-h-20' : 'min-h-6',
          ]"
          :placeholder="posterPlaceholder"
          @focus="handleFocus"
          @blur="handleBlur"
          :max-rows-visible="5"
          ref="textareaRef"
        />
        
        <div
          class="absolute transition-all duration-200 ease-in-out"
          :class="[
            opened || form.body.trim() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2 pointer-events-none',
            scrollbar ? 'bottom-3 right-6' : 'bottom-3 right-3'  
          ]"
        >
          <VButton 
            :size="mdAndLarger ? 'xs' : 'sm'" 
            type="submit"
            :disabled="!form.body.trim()"
            class="shadow"
          >
            <span class="flex items-center gap-2">
              <span class="hidden md:block">Post</span>
              <BIconSendFill />
            </span>
          </VButton>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, onBeforeUnmount } from 'vue';
import { useForm } from '@/hooks/form';
import BIconSendFill from '~icons/bi/send-fill';
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core';

import { useCreateComment } from '@/modules/comments/api/post-comment';
import { useCreateUserComment } from '@/modules/users/api/post-user-comment';
import { useCreateAnnouncementComment } from '@/modules/announcements/api/post-announcement-comment';
import { useCreateCompanyComment } from '@/modules/companies/api/post-company-comment';

const props = defineProps({
  commentableType: {
    type: String,
    default: null,
  },
  commentableId: {
    type: String,
    default: null,
  },
  posterPlaceholder: {
    type: String,
    default: 'Share your thoughts...',
  },
});

const breakpoints = useBreakpoints(breakpointsTailwind);
const mdAndLarger = breakpoints.greaterOrEqual('md');

const opened = ref(false);
const container = ref(null);
const textareaRef = ref(null);
const scrollbar = ref(false);
let observer = null;

const checkOverflow = () => {
  const el = textareaRef.value?.$el?.querySelector('textarea');
  if (!el) return;

  scrollbar.value = el.scrollHeight > el.clientHeight;
}

onMounted(() => {
  const el = textareaRef.value?.$el?.querySelector('textarea');
  if (!el) return;

  // Observe changes to size/overflow.
  observer = new ResizeObserver(() => {
    checkOverflow();
  })
  observer.observe(el)
  el.addEventListener('input', checkOverflow);

  // Initial check.
  checkOverflow();
})

onBeforeUnmount(() => {
  const el = textareaRef.value?.$el?.querySelector('textarea');
  if (observer) observer.disconnect();
  if (el) el.removeEventListener('input', checkOverflow);
})


const form = useForm({
  commentable_id: props.commentableId,
  commentable_type: props.commentableType,
  parent_id: null,
  body: '',
});

function handleFocus() {
  opened.value = true;
}

function handleBlur() {
  // Don't close if there's content in the textarea
  if (form.body?.trim()) {
    return;
  }
  
  // Use nextTick to allow potential focus changes within the container
  nextTick(() => {
    setTimeout(() => {
      const activeElement = document.activeElement;
      if (!container.value?.contains(activeElement) && !form.body?.trim()) {
        opened.value = false;
      }
    }, 150);
  });
}

const commentableLowercase = props.commentableType?.toLowerCase();

const createCommentMutation = ({
  'user': useCreateUserComment,
  'announcement': useCreateAnnouncementComment,
  'company': useCreateCompanyComment,
}[commentableLowercase] || useCreateComment)({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Comment Added',
          message: `Your comment was successfully added.`,
        },
        'success'
      );
      
      // Reset form and close
      form.value.body = '';
      opened.value = false;
    },
  },
});

const submitPost = () => {
  if (!form.value.body.trim()) return;
  
  form.value.submit((data) => createCommentMutation.mutateAsync({ data }));
};

</script>
