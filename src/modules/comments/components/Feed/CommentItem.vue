<template>
  <div 
    class="transition-all duration-500 ease-in-out"
    :class="[
      { 'pl-4 md:pl-10' : isReply },
      { 'opacity-0 translate-y-5': !isVisible },
    ]"
  >
    <div
      class="flex gap-4 relative group/container"
      :class="!comment?.creator?.locked_id ? 'opacity-50' : ''"
    >
      <div
        v-if="isReply"
        class="absolute left-[-40px] flex-none w-10"
        :class="{
          'mt-2 md:mt-1 ml-3': minimized,
          'mt-2 md:mt-2 ml-3 md:ml-1': !minimized,
        }"
      >
        <BIconArrowReturnRight class="text-platinum-500 dark:text-midnight-100/40 h-3 w-3 md:h-4 md:w-4 ml-auto md:ml-1 mr-4 md:mr-1.5" />
      </div>
      <div class="flex-none space-y-1 relative">
        <VAvatar
          avatarable="user"
          :avatarable-id="comment?.creator?.locked_id"
          :name="comment?.creator?.full_name"
          :src="comment?.creator?.image"
          :size="avatarSize"
          class="w-10 flex justify-end"
        />
        <RatingMenu
          v-if="comment?.creator && canRate"
          class="px-1 flex items-center justify-start gap-1 mt-2 text-xs text-platinum-950 dark:text-midnight-50 absolute left-1/2 transform -translate-x-1/2 will-change-transform"
          @reacted="hasReacted"
          reactable-type="Comment"
          :reactable-id="comment?.locked_id"
          :ratings="comment?.ratings"
          :force-show="extended"
          :selected-rating="comment?.selected_rating"
        />
      </div>
      <div
        class="flex-1 relative group"
        @click="bodyClicked"
        ref="container"
        tabindex="0"
        @blur="bodyBlurred"
      >
        <div
          class="ring-1 ring-platinum-900/5 dark:ring-midnight-900/5 rounded-lg shadow shadow-gray-400/30 dark:shadow-black/20 relative group-hover:cursor-pointer transition-all group-active:outline group-active:outline-4 group-active:outline-primary-500 bg-platinum-50 dark:bg-midnight-500 group-hover:bg-white dark:group-hover:bg-midnight-400"
          :class="[
            commentClasses?.container,
            size === 'sm' ? 'px-2 py-2' : 'px-3 py-2',
            minimized ? '!px-2 !py-1' : ''
          ]"
        >
          <div
            class="absolute"
            :class="{
              'left-[-8px] top-[12px] z-20': !minimized && isReply,
              'left-[-8px] top-[10px]': !minimized && !isReply,
              'left-[-8px] top-[7px]': minimized && isReply,
              'left-[-8px] top-[12px] z-10': minimized && !isReply,
            }"
          >
            <div class="w-0 h-0 border-y-[7px] border-y-transparent border-r-[8px] border-r-platinum-900/10 dark:border-r-midnight-900/10 absolute left-0 top-0"></div>
            <div
              class="w-0 h-0 border-y-[6px] border-y-transparent border-r-[7px] transition-colors absolute left-[1px] top-[1px] bg-inherit"
              :class="[
                'group-hover:border-r-white dark:group-hover:border-r-midnight-400',
                'border-r-platinum-50 dark:border-r-midnight-500',
                commentClasses?.triangle
              ]"
            ></div>
          </div>
          <div :class="isReply ? '-mt-1' : ''">
            <div class="relative z-10 md:px-1 pt-0 select-none">
              <div class="flex-wrap md:flex md:flex-row md:flex-nowrap justify-between items-center [&>button]:flex md:[&>button]:inline [&>button]:self-start" :class="!comment?.creator && !isReply ? 'mb-1' : ''">
                <span class="flex items-center gap-1.5 w-full md:w-auto justify-between md:justify-start">
                  <VEntityLink
                    v-if="comment?.creator"
                    entity-type="user"
                    :entity-id="comment.creator.locked_id"
                    :value="comment.creator.full_name"
                    :show-icon="false"
                    :show-link-icon="false"
                    :text-class="size === 'sm' ? 'text-sm font-semibold' : 'font-semibold'"
                  />
                  <span 
                    v-else-if="metaJson?.requires_review"
                    class="text-primary-500 mt-1 font-semibold"
                    :class="size === 'sm' ? 'text-xs' : ''"
                  >Comment Pending Review</span>
                  <span 
                    v-else-if="metaJson?.restricted"
                    class="text-platinum-700 dark:text-midnight-200 mt-1 font-semibold"
                    :class="size === 'sm' ? 'text-xs' : ''"
                  >Comment Deleted</span>
                  <span 
                    v-else
                    class="text-platinum-700 dark:text-midnight-200 mt-1 font-semibold"
                    :class="size === 'sm' ? 'text-xs' : ''"
                  >Unknown Error</span>
                  <component
                    v-if="commentTypeIcon"
                    :is="commentTypeIcon.component"
                    :class="commentTypeIcon.class"
                    class="inline"
                  />
                </span>
                <VTooltip :content="`Posted ${formattedDate(comment.created_at)}`">
                  <component
                    :is="canViewConversation ? 'VTextLink' : 'span'"
                    :to="canViewConversation ? { name: 'comments.conversation', params: { id: comment.locked_id } } : undefined"
                    class="text-xxs block cursor-pointer transition-colors mt-0.5 !text-platinum-600 dark:!text-midnight-200/75 hover:!text-platinum-900 dark:hover:!text-midnight-100"
                    :disable-icon="canViewConversation || undefined"
                  >
                    {{ timeAgo(comment.created_at) }}
                  </component>
                </VTooltip>
              </div>
              <div
                :class="[
                  commentClasses?.text,
                  size === 'sm' ? 'text-xs' : 'text-base'
                ]"
                class="text-platinum-950 dark:text-midnight-100"
              >
                <TransitionFade>
                  <div v-if="isEditing" class="mb-1 -mx-1">
                    <form
                      :id="`edit-form-${comment.locked_id}`"
                      class="space-x-2 flex px-1"
                      :class="!isReply ? 'pb-1' : ''"
                      @submit.prevent="submitEdit"
                    >
                      <VField
                        label="Update Comment"
                        :errors="editForm.errors.get('body')"
                        class="w-full"
                        feedback-class="text-xs pt-1"
                        label-class="sr-only"
                      >
                        <div class="flex gap-2">
                          <VTextarea
                            ref="editInput"
                            rows="3"
                            class="border-primary-400 h-auto z-20 flex grow text-xs"
                            wrapper-class="mt-2 !bg-platinum-200 hover:!bg-platinum-100 dark:!bg-midnight-600 dark:hover:!bg-midnight-700"
                            placeholder="Update your comment..."
                            v-model="editForm.body"
                            :max-rows-visible="10"
                            :resize="false"
                            auto-focus
                          ></VTextarea>
                          <div class="flex pt-2">
                            <div class="flex items-center justify-center [&>button]:flex [&>button]:rounded-full [&>button]:!outline-none [&>button]:focus-within:!outline-primary-400 dark:[&>button]:focus-within:!outline-primary-600 gap-2.5">
                              <div class="flex">
                                <VTooltip content="Submit Edit">
                                  <VButton
                                    type="submit"
                                    :form="`edit-form-${comment.locked_id}`"
                                    intent="icon"
                                    square
                                    class="group/btn rounded-full action-btn overflow-visible"
                                  >
                                    <BIconSendCheckFill class="h-4 w-4 text-primary-500 dark:text-primary-500 group-hover/btn:text-primary-600 dark:group-hover/btn:text-primary-400 transition-colors" />
                                  </VButton>
                                </VTooltip>
                              </div>
                              <div class="flex">
                                <VTooltip content="Cancel Edit">
                                  <VButton
                                    type="button"
                                    @click="isEditing = false"
                                    intent="icon"
                                    square
                                    class="group/btn rounded-full action-btn overflow-visible"
                                  >
                                    <BIconXCircleFill class="h-3.5 w-3.5 text-danger-300 dark:text-danger-500 group-hover/btn:text-danger-400 dark:group-hover/btn:text-danger-400 transition-colors" />
                                  </VButton>
                                </VTooltip>
                              </div>
                            </div>
                          </div>
                        </div>
                      </VField>
                    </form>
                  </div>
                  <div v-else>
                    {{ comment.body }}
                  </div>
                </TransitionFade>
              </div>
            </div>
            <div
              v-if="!isEditing"
              class="transition-all duration-1000"
              :class="extended ? 'max-h-[750px] opacity-100' : 'max-h-0 opacity-0'"
            >
              <TransitionFade>
                <div v-if="extendedAfter" class="pt-2">
                  <div
                    v-if="canEdit || canDelete || canReport || canReact"
                    class="px-1 flex items-center justify-start space-x-2 text-platinum-950 dark:text-midnight-50 text-xs overflow-visible"
                  >
                    <ReactionMenu
                      v-if="canReact && hasPermissionToReact"
                      reactableType="Comment"
                      :reactableId="comment?.locked_id"
                      :reactions="comment?.reactions"
                      :size="isReply ? 'sm' : 'base'"
                      :selectedReaction="comment?.selected_reaction || undefined"
                      @reacted="hasReacted"
                      :compact="!mdAndLarger"
                    />
                    <VTextLink
                      v-if="canEdit"
                      class="space-x-1 flex items-center action-btn"
                      :disableIcon="true"
                      linkType="button"
                      @click="isEditing = true"
                    >
                      <BIconPencilSquare class="h-3 w-3 text-platinum-950 dark:text-midnight-50" />
                      <span class="hidden md:inline">Edit</span>
                    </VTextLink>
                    <VTextLink
                      v-if="canDelete"
                      class="space-x-1 flex items-center action-btn"
                      :disableIcon="true"
                      linkType="button"
                      @click="deleteComment"
                    >
                      <BIconTrash class="h-3 w-3 text-danger-500" />
                      <span class="hidden md:inline">Delete</span>
                    </VTextLink>
                    <VTextLink
                      v-if="canReport"
                      class="space-x-1 flex items-center action-btn"
                      :disableIcon="true"
                      linkType="button"
                      @click="reportComment"
                    >
                      <BIconFlag class="h-3 w-3 text-warning-500" />
                      <span class="hidden md:inline">Report</span>
                    </VTextLink>
                  </div>
                  <form
                    v-if="depth < 4"
                    :id="`reply-form-${comment.locked_id}`"
                    class="space-x-2 flex px-1 pt-1"
                    :class="isReply ? 'mb-1' : 'mb-2'"
                    @submit.prevent="submitReply"
                  >
                    <VField
                      label="Reply"
                      :errors="replyForm.errors.get('body')"
                      class="w-full"
                      feedback-class="text-xs pt-1"
                      label-class="sr-only"
                    >
                      <div class="flex gap-2">
                        <VTextarea
                          ref="replyInput"
                          v-if="!isEditing"
                          rows="1"
                          class="border-primary-400 h-auto z-20 flex grow text-xs"
                          :placeholder="comment?.creator?.first_name ? `Reply to ${comment?.creator?.first_name}...` : 'Reply to comment...'"
                          v-model="replyForm.body"
                          :max-rows-visible="10"
                          :resize="false"
                          auto-focus
                        ></VTextarea>
                        <div class="flex items-center justify-center">
                          <div class="flex items-center justify-center [&>button]:flex [&>button]:rounded-full [&>button]:!outline-none [&>button]:focus-within:!outline-primary-400 dark:[&>button]:focus-within:!outline-primary-600">
                            <VTooltip content="Submit Reply">
                              <VButton
                                type="submit"
                                :form="`reply-form-${comment.locked_id}`"
                                intent="icon"
                                class="group/btn rounded-full"
                                square
                              >
                                <BIconSendPlusFill class="z-30 h-5 text-primary-500 dark:text-primary-500 group-hover/btn:text-primary-600 dark:group-hover/btn:text-primary-400 transition-colors" />
                              </VButton>
                            </VTooltip>
                          </div>
                        </div>
                      </div>
                    </VField>
                  </form>
                </div>
              </TransitionFade>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="comment?.replies?.length > 0" class="space-y-3 mt-3">
      <CommentItem
        v-for="replyComment in limitedReplies"
        :key="replyComment.locked_id"
        :comment="replyComment"
        :can-react="canReact"
        :can-rate="canRate"
        :parent-commentable-type="parentCommentableType"
        :parent-commentable-id="parentCommentableId"
        :is-reply="true"
        :depth="depth + 1"
        size="sm"
        @reply="emits('reply', $event)"
        @delete="emits('delete', $event)"
        @edit="emits('edit', $event)"
        @report="emits('report', $event)"
      />
      <span
        v-if="comment?.replies?.length > replyCountLimit"
        class="inline-flex text-platinum-500 dark:text-midnight-100/40 hover:text-platinum-950 dark:hover:text-midnight-50 text-xs cursor-pointer items-center gap-2 transition-colors"
        :class="size === 'sm' ? 'pl-[3.125rem]' : 'pl-3.5'"
        @click="replyCountLimit += 2"
      >
        <BIconArrowsExpand class="h-3 w-3" />
        <span>Show more replies</span>
      </span>
    </div>
  </div>
</template>

<script setup>
import { timeAgo, formattedDate } from '@/util/dateUtils';
import { ref, inject, computed, watch, onMounted } from 'vue';
import { useBreakpoints, breakpointsTailwind } from '@vueuse/core';
import { useAuth } from '@/store';
import BIconGem from '~icons/bi/gem';
import BIconHeadset from '~icons/bi/headset';
import BIconArrowReturnRight from '~icons/bi/arrow-return-right';
import BIconSendCheckFill from '~icons/bi/send-check-fill';
import BIconSendPlusFill from '~icons/bi/send-plus-fill';
import BIconXCircleFill from '~icons/bi/x-circle-fill';
import BIconFlag from '~icons/bi/flag';
import BIconPencilSquare from '~icons/bi/pencil-square'; // TODO add edit feature.
import BIconTrash from '~icons/bi/trash';
import BIconArrowsExpand from '~icons/bi/arrows-expand';
import { useForm } from '@/hooks/form';
import ReactionMenu from '@/components/ui/reactions/ReactionMenu.vue';
import RatingMenu from '@/components/ui/reactions/RatingMenu.vue';
const props = defineProps({
  comment: {
    type: Object,
    required: true,
  },
  isReply: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String,
    default: 'base',
    validator(value) {
      return ['base', 'sm', 'xs'].includes(value);
    },
  },
  parentCommentableType: {
    type: String,
    default: null,
  },
  parentCommentableId: {
    type: String,
    default: null,
  },
  depth: {
    type: Number,
    default: 0,
  },
  canReact: {
    type: Boolean,
    default: false,
  },
  canRate: {
    type: Boolean,
    default: false,
  },
});

const auth = useAuth();
const globalConfirm = inject('globalConfirm');
const emits = defineEmits(['reply', 'delete', 'edit', 'report']);

const canViewConversation = computed(() => {
  return auth.can('comments.view.any') || auth.can('comments.view');
});

const submitReply = () => {
  emits('reply', replyForm.value);
};

const submitEdit = () => {
  // Add success callback to the form to close editing mode
  editForm.value.onSuccess = () => {
    isEditing.value = false;
  };
  emits('edit', editForm.value);
};

const extended = ref(false);
const editInput = ref(null);
const extendedAfter = ref(false);
const replyInput = ref(null);

// Used for transition delay purposes.
watch(extended, (value) => {
  if (value) {
    extendedAfter.value = true;
  } else {
    setTimeout(() => {
      extendedAfter.value = false;
    }, 1000);
  }
});

// TODO: Add permissions/checks.
const canEdit = ref(true);
const canDelete = ref(true);
const canReport = ref(true);
const hasPermissionToReact = ref(true);

const isEditing = ref(false);

const typeClassMap = {
  featured: {
    container: 'border-r-4 border-primary-300 dark:border-primary-700',
    triangle: '',
    text: '',
  },
  staff: {
    container: 'border-2 outline outline-2 outline-primary-200 dark:outline-primary-800 border-primary-500 dark:border-primary-700',
    triangle: '',
    text: 'font-semibold',
  },
  vip: {
    container: 'border-r-4 border-yellow-500',
    triangle: '',
    text: '',
  },
};

const commentIconMap = {
  staff: {
    component: BIconHeadset,
    class: 'text-primary-500 h-3.5 w-3.5'
  },
  vip: {
    component: BIconGem,
    class: 'text-yellow-500 h-3 w-3'
  }
};

const breakpoints = useBreakpoints(breakpointsTailwind);
const mdAndLarger = breakpoints.greaterOrEqual('md');

const lowerType = props.comment.type?.name?.toLowerCase();

const commentTypeIcon = (() => {
  if (!lowerType) return null;
  return Object.entries(commentIconMap).find(([key]) => lowerType.includes(key))?.[1] || null;
})();

const commentClasses = (() => {
  if (!lowerType) return null;
  return Object.entries(typeClassMap).find(([key]) =>
    lowerType.includes(key)
  )?.[1] || {
    container: '',
    arrow: '',
    text: '',
  };
})();

const bodyClicked = (e) => {
  if (
    props.comment.body &&
    ['DIV'].includes(e.target.tagName)
    && (
      !e.target.classList.contains('action-btn')
      || !e.target.parentElement.classList.contains('action-btn')
    )
  ) {
    if (!isEditing.value) {
      extended.value = true;
    }
  }
};

const isCheckingClose = ref(false);
const container = ref(null);

const bodyBlurred = () => {
  if (isCheckingClose.value) return;
  isCheckingClose.value = true;

  // After 2 seconds, if the focus is outside the container, close.
  const interval = setInterval(() => {
    const active = document.activeElement;
    if (!container?.value?.contains(active)) {
      // Focus is outside the container
      extended.value = false;
      clearInterval(interval);
      isCheckingClose.value = false;
    }
  }, 2000);
};

const trimmedBody = computed(() => {
  return props.comment.body.length > 100
    ? props.comment.body.slice(0, 100) + '...'
    : props.comment.body;
});

const replyForm = useForm({
  parent_id: props.comment.locked_id,
  commentable_type: props.parentCommentableType,
  commentable_id: props.parentCommentableId,
  body: '',
});

const editForm = useForm({
  id: props.comment.locked_id,
  commentable_type: props.parentCommentableType,
  commentable_id: props.parentCommentableId,
  body: props.comment.body,
});

/**
 * The user wishes to delete the comment.
 */
const deleteComment = async () => {
  const confirmed = await globalConfirm.value.showConfirm(
    'Delete Comment',
    `<p>You are about to delete the comment: <b>${trimmedBody.value}</b></p>`
    + `<p>Created by the user: <b>${props.comment?.creator?.full_name}</b>.</p>`
    + `<p>Please confirm your action.</p>`
  );
  
  if (confirmed) {
    emits('delete', props.comment.locked_id);
  }
};

/**
 * The user wishes to report the comment. Launches a modal on the parent component requesting some details.
 */
const reportComment = () => {
  emits('report', {
    id: props.comment.locked_id,
    body: trimmedBody.value,
    creator: props.comment?.creator?.full_name,
  });
};

const replyCountLimit = ref(2);
const limitedReplies = computed(() => {
  // Get the first 2 replies (should already be sorted by rating/oldest first).
  return props.comment?.replies?.slice(0, replyCountLimit.value) || [];
});

const isVisible = ref(false);

onMounted(() => {
  setTimeout(() => {
    isVisible.value = true;
  }, 100);
});

/**
 * The user has reacted to the comment.
 * @param name - The name of the reaction
 */
const hasReacted = (name) => {
  container.value.focus(); // Prevent the reaction from causing focus to be lost.
}

const metaJson = computed(() => {
  return JSON.parse(props.comment?.status?.meta);
});

const minimized = computed(() => {
  return metaJson.value?.restricted || metaJson.value?.requires_review;
});

const avatarSize = computed(() => {
  // mdAndLarger ? size : 'sm'
  if (mdAndLarger.value && !minimized.value && !props.isReply) return props.size; // Standard non-reply.
  if (mdAndLarger.value && minimized.value && !props.isReply) return 'sm'; // Deleted or restricted non-reply.
  if (mdAndLarger.value && !minimized.value && props.isReply) return 'sm'; // Standard reply.
  return 'xs'; // All others won't show the avatar anyway, so should be xs.
})

</script>

<style scoped>
.v-enter-active,
.v-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style>
