import UserLayout from '@/layouts/UserLayout.vue';
import { auth } from '@/router/middleware';

const routes = [
  {
    path: '/comments',
    component: UserLayout,
    meta: {
      feature: 'feature.comments',
      middleware: auth,
    },
    children: [
      {
        path: '',
        name: 'comments.index',
        component: () => import('@/modules/comments/pages/CommentIndex.vue'),
        props: () => ({
          management: true,
        }),
        meta: {
          title: 'Comments',
          breadcrumbs: [
            { label: 'Comments', name: 'comments.index' },
          ],
          permissions: ['comments.index.any'],
        },
      },
      {
        path: ':id/view',
        name: 'comments.view',
        component: () => import('@/modules/comments/pages/CommentView.vue'),
        props: (route) => ({
          id: route.params.id,
          management: true,
        }),
        meta: {
          title: 'View Comment',
          breadcrumbs: [
            { label: 'Comments', name: 'comments.index' },
            { label: 'View', name: 'comments.view' },
          ],
          permissions: ['comments.view.any'],
        },
      },
      {
        path: ':id/edit',
        name: 'comments.update',
        component: () => import('@/modules/comments/pages/CommentUpdate.vue'),
        props: true,
        meta: {
          title: 'Edit Comment',
          breadcrumbs: [
            { label: 'Comments', name: 'comments.index' },
            { label: 'Edit', name: 'comments.update' },
          ],
          permissions: ['comments.update.any'],
        },
      },
      {
        path: ':id',
        name: 'comments.conversation',
        component: () => import('@/modules/comments/pages/CommentConversation.vue'),
        props: true,
        meta: {
          title: 'View Conversation',
          breadcrumbs: [
            { label: 'Comments', name: 'comments.index' },
            { label: 'Conversation', name: 'comments.tree' },
          ],
          permissions: ['comments.view', 'comments.view.any'],
        },
      },
      {
        path: ':id/activity',
        name: 'comments.activity',
        component: () => import('@/modules/activity/pages/ActivityIndex.vue'),
        props: (route) => ({
          activitableType: 'Comment',
          activitableId: route.params.id,
        }),
        meta: {
          title: 'Comment Activity',
          headerTitle: 'Comment Activity',
          headerDescription: 'View the activity for the selected comment.',
          breadcrumbs: [
            { label: 'Comments', name: 'comments.index' },
            { label: 'View', name: 'comments.view' },
            { label: 'Activity', name: 'comments.activity' },
          ],
        },
      },
    ],
  },
];

export default routes; 