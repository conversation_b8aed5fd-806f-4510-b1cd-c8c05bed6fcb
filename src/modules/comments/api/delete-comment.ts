import { useMutation, useQueryClient } from '@tanstack/vue-query';
import { MutationConfig } from '@/lib/vue-query';
import { getCommentsQueryOptions } from './get-comments';
import { getCommentsQueryOptions as getUserCommentsQueryOptions } from '@/modules/users/api/get-user-comments';

// Declare the Reshape global variable
declare const Reshape: {
  http: () => {
    delete: (url: string) => Promise<any>;
  };
};

export const deleteComment = ({ commentId }: { commentId: string }) => {
  return Reshape.http().delete(`/api/comments/${commentId}`);
};

type UseDeleteCommentOptions = {
  mutationConfig?: {
    onSuccess?: (...args: any[]) => void;
    [key: string]: any;
  };
};

export const useDeleteComment = ({ mutationConfig }: UseDeleteCommentOptions = {}) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      // Remove the specific comment from cache
      queryClient.removeQueries({
        queryKey: ['comments', args[1].commentId],
        exact: true,
      });

      // Invalidate all comment lists
      queryClient.invalidateQueries({
        queryKey: ['comments'],
        refetchType: 'all',
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteComment,
  });
}; 