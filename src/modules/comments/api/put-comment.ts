import { useMutation, useQueryClient } from '@tanstack/vue-query';
import { MutationConfig } from '@/lib/vue-query';
import { getCommentQueryOptions } from './get-comment';
import { Comment } from '@/types/api/comment';

// Declare the Reshape global variable
declare const Reshape: {
  http: () => {
    put: (url: string, data: any) => Promise<any>;
  };
};
export const updateComment = ({ commentId, data }: { commentId: string, data: UpdateCommentPayload }) => {
  return Reshape.http().put(`/api/comments/${commentId}`, data);
};

type UpdateCommentPayload = Partial<Omit<Comment, 'locked_id'>>;

type UseUpdateCommentOptions = {
  mutationConfig?: MutationConfig<typeof updateComment>;
};

export const useUpdateComment = ({ mutationConfig }: UseUpdateCommentOptions = {}) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['comments', args[1].commentId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getCommentQueryOptions(args[1].commentId).queryKey,
      });

      // Invalidate all comment list queries that might contain this comment
      queryClient.invalidateQueries({
        queryKey: ['comments'],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: updateComment,
  });
};