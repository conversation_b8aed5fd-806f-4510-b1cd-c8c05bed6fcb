import { useQuery, queryOptions } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Comment } from '@/types/api';
import { Reshape } from '@/Reshape';

export const getComment = ({
  commentId,
}: {
  commentId: string;
}): Promise<{ data: Comment }> => {
  return Reshape.http()
    .get(`/api/comments/${commentId}`)
    .then(({ data }) => data);
};

export const getCommentQueryOptions = (commentId: string) => {
  return queryOptions({
    queryKey: ['comments', commentId],
    queryFn: () => getComment({ commentId }),
  });
};

type UseCommentOptions = {
  commentId: string;
  queryConfig?: QueryConfig<typeof getCommentQueryOptions>;
};

export const useComment = ({ commentId, queryConfig }: UseCommentOptions) => {
  return useQuery({
    ...getCommentQueryOptions(commentId),
    ...queryConfig,
  });
}; 