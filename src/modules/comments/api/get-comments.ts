import { queryOptions, useQuery } from '@tanstack/vue-query';
import { Comment, Meta } from '@/types/api';
import { QueryConfig } from '@/lib/vue-query';
import { Reshape } from '@/Reshape';

export const getComments = (
  params,
  signal
): Promise<{
  data: Comment[];
  meta: Meta;
}> => {
  return Reshape.http().get(
    `/api/comments`,
    {
      params,
      signal,
    }
  );
};

export const getCommentsQueryOptions = ({ params }: { params?: unknown } = {}) => {
  return queryOptions({
    queryKey: params ? ['comments', params] : ['comments'],
    queryFn: ({ signal }) => getComments(params, signal),
  });
};

type UseCommentsOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getCommentsQueryOptions>;
};

export const useComments = ({ queryConfig, params }: UseCommentsOptions = {}) => {
  return useQuery({
    ...getCommentsQueryOptions({ params }),
    ...queryConfig,
  });
}; 