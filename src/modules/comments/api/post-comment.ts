import { useMutation, useQueryClient } from '@tanstack/vue-query';
import { MutationConfig } from '@/lib/vue-query';
import { Comment } from '@/types/api/comment';

// Declare the Reshape global variable
declare const Reshape: {
  http: () => {
    post: (url: string, data: any) => Promise<any>;
  };
};
export const createComment = ({ data }: { data: CreateCommentPayload }) => {
  return Reshape.http().post(`/api/comments`, data);
};

type CreateCommentPayload = Omit<Comment, 'id' | 'locked_id'>;

type UseCreateCommentOptions = {
  mutationConfig?: MutationConfig<typeof createComment>;
};

export const useCreateComment = ({ mutationConfig }: UseCreateCommentOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ['comments'],
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createComment,
  });
};