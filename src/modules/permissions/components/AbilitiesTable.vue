<template>
  <Table v-bind="$attrs" :columns="columns" paginated sortable searchable controlled />
</template>

<script setup>
import { h, ref } from 'vue';

import Table from '@/components/ui/tables/Table.vue';
import { humanizeTime } from '@/util/dateUtils';
import AbilityTableActionCell from '@/modules/permissions/components/AbilitiesTableActionCell.vue';
const columns = ref([
  {
    accessorKey: 'title',
    header: 'Title',
    cell: (info) => info.getValue(),
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: (info) => {
      const desc = info.getValue();
      if (!desc) return '';
      return desc.length > 60 ? desc.substring(0, 60) + '...' : desc;
    },
  },
  {
    accessorKey: 'name',
    header: 'Name',
    cell: (info) => info.getValue(),
  },
  {
    accessorKey: 'created_at',
    header: 'Created Date',
    cell: (info) => humanizeTime(info.getValue()),
  },
  {
    id: 'actions',
    header: () => h('span', { class: 'w-full flex justify-end' }),
    cell: (info) => h(AbilityTableActionCell, { row: info.row.original }),
    enableSorting: false,
    enableResizing: false,
    size: 50,
  },
]);
</script>
