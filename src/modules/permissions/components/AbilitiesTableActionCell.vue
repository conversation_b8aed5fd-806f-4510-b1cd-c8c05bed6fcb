<template>
  <div class="flex items-center justify-end space-x-1.5">
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <VButton intent="table">
          <BIconThreeDots class="h-5 w-5" />
        </VButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem as-child>
          <RouterLink
            :to="{
              name: 'settings.abilities.view',
              params: { id: row.locked_id },
            }"
          >
            <BIconSearch />View
          </RouterLink>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<script setup lang="ts">
import BIconSearch from '~icons/bi/search';
import BIconThreeDots from '~icons/bi/three-dots';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from '@/components/ui/shadcn/dropdown-menu';

interface AbilityRow {
  locked_id: string;
  name: string;
  [key: string]: any;
}

const props = defineProps<{
  row: AbilityRow;
}>();
</script>
