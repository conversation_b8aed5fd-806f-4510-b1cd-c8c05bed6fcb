import { useQuery, queryOptions } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Ability } from '@/types/api';

export const getAbility = ({
  abilityId,
}: {
  abilityId: string;
}): Promise<{ data: Ability }> => {
  return Reshape.http()
    .get(`/api/abilities/${abilityId}`)
    .then(({ data }) => data);
};

export const getAbilityQueryOptions = (abilityId: string) => {
  return queryOptions({
    queryKey: ['abilities', abilityId],
    queryFn: () => getAbility({ abilityId }),
  });
};

type UseAbilityOptions = {
  abilityId: string;
  queryConfig?: QueryConfig<typeof getAbilityQueryOptions>;
};

export const useAbility = ({ abilityId, queryConfig }: UseAbilityOptions) => {
  return useQuery({
    ...getAbilityQueryOptions(abilityId),
    ...queryConfig,
  });
}; 