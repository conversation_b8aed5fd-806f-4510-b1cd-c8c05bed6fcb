import { queryOptions, useQuery } from '@tanstack/vue-query';
import { Ability, Meta } from '@/types/api';

import { QueryConfig } from '@/lib/vue-query';

export const getAbilities = (
  params,
  signal
): Promise<{
  data: Ability[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/abilities`, {
    params,
    signal,
  });
};

export const getAbilitiesQueryOptions = ({ params }: { params?: unknown } = {}) => {
  return queryOptions({
    queryKey: params ? ['abilities', params] : ['abilities'],
    queryFn: ({ signal }) => getAbilities(params, signal),
  });
};

type UseAbilitiesOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getAbilitiesQueryOptions>;
};

export const useAbilities = ({ queryConfig, params }: UseAbilitiesOptions = {}) => {
  return useQuery({
    ...getAbilitiesQueryOptions({ params }),
    ...queryConfig,
  });
}; 