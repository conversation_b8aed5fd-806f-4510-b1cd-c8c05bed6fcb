import UserLayout from '@/layouts/UserLayout.vue';
import { auth } from '@/router/middleware';

export default [
  {
    path: '/settings/abilities',
    component: UserLayout,
    meta: {
      middleware: auth,
    },
    children: [
      {
        path: '',
        name: 'settings.abilities.index',
        component: () => import('./pages/AbilitiesIndex.vue'),
        meta: {
          title: 'Abilities',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Abilities', name: 'settings.abilities.index' },
          ],
        },
      },
      {
        path: ':id/view',
        name: 'settings.abilities.view',
        component: () => import('./pages/AbilitiesView.vue'),
        props: true,
        meta: {
          title: 'View Role',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Abilities', name: 'settings.abilities.index' },
            { label: 'View', name: 'settings.abilities.view' },
          ],
        },
      },
    ],
  },
];
