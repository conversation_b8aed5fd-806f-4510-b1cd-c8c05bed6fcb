<template>
  <div>
    <VPageHeader
      title="Abilities"
      description="Assignable abilities for use primarily in roles."
    />

    <VContainer class="py-2 lg:py-6">
      <AbilitiesTable
        title="All Abilities"
        :records="abilities?.data"
        :total-records="abilities?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
      />
    </VContainer>
  </div>
</template>

<script setup lang="ts">
import { useFilterParams } from '@/hooks/query';
import AbilitiesTable from '@/modules/permissions/components/AbilitiesTable.vue';
import { useAbilities } from '@/modules/permissions/api/get-abilities';

const { params, search, sort, paginate } = useFilterParams();

const { data: abilities, isLoading } = useAbilities({
  params: params.value,
});
</script>