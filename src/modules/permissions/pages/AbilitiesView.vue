<template>
  <div>
    <VPageHeader title="View Ability" description="Overview of selected ability." />
    <VContainer class="my-3">
      <TransitionFade>
        <div v-if="isLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>
        <div v-else-if="ability" class="grid gap-3 lg:grid-cols-5 items-start">
          <ViewPanel class="col-span-5 xl:col-span-4">
            <PanelDefaultField label="Title" :value="ability?.title" allowCopy />
            <PanelDefaultField label="Description" :value="ability?.description" allowCopy />
            <PanelDefaultField label="Name" :value="ability?.name" allowCopy />
            <PanelDateTimeField label="Date Created" :value="ability?.created_at" />
            <PanelDateTimeField label="Date Updated" :value="ability?.updated_at" />
          </ViewPanel>

          <VCard class="col-span-5 xl:col-span-1">
            <VCardHeader>Actions</VCardHeader>
            <VCardBody class="flex gap-3 justify-center flex-wrap">
              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'settings.abilities.index' }"
              >All Abilities
                <template #start>
                  <BIconListUl />
                </template>
              </VButton>
            </VCardBody>
          </VCard>
        </div>
        <template v-else>
          <VEmptyState
            title="Ability Not Found"
            description="The requested ability was not found."
            :contact="true"
          />
        </template>
      </TransitionFade>
    </VContainer>
  </div>
</template>

<script setup lang="ts">
import type { Ability } from '@/types/api/ability';
import BIconListUl from '~icons/bi/list-ul';
import ViewPanel from '@/components/ui/panels/ViewPanel.vue';
import PanelDefaultField from '@/components/ui/panels/fields/ViewPanel/PanelDefaultField.vue';
import PanelDateTimeField from '@/components/ui/panels/fields/ViewPanel/PanelDateTimeField.vue';
import { useAbility } from '@/modules/permissions/api/get-ability';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const { data: ability, isLoading } = useAbility({
  abilityId: props.id,
});

</script>
