import { useMutation } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';
import { Reshape } from '@/Reshape';

export const updateOnboardingStep = ({ step, data }: { step: string, data: UpdateOnboardingStepPayload }) => {
  return Reshape.http().put(`/api/profile-setup/${step}`, data);
};

type UpdateOnboardingStepPayload = {
  profile_photo?: File;
  from_country_id?: string;
  from_country_extra?: string;
  intro?: string;
  bio?: string;
};

type UseUpdateOnboardingStepOptions = {
  step: string;
  mutationConfig?: MutationConfig<typeof updateOnboardingStep>;
};

export const useUpdateOnboardingStep = ({ step, mutationConfig }: UseUpdateOnboardingStepOptions) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: (data: UpdateOnboardingStepPayload) => updateOnboardingStep({ step, data }),
  });
};
