import { useMutation } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';
import { Reshape } from '@/Reshape';

export const createOnboardingStep = ({ data }: { data: CreateOnboardingStepPayload }) => {
  return Reshape.http().post('/api/profile-setup', data);
};

type CreateOnboardingStepPayload = {
  step?: number;
  // Step 1 fields only
  first_name?: string;
  middle_names?: string;
  last_name?: string;
  current_country_id?: string;
  current_country_extra?: string;
  honorific_id?: string;
  timezone_id?: string;
  job_title?: string;
};

type UseCreateOnboardingStepOptions = {
  mutationConfig?: MutationConfig<typeof createOnboardingStep>;
};

export const useCreateOnboardingStep = ({ mutationConfig }: UseCreateOnboardingStepOptions = {}) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createOnboardingStep,
  });
};
