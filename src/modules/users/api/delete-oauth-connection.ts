import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';
import { getOAuthConnectionsQueryOptions } from './get-oauth-connections';
import { Reshape } from '@/Reshape';

export const deleteOAuthConnection = ({ provider }: { provider: string }) => {
  return Reshape.http().delete(`/api/profile/oauth-connections/${provider}`);
};

type UseDeleteOAuthConnectionOptions = {
  mutationConfig?: MutationConfig<typeof deleteOAuthConnection>;
};

export const useDeleteOAuthConnection = ({ mutationConfig }: UseDeleteOAuthConnectionOptions = {}) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    mutationFn: deleteOAuthConnection,
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({
        queryKey: getOAuthConnectionsQueryOptions().queryKey,
      });
      onSuccess?.(data, variables, context);
    },
    ...restConfig,
  });
};
