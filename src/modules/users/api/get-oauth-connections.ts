import { queryOptions, useQuery } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { OAuthConnection } from '@/types/api';
import { Reshape } from '@/Reshape';

export const getOAuthConnections = (): Promise<{ data: OAuthConnection[] }> => {
  return Reshape.http().get('/api/profile/oauth-connections');
};

export const getOAuthConnectionsQueryOptions = () => {
  return queryOptions({
    queryKey: ['oauth-connections'],
    queryFn: getOAuthConnections,
  });
};

type UseOAuthConnectionsOptions = {
  queryConfig?: QueryConfig<typeof getOAuthConnectionsQueryOptions>;
};

export const useOAuthConnections = ({ queryConfig }: UseOAuthConnectionsOptions = {}) => {
  return useQuery({
    ...getOAuthConnectionsQueryOptions(),
    ...queryConfig,
  });
};
