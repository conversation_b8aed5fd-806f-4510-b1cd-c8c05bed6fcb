<template>
  <VCard class="shadow-xl p-8 z-20 overflow-visible">
    <Confetti
      ref="confettiRef"
      :colors="confettiColors"
      @fired="onConfettiFired"
    />

    <div class="space-y-10 text-center">
      <div class="space-y-6">
        <div class="mx-auto rounded-full flex items-center justify-center">
          <NotoIconCheckmarkCircle class="w-14 h-14" />
        </div>
        <div class="space-y-3">
          <h2 class="text-4xl font-bold text-primary-600 dark:text-primary-400">Let's get started!</h2>
          <p class="text-lg text-primary-700 dark:text-midnight-50 max-w-lg mx-auto leading-relaxed">
            You're all set and ready to use the platform! You can update your profile anytime to add or change your details.
          </p>
        </div>
      </div>

      <div class="space-y-12">
        <VButton
          intent="primary"
          size="lg"
          as="router-link"
          :to="{ name: 'index' }"
        >Go to Dashboard
          <template #start>
            <BIconSpeedometer />
          </template>
        </VButton>

        <div class="bg-gradient-to-r from-primary-50 to-primary-300/50 dark:from-primary-900/20 dark:to-primary-800/50 rounded-xl p-3 border border-primary-200 dark:border-primary-700">
          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-primary-200 dark:bg-primary-800 rounded-full flex items-center justify-center">
                <BIconInfoCircle class="w-5 h-5 text-primary-600 dark:text-primary-400" />
              </div>
            </div>
            <div class="text-left space-y-1">
              <h3 class="font-semibold text-primary-600 dark:text-primary-300">Reminder</h3>
              <p class="text-sm text-primary-500 dark:text-primary-400 leading-relaxed">
                If you need help or have questions, our <TextLink :to="{ name: 'support.index'}" disableIcon>support area</TextLink> is a great place to find answers and helpful resources to get you started.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </VCard>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import BIconInfoCircle from '~icons/bi/info-circle';
import BIconSpeedometer from '~icons/bi/speedometer';
import NotoIconCheckmarkCircle from '~icons/noto/partying-face';
import Confetti from '@/components/ui/effects/Confetti.vue';
import { getThemeColorsAsHex } from '@/util/colorUtils';

const emit = defineEmits(['complete']);

const confettiRef = ref(null);

const confettiColors = computed(() => getThemeColorsAsHex(['--primary-200', '--primary-400', '--primary-600', '--primary-800']));

onMounted(() => {
  setTimeout(() => {
    if (confettiRef.value) {
      confettiRef.value.fireRealistic();
    }
  }, 250);
});
</script>
