<template>
  <VCard class="shadow-xl p-8 z-20 overflow-visible space-y-8">
    <div class="text-center space-y-1">
      <h2 class="text-4xl font-bold text-primary-500 dark:text-primary-400">Welcome,</h2>
      <p class="text-platinum-950 dark:text-midnight-100">
        Let's get to know each other!
      </p>
    </div>

    <form class="grid grid-cols-6 gap-6" @submit.prevent="submitAbout">
      <VField 
        label="Title" 
        class="col-span-6 md:col-span-2"
        :errors="form.errors.get('honorific_id')"
      >
        <HonorificCombobox v-model="form.honorific_id" />
      </VField>

      <VField 
        class="col-span-6 md:col-span-4" 
        label="First Name" 
        required
        :errors="form.errors.get('first_name')"
      >
        <VInput 
          v-model="form.first_name" 
          placeholder="Enter First Name" 
          required
        />
      </VField>

      <VField 
        class="col-span-6 md:col-span-3" 
        label="Middle Name(s)"
        :errors="form.errors.get('middle_names')"
      >
        <VInput 
          v-model="form.middle_names" 
          placeholder="Enter Middle Name(s)" 
        />
      </VField>

      <VField 
        class="col-span-6 md:col-span-3" 
        label="Last Name" 
        required
        :errors="form.errors.get('last_name')"
      >
        <VInput 
          v-model="form.last_name" 
          placeholder="Enter Last Name" 
          required
        />
      </VField>

      <VField 
        class="col-span-6 sm:col-span-3" 
        label="Country Based"
        required
        :errors="form.errors.get('current_country_id')"
      >
        <CountryCombobox v-model="form.current_country_id" />
      </VField>

      <VField 
        class="col-span-6 sm:col-span-3" 
        label="Location"
        help="City, town, or specific location"
        :errors="form.errors.get('current_country_extra')"
      >
        <VInput 
          v-model="form.current_country_extra" 
          placeholder="City, Town, etc." 
        />
      </VField>

      <VField
        class="col-span-6 sm:col-span-2"
        label="Local Timezone"
        required
        :errors="form.errors.get('timezone_id')"
      >
        <TimezoneCombobox v-model="form.timezone_id" :default-option="defaultTimezone" />
      </VField>

      <VField
        class="col-span-6 sm:col-span-4"
        label="Job Title"
        required
        :errors="form.errors.get('job_title')"
      >
        <VInput
          v-model="form.job_title"
          placeholder="Enter Job Title"
          required
        />
      </VField>

      <div class="flex justify-center pt-6 col-span-6">
        <VButton
          type="submit"
          intent="primary"
          size="lg"
          :disabled="!isValid"
          class="px-8"
        >
          Next
          <template #end>
            <BIconArrowRight />
          </template>
        </VButton>
      </div>
    </form>
  </VCard>
</template>

<script setup>
import { computed } from 'vue';
import { useForm } from '@/hooks/form';
import { useCreateOnboardingStep } from '@/modules/users/api/post-onboarding-step';
import CountryCombobox from '@/components/ui/form/composables/CountryCombobox.vue';
import HonorificCombobox from '@/components/ui/form/composables/HonorificCombobox.vue';
import TimezoneCombobox from '@/components/ui/form/composables/TimezoneCombobox.vue';
import BIconArrowRight from '~icons/bi/arrow-right';

const defaultTimezone = import.meta.env.VITE_DEFAULT_TIMEZONE

const emit = defineEmits(['next']);

// Form for step 1 data
const form = useForm({
  honorific_id: '',
  current_country_id: '',
  current_country_extra: '',
  first_name: '',
  middle_names: '',
  last_name: '',
  timezone_id: '',
  job_title: '',
});

// Create mutation for step 1
const createOnboardingMutation = useCreateOnboardingStep({
  mutationConfig: {
    onSuccess: (result) => {
      if (result.success) {
        // Emit success to parent
        emit('next');
      } else {
        // Show error toast
        Reshape.toast(
          {
            title: 'Profile Setup Error',
            message: result.data?.message || 'Failed to save profile information.',
          },
          'danger'
        );
      }
    },
    onError: (error) => {
      console.error('Profile setup error:', error);

      // Show error toast
      Reshape.toast(
        {
          title: 'Profile Setup Error',
          message: 'Failed to save profile information. Please try again.',
        },
        'danger'
      );
    }
  },
});

// Computed property to check if required fields are filled
const isValid = computed(() => {
  return form.value.first_name && form.value.last_name && form.value.current_country_id && form.value.timezone_id && form.value.job_title;
});

const submitAbout = () => {
  if (isValid.value) {
    form.value.submit((data) => {
      return createOnboardingMutation.mutateAsync({
        data: {
            ...data,
            step: 1
          }
      });
    });
  }
};
</script>
