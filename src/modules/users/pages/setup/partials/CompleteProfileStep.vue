<template>
  <VCard class="shadow-xl p-8 z-20 overflow-visible space-y-8">
    <div class="text-center space-y-1">
      <h2 class="text-3xl font-bold text-primary-500 dark:text-primary-400">
        Complete Your Profile
      </h2>
      <p class="text-platinum-950 dark:text-midnight-100">
        Provide some additional details to enhance your profile.
      </p>
    </div>

    <form class="grid grid-cols-6 gap-6" @submit.prevent="submitCompleteProfile">
      
      <VField 
        class="col-span-6" 
        label="Profile Photo"
        help="Upload a profile picture"
        :errors="form.errors.get('profile_photo')"
      >
        <VFile 
          v-model="form.profile_photo" 
          accepted="image/*"
          name="profile_photo"
        />
      </VField>
      
      <VField
        class="col-span-6 sm:col-span-3" 
        label="From Country"
        help="Let people know where you're from if you want."
        :errors="form.errors.get('from_country_id')"
      >
        <CountryCombobox v-model="form.from_country_id" />
      </VField>

      <VField 
        class="col-span-6 sm:col-span-3" 
        label="From Location"
        help="City, town, or specific location you're from."
        :errors="form.errors.get('from_country_extra')"
      >
        <VInput 
          v-model="form.from_country_extra" 
          placeholder="City, Town, etc." 
        />
      </VField>

      <VField 
        class="col-span-6" 
        label="Introduction"
        help="A brief introduction visible on your user profile."
        :errors="form.errors.get('intro')"
      >
        <VTextarea
          v-model="form.intro"
          placeholder="A little about yourself in a few words..."
          rows="2"
          :maxlength="200"
        />
      </VField>

      <VField 
        class="col-span-6" 
        label="Bio"
        help="A more detailed description for your profile."
        :errors="form.errors.get('bio')"
      >
        <VTextarea
          v-model="form.bio"
          placeholder="Share more details about your background, interests, experience..."
          rows="4"
          :maxlength="1000"
        />
      </VField>
      
      <div class="flex justify-center col-span-6">
        <VButton
          type="submit"
          :intent="isSkipping ? 'primary' : 'success'"
          size="lg"
        >
          {{ isSkipping ? 'Complete Later' : 'Complete Profile Setup' }}
          <template #end>
            <BIconArrowRight />
          </template>
        </VButton>
      </div>
    </form>

  </VCard>
</template>

<script setup>
import { computed } from 'vue';
import { useForm } from '@/hooks/form';
import { useUpdateOnboardingStep } from '@/modules/users/api/put-onboarding-step';
import CountryCombobox from '@/components/ui/form/composables/CountryCombobox.vue';
import VFile from '@/components/ui/form/VFile.vue';

import BIconArrowRight from '~icons/bi/arrow-right';

const emit = defineEmits(['next', 'back']);

// Form for step 2 data.
const form = useForm({
  profile_photo: '',
  from_country_id: '',
  from_country_extra: '',
  intro: '',
  bio: '',
});

// Computed property to check if user is skipping (no values provided).
const isSkipping = computed(() => {
  const formData = form.value;
  return !formData.profile_photo &&
    !formData.from_country_id &&
    !formData.from_country_extra &&
    !formData.intro &&
    !formData.bio;
});

// Update mutation for step 2.
const updateOnboardingMutation = useUpdateOnboardingStep({
  step: 'profile',
  mutationConfig: {
    onSuccess: (result) => {
      if (result.success) {
        // Emit success to parent.
        emit('next');
      } else {
        Reshape.toast({
          title: 'Complete Profile Error',
          message: result.data?.message || 'Failed to set profile information.',
        }, 'danger');
      }
    },
    onError: (error) => {
      console.error('Profile update error:', error);
      Reshape.toast({
        title: 'Complete Profile Error',
        message: 'Failed to set profile information. Please try again.',
      }, 'danger');
    }
  },
});

const submitCompleteProfile = () => {
  // Only attempt to submit if the form is not empty.
  if (isSkipping.value) {
    emit('next');
    return;
  }

  form.value.submit((data) => {
    return updateOnboardingMutation.mutateAsync(data);
  });
};
</script>
