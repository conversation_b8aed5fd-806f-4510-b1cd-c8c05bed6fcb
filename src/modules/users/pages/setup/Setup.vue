<template>
  <div class="flex flex-col bg-platinum-200 dark:bg-midnight-900">
    <div class="absolute top-0 left-0 h-full w-full">
      <div class="w-full h-full absolute inset-0 left-0 z-10">
        <VImage class="w-full h-full object-cover"/> <!-- Empty image, uses default placeholder -->
        <div class="absolute inset-0 bg-gradient-to-t from-platinum-200 dark:from-midnight-900 to-transparent"></div>
      </div>
    </div>

    <VContainer class="py-12 px-6 w-full my-auto flex items-center justify-center">
      <div class="mx-auto max-w-3xl w-full z-20">
        
        <div class="flex justify-center mb-8">
          <Stepper
            :model-value="currentStep"
            :linear="true"
            class="w-full flex justify-center"
          >
            <StepperItem
              v-for="stepData in stepsWithState"
              :key="stepData.step"
              :step="stepData.step"
              :disabled="true"
              :class="[
                stepData.isLast ? '' : 'basis-1/3',
                stepData.isLast ? '' : 'flex'
              ]"
            >
              <StepperTrigger class="pointer-events-none flex-1 flex flex-col justify-start items-center self-start">
                <StepperIndicator>
                  <TransitionFade>
                    <BIconCheckLg v-if="stepData.isCompleted" class="text-2xl" />
                    <span v-else>{{ stepData.step }}</span>
                  </TransitionFade>
                </StepperIndicator>
                <div class="text-center">
                  <StepperTitle class="block text-platinum-50 dark:text-midnight-50 text-lg">{{ stepData.title }}</StepperTitle>
                  <StepperDescription class="block mt-1 max-w-16 text-center text-xs mx-auto text-platinum-200 dark:text-midnight-100">
                    {{ stepData.description }}
                  </StepperDescription>
                </div>
              </StepperTrigger>
              <StepperSeparator
                v-if="!stepData.isLast"
                class="w-full h-[2px] rounded-full mt-3"
              />
            </StepperItem>
          </Stepper>
        </div>

        <TransitionFadeHeightLeftToRight>
          <AboutYouStep
            v-if="currentStep === 1"
            @next="nextStep"
          />

          <CompleteProfileStep
            v-if="currentStep === 2"
            @next="nextStep"
            @back="previousStep"
          />

          <SetupCompleteStep
            v-if="currentStep === 3"
          />
        </TransitionFadeHeightLeftToRight>
      </div>
    </VContainer>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useAuth } from '@/store';
import BIconCheckLg from '~icons/bi/check-lg';

import {
  Stepper,
  StepperItem,
  StepperTrigger,
  StepperIndicator,
  StepperSeparator,
  StepperTitle,
  StepperDescription
} from '@/components/ui/shadcn/stepper';

// Step components
import AboutYouStep from '@/modules/users/pages/setup/partials/AboutYouStep.vue';
import CompleteProfileStep from '@/modules/users/pages/setup/partials/CompleteProfileStep.vue';
import SetupCompleteStep from '@/modules/users/pages/setup/partials/SetupCompleteStep.vue';

// No router needed as navigation is handled in child components

const currentStep = ref(1);

const props = defineProps({
  id: {
    type: String,
    default: null,
  },
  self: {
    type: Boolean,
    default: false,
  },
});

// Reactive steps data
const steps = ref([
  {
    step: 1,
    title: 'About You',
    description: 'Required information',
    hasError: false,
    isCompleted: false,
  },
  {
    step: 2,
    title: 'Your Profile',
    description: 'Optional details',
    hasError: false,
    isCompleted: false,
  },
  {
    step: 3,
    title: 'Complete',
    description: "Let's get started!",
    hasError: false,
    isCompleted: false,
  },
]);

// Computed properties for step states
const stepsWithState = computed(() => {
  return steps.value.map((step, index) => ({
    ...step,
    isActive: currentStep.value === step.step,
    isLast: index === steps.value.length - 1,
    state: step.hasError ? 'error' :
      step.isCompleted ? 'completed' :
      currentStep.value === step.step ? 'active' : 'inactive'
  }));
});

const nextStep = () => {
  if (currentStep.value < steps.value.length) {
    // Mark current step as completed
    const currentStepIndex = currentStep.value - 1;
    steps.value[currentStepIndex].isCompleted = true;
    steps.value[currentStepIndex].hasError = false;

    // Move to next step
    currentStep.value++;

    // If we're moving to the final step, reload user data
    if (currentStep.value === 3) {
      const auth = useAuth();
      auth.getUser();
    }
  }
};

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};
</script>
