<template>
  <div>
    <VPageHeader>
      <template #actions>
        <VButton
          as="router-link"
          :to="{ name: 'profile.view' }"
          intent="secondary"
        >Return to Profile
          <template #start>
            <BIconArrowReturnLeft />
          </template>
        </VButton>
      </template>
    </VPageHeader>
    
    <VContainer class="py-2 lg:py-6">
      <ActionsPanel>
        <ActionsPanelItem
          v-for="(provider, index) in providers"
          :key="provider.slug"
          :item="provider"
          :index="index"
          :total-length="providers.length"
          @connect="connect"
          @disconnect="disconnect"
        >
          <template #actions>
            <TransitionFade>
              <VButton
                v-if="!provider.connected"
                type="button"
                intent="primary"
                size="sm"
                @click="connect(provider.slug)"
              >Connect
                <template #start>
                  <BIconPlus class="overflow-visible text-base" />
                </template>
              </VButton>
              <VButton
                v-else
                type="button"
                intent="danger"
                size="sm"
                @click="disconnect(provider.slug)"
              >Disconnect
                <template #start>
                  <BIconX class="overflow-visible text-base" />
                </template>
              </VButton>
            </TransitionFade>
          </template>
        </ActionsPanelItem>
      </ActionsPanel>
    </VContainer>
  </div>
</template>

<script setup>
import { markRaw, computed } from 'vue';
import { axios } from '@/lib/axios';
import { useOAuthToasts } from '@/composables/useOAuthToasts';
import { useOAuthConnections } from '@/modules/users/api/get-oauth-connections';
import { useDeleteOAuthConnection } from '@/modules/users/api/delete-oauth-connection';
import { formattedDate } from '@/util/dateUtils';

import ActionsPanel from '@/components/ui/panels/ActionsPanel.vue';
import ActionsPanelItem from '@/components/ui/panels/actions/ActionsPanelItem.vue';

import BIconGoogle from '~icons/bi/google';
import BIconApple from '~icons/bi/apple';
import BIconFacebook from '~icons/bi/facebook';

import BIconPlus from '~icons/bi/plus';
import BIconX from '~icons/bi/x';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';

// Initialize OAuth toast handling for account linking.
useOAuthToasts({ successType: 'connect' });

// Fetch OAuth connections
const { data: oauthConnections, isLoading } = useOAuthConnections();

// Delete OAuth connection mutation
const { mutate: deleteConnection } = useDeleteOAuthConnection();

const baseProviders = [
  {
    slug: 'google',
    name: 'Google',
    description: `Sign in with your Google account.`,
    icon: markRaw(BIconGoogle),
    iconForeground: 'text-white',
    iconBackground: 'bg-google-plus'
  },
  {
    slug: 'apple',
    name: 'Apple',
    description: `Sign in with your Apple ID.`,
    icon: markRaw(BIconApple),
    iconForeground: 'text-white',
    iconBackground: 'bg-black'
  },
  {
    slug: 'facebook',
    name: 'Facebook',
    description: `Sign in with your Facebook account.`,
    icon: markRaw(BIconFacebook),
    iconForeground: 'text-white',
    iconBackground: 'bg-facebook'
  }
];

const providers = computed(() => {
  if (!oauthConnections?.value?.data) return baseProviders;

  return baseProviders.map(provider => {
    const connection = oauthConnections.value.data.find(conn => conn.provider === provider.slug);

    if (connection) {
      const connectedDate = formattedDate(connection.created_at, 'Do MMMM, YYYY');
      const userName = connection.name || connection.email || 'your account';

      let description = `Connected as ${userName} on ${connectedDate}.`;

      return {
        ...provider,
        connected: true,
        connection,
        description
      };
    }

    return {
      ...provider,
      connected: false
    };
  });
});

// Connect to a provider. Backend will redirect back with potential query params.
const connect = async (providerSlug) => {
  try {
    const response = await axios.get(`/api/auth/redirect/connect/${providerSlug}`);
    window.location.href = response.data.url;
  } catch (error) {
    Reshape.toast({
        title: 'Connection Failed',
        message: 'Failed to connect to the provider. Please try again.',
      }, 'danger'
    );
  }
};

// Disconnect from a provider.
const disconnect = (providerSlug) => {
  deleteConnection(
    { provider: providerSlug },
    {
      onSuccess: () => {
        Reshape.toast({
          title: 'Account Disconnected',
          message: `Your ${providerSlug} account has been disconnected successfully.`,
        }, 'success');
      },
      onError: () => {
        Reshape.toast({
          title: 'Disconnection Failed',
          message: 'Failed to disconnect the account. Please try again.',
        }, 'danger');
      }
    }
  );
};


</script>
