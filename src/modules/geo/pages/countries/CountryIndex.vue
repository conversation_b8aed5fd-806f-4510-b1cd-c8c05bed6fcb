<template>
  <div>
    <VPageHeader
      title="Countries"
      description="Overview of all countries defined within the application."
    >
      <template #actions>
        <VButton
          as="router-link"
          intent="secondary"
          :to="{ name: 'settings.countries.create' }"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Add Country
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="py-2 lg:py-6">
      <CountryTable
        title="All Countries"
        :records="countries?.data"
        :total-records="countries?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
      />
    </VContainer>
  </div>
</template>

<script setup lang="ts">
import { useFilterParams } from '@/hooks/query';
import CountryTable from '@/modules/geo/components/CountryTable.vue';
import BIconPlusLg from '~icons/bi/plus-lg';
import { useCountries } from '../../api/get-countries';

const { params, search, sort, paginate } = useFilterParams();

const { data: countries, isLoading } = useCountries({
  params: params.value,
});
</script>
