<template>
  <div>
    <VPageHeader title="View Country" description="Overview of selected country." />
    <VContainer class="my-3">
      <TransitionFade>
        <div v-if="isLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>
        <div v-else-if="country" class="grid gap-3 lg:grid-cols-5 items-start">
          <ViewPanel class="col-span-5 xl:col-span-4 order-2 xl:order-1">
            <PanelCountryField
                label="Country Name"
                :value="country?.name"
                :iso2="country?.iso_alpha_2"
                allowCopy
              />
            <PanelDefaultField label="Official Country Name" :value="country.official_name" allowCopy />
            <PanelDefaultField label="ISO 2 Code" :value="country.iso_alpha_2" allowCopy />
            <PanelDefaultField label="ISO 3 Code" :value="country.iso_alpha_3" allowCopy />
            <PanelDefaultField label="ISO Numeric Code" :value="country.iso_numeric" allowCopy />
            <PanelDefaultField label="Spoken Languages" :value="languages" allowCopy />
            <PanelDefaultField label="Region" :value="country.region?.name" />
            <PanelDefaultField label="Country Currencies" :value="currencies" allowCopy>
              <template #default v-if="country.currencies.length > 0">
                <ul :class="country.currencies.length > 1 ? 'list-disc list-inside' : 'list-none'">
                  <li v-for="currency in country.currencies" :key="currency.id">
                    {{ currency.name }} ({{ currency.symbol }})
                  </li>
                </ul>
              </template>
            </PanelDefaultField>
            <PanelDefaultField label="Geoname ID" :value="country.geoname_id" allowCopy />
            <PanelDefaultField label="TLD" :value="tld" allowCopy />
            <PanelDefaultField label="WMO" :value="country.wmo" allowCopy />
            <PanelDefaultField label="International Phone Code" :value="country.international_phone" allowCopy />
            <PanelDefaultField label="Emoji & Code" :value="country.emoji" allowCopy>
              <template #default>
                <span v-if="country.emoji">
                  {{ emojiValue }} <BIconArrowRight class="inline h-4 w-4 mx-1 text-primary-500" /> {{ country.emoji }}
                </span>
                <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
              </template>
            </PanelDefaultField>
            <PanelDefaultField label="Coordinates" :value="coordinatesString" allowCopy>
              <template #default>
                <span v-if="coordinates">
                  <p>Latitude: {{ coordinates?.latitude?.classic }} (Classic) & {{ coordinates?.latitude?.desc }} (Decimal)</p>
                  <p>Longitude: {{ coordinates?.longitude?.classic }} (Classic) & {{ coordinates?.longitude?.desc }} (Decimal)</p>
                </span>
                <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
              </template>
            </PanelDefaultField>
            <PanelDefaultField label="Coordinates Limit" :value="coordinatesLimitString" allowCopy>
              <template #default>
                <span v-if="coordinatesLimit">
                  <p>Latitude: {{ coordinatesLimit?.latitude?.min }} to {{ coordinatesLimit?.latitude?.max }}</p>
                  <p>Longitude: {{ coordinatesLimit?.longitude?.min }} to {{ coordinatesLimit?.longitude?.max }}</p>
                </span>
                <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
              </template>
            </PanelDefaultField>
            <PanelDefaultField label="Status" :value="country?.status?.name">
              <template #default>
                <CountryStatusBadge :status="country?.status?.name" />
              </template>
            </PanelDefaultField>
            <PanelDateTimeField label="Date Created" :value="country.created_at" />
            <PanelDateTimeField label="Date Updated" :value="country.updated_at" />
          </ViewPanel>

          <VCard class="col-span-5 xl:col-span-1 order-1 xl:order-2">
            <VCardHeader>Actions</VCardHeader>
            <VCardBody class="flex gap-3 justify-start flex-wrap">
              <!-- Button that returns the user to the referring page. -->
              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'settings.countries.index' }"
              >All Countries
                <template #start>
                  <BIconListUl />
                </template>
              </VButton>

              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'settings.countries.activity', params: { id: country.locked_id } }"
              >View Activity
                <template #start>
                  <BIconActivity />
                </template>
              </VButton>

              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <VButton intent="danger">
                    <template #start>
                      <BIconPencilSquare />
                    </template>
                    <template #end>
                      <BIconChevronDown />
                    </template>
                    Modify
                  </VButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{
                        name: 'settings.countries.update',
                        params: { id: country.locked_id },
                      }"
                      class="w-full"
                    >
                      <BIconPencilSquare />Edit
                    </RouterLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem @click="openCountryDeleteConfirmationDialog">
                    <BIconTrash />Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </VCardBody>
          </VCard>
        </div>
        <template v-else>
          <VEmptyState
            title="Country Not Found"
            description="The requested country was not found."
            :contact="true"
          />
        </template>
      </TransitionFade>
    </VContainer>

    <VConfirmModal
      v-model:open="isCountryDeleteConfirmationDialog"
      @confirmed="deleteCountryMutation.mutate({
        countryId: props.id
      })"
      title="Confirm Country Deletion"
      :description="`<p>You're about to delete the requested country: <b>${name}</b>.</p><p>Please confirm you wish to proceed.</p>`"
      :icon="BIconTrashFill"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useDisclosure } from '@/hooks';
import { useRouter } from 'vue-router';

import BIconListUl from '~icons/bi/list-ul';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconChevronDown from '~icons/bi/chevron-down';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconArrowRight from '~icons/bi/arrow-right';
import BIconActivity from '~icons/bi/activity';

import ViewPanel from '@/components/ui/panels/ViewPanel.vue';
import PanelDefaultField from '@/components/ui/panels/fields/ViewPanel/PanelDefaultField.vue';
import PanelDateTimeField from '@/components/ui/panels/fields/ViewPanel/PanelDateTimeField.vue';
import CountryStatusBadge from '@/modules/geo/components/CountryStatusBadge.vue';

import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';

import { useDeleteCountry } from '@/modules/geo/api/delete-country';
import { useCountry } from '@/modules/geo/api/get-country';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const coordinatesLimit = computed(() => {
  return country.value?.coordinates_limit ? JSON.parse(country.value?.coordinates_limit) : null;
});
const coordinatesLimitString = computed(() => {
  // Return a single string combining the latitude and longitude limits.
  if (!coordinatesLimit.value) return null;
  return `Latitude: ${coordinatesLimit?.value?.latitude?.min} to ${coordinatesLimit?.value?.latitude?.max}, `
    + `Longitude: ${coordinatesLimit?.value?.longitude?.min} to ${coordinatesLimit?.value?.longitude?.max}`;
});

const coordinates = computed(() => {
  return country.value?.coordinates ? JSON.parse(country.value?.coordinates) : null;
});
const coordinatesString = computed(() => {
  if (!coordinates.value) return null;
  return `Latitude: ${coordinates?.value?.latitude?.classic} (Classic) & ${coordinates?.value?.latitude?.desc} (Decimal), `
    + `Longitude: ${coordinates?.value?.longitude?.classic} (Classic) & ${coordinates?.value?.longitude?.desc} (Decimal)`;
});

const router = useRouter();

const flagLoaded = ref(false);

const {
  isOpen: isCountryDeleteConfirmationDialog,
  open: openCountryDeleteConfirmationDialog
} = useDisclosure(false);

const { data: country, isLoading } = useCountry({
  countryId: props.id,
});

// Computed property to return the country name if set.
const name = computed(() => {
  return country.value?.name ? country.value.name : 'Undefined';
});

// Computed property to return the country emoji if set.
const emojiValue = computed(() => {
  try {
    return JSON.parse(country.value?.emoji);
  } catch (error) {
    return country.value?.emoji ? country.value.emoji : 'Undefined';
  }
});

// Computed property to convert three character codes to actual language string.
const languages = computed(() => {
  try {
    const languagesArray = JSON.parse(country.value?.languages);
    const languages = languagesArray.map((language) => {
      return new Intl.DisplayNames(['en'], { type: 'language' }).of(language);
    });
    return languages.length > 0 ? languages.join(', ') : 'Undefined';
  } catch (error) {
    return country.value?.languages ? country.value.languages : 'Undefined';
  }
});

// Computed property converting the currencies array to a readable string.
const currencies = computed(() => {
  return country.value?.currencies
    ? country.value.currencies.map((currency) => currency.name).join(', ')
    : null;
});

// Computed property converting the tld array to a readable string.
const tld = computed(() => {
  if (!country.value?.tld) return null;
  const arr = JSON.parse(country.value?.tld);
  return arr.length > 0 ? arr.map((tld) => tld).join(', ') : null;
});

const deleteCountryMutation = useDeleteCountry({
  mutationConfig: {
    onSuccess: () => {
      Reshape.toast(
        {
          title: 'Country Deleted',
          message: `The requested country <b>${name.value}</b> was successfully deleted.`,
        },
        'success'
      );
      router.push({
        name: 'settings.countries.index',
      });
    },
  },
});

</script>
