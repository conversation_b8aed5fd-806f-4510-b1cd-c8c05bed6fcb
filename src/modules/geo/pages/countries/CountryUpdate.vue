<template>
  <div>
    <VPageHeader title="Update Country" description="Update an existing country in the system.">
      <template #actions>
        <VButton type="submit"
          form="country-form"
          intent="secondary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Confirm
        </VButton>
        <VButton
          intent="danger"
          @click="confirmModalOpen = true"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <TransitionFade>
        <CountryForm v-if="!isCountryLoading" :country="country" @submit="submit" />
        <div v-else class="flex w-full justify-center p-6">
          <VSpinner size="xl" />
        </div>
      </TransitionFade>

      <div class="flex w-full justify-start lg:justify-end space-x-3">
        <VButton
          type="submit"
          form="country-form"
          intent="primary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Confirm
        </VButton>

        <VButton intent="danger" @click="confirmModalOpen = true">
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </div>
    </VContainer>
    
    <VConfirmModal
      @confirmed="router.push({ name: 'settings.countries.index' })"
      v-model:open="confirmModalOpen"
    />
  </div>
</template>

<script setup>
import CountryForm from '@/modules/geo/components/CountryForm.vue';
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconCheckLg from '~icons/bi/check-lg';
import { ref } from 'vue';
import { useUpdateCountry } from '@/modules/geo/api/put-country';
import { useCountry } from '@/modules/geo/api/get-country';

const confirmModalOpen = ref(false);

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const updateCountryMutation = useUpdateCountry({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Country Updated',
          message: `The country <b>${result.data?.name}</b> was successfully updated.`,
        },
        'success'
      );
      router.push({ name: 'settings.countries.view', params: { id: result.data.locked_id } });
    },
  },
});

const { data: country, isLoading: isCountryLoading } = useCountry({
  countryId: props.id
});

const submit = (form) => {
  form.submit((data) => updateCountryMutation.mutateAsync({
    countryId: props.id,
    data,
  }));
};
</script>
