<template>
  <div>
    <VPageHeader
      title="Add Country"
      description="Define a new country for the application."
    >
      <template #actions>
        <VButton type="submit"
          form="country-form"
          intent="secondary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>
        <VButton
          intent="danger"
          @click="confirmModalOpen = true"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <CountryForm @submit="submit" />

      <div class="flex w-full justify-start lg:justify-end space-x-3">
        <VButton
          type="submit"
          form="country-form"
          intent="primary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>

        <VButton intent="danger" @click="confirmModalOpen = true">
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </div>
    </VContainer>
    
    <VConfirmModal
      @confirmed="router.push({ name: 'settings.countries.index' })"
      v-model:open="confirmModalOpen"
    />
  </div>
</template>

<script setup>
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconPlusLg from '~icons/bi/plus-lg';
import CountryForm from '@/modules/geo/components/CountryForm.vue';
import { ref } from 'vue';
import { useCreateCountry } from '@/modules/geo/api/post-country';
const confirmModalOpen = ref(false);

const createCountryMutation = useCreateCountry({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Country Successfully Created',
          message: `The country <b>${result.data?.title}</b> was successfully created.`,
        },
        'success'
      );
      router.push({
        name: 'settings.countries.view',
        params: { id: result.data.locked_id }
      });
    },
  },
});

const submit = (form) => {
  form.submit((data) => createCountryMutation.mutateAsync({
    data,
  }));
};
</script>
