import UserLayout from '@/layouts/UserLayout.vue';
import { auth } from '@/router/middleware';

const routes = [
  {
    path: '/settings/countries',
    component: UserLayout,
    meta: {
      middleware: auth,
    },
    children: [
      {
        path: '',
        name: 'settings.countries.index',
        component: () => import('./pages/countries/CountryIndex.vue'),
        meta: {
          title: 'Countries',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Countries', name: 'settings.countries.index' },
          ],
        },
      },
      {
        path: ':id/view',
        name: 'settings.countries.view',
        component: () => import('./pages/countries/CountryView.vue'),
        props: true,
        meta: {
          title: 'View Country',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Countries', name: 'settings.countries.index' },
            { label: 'View', name: 'settings.countries.view' },
          ],
        },
      },
      {
        path: 'add',
        name: 'settings.countries.create',
        component: () => import('./pages/countries/CountryCreate.vue'),
        meta: {
          title: 'Add Country',
          breadcrumbs: [
            { label: 'Countries', name: 'settings.countries.index' },
            { label: 'Add', name: 'settings.countries.create' },
          ],
        },
      },
      {
        path: ':id/update',
        name: 'settings.countries.update',
        component: () => import('./pages/countries/CountryUpdate.vue'),
        props: true,
        meta: {
          title: 'Edit Country',
          breadcrumbs: [
            { label: 'Countries', name: 'settings.countries.index' },
            { label: 'Edit', name: 'settings.countries.update' },
          ],
        },
      },
      {
        path: ':id/activity',
        name: 'settings.countries.activity',
        component: () => import('@/modules/activity/pages/ActivityIndex.vue'),
        props: (route) => ({
          activitableId: route.params.id,
          activitableType: 'Country',
        }),
        meta: {
          title: 'Country Activity',
          headerTitle: 'Country Activity',
          headerDescription: 'Overview of all activity related to the selected country.',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Countries', name: 'settings.countries.index' },
            { label: 'Activity', name: 'settings.countries.activity' },
          ],
        },
      },
    ],
  },
];

export default routes;
