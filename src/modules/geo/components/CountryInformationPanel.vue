<template>
  <VInformationPanel :loading="isCountryLoading">
    <VDefaultField label="Name" :value="country.name" />
    <VDefaultField label="Official Name" :value="country.official_name" />
    <VDefaultField label="ISO 2" :value="country.iso_alpha_2" />
    <VDefaultField label="ISO 3" :value="country.iso_alpha_3" />
    <VDefaultField label="Currencies" :value="country.currencies.map((currency) => currency.iso).join(', ')" />
    <VTimeField label="Created At" :value="country.created_at" />
    <VTimeField label="Updated At" :value="country.updated_at" />
  </VInformationPanel>
</template>

<script setup>
import { useCountry } from '@/modules/geo/api/get-country';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const { data: country, isLoading: isCountryLoading } = useCountry({
  countryId: props.id,
});

</script>
