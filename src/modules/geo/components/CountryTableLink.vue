
<template>
  <EntityLink
    v-if="row.name"
    entity-type="Country"
    :entity-id="row.locked_id"
    :show-icon="false"
    :value="row.name"
  >
    <div class="flex items-center gap-2">
      <img
        v-if="row.iso_alpha_2"
        :src="`https://flagicons.lipis.dev/flags/4x3/${row.iso_alpha_2.toLowerCase()}.svg`"
        class="inline h-5 w-5"
        @load="flagLoaded = true"
        @error="flagLoaded = false"
        :style="{ display: flagLoaded ? 'inline' : 'none' }"
      />
      {{ row.name }}
    </div>
  </EntityLink>
  <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
</template>

<script setup>
import EntityLink from '@/components/ui/links/EntityLink.vue';
import { ref } from 'vue';

const props = defineProps({
  row: {
    type: Object,
    required: true,
  },
});

const flagLoaded = ref(false);

</script>