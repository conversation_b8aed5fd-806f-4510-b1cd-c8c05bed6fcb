<template>
  <form id="country-form" class="space-y-4" @submit.prevent="submit">
    <FormContainer
      title="Basic Details"
      description="Provide low level details about the country."
      :icon="BIconGlobeAmericas"
    >
      <template #body>
        <div class="grid grid-cols-6 gap-6">
          <VField
            class="col-span-6 md:col-span-3"
            label="Name"
            required
            :errors="form.errors.get('name')"
          >
            <VInput
              v-model="form.name"
              placeholder="Enter Country Name"
            />
          </VField>

          <VField
            class="col-span-6 md:col-span-3"
            label="Official Name"
            required
            :errors="form.errors.get('official_name')"
          >
            <VInput
              v-model="form.official_name"
              placeholder="Enter Country Official Name"
            />
          </VField>

          <VField
            class="col-span-6 sm:col-span-3 xl:col-span-2"
            label="ISO 2"
            required
            :errors="form.errors.get('iso_alpha_2')"
          >
            <VInput
              v-model="form.iso_alpha_2"
              placeholder="Enter ISO 2"
            />
          </VField>

          <VField
            class="col-span-6 sm:col-span-3 xl:col-span-2"
            label="ISO 3"
            required
            :errors="form.errors.get('iso_alpha_3')"
          >
            <VInput
              v-model="form.iso_alpha_3"
              placeholder="Enter ISO 3"
            />
          </VField>
          
          <VField
            class="col-span-6 sm:col-span-3 xl:col-span-2"
            label="ISO Numeric"
            required
            :errors="form.errors.get('iso_numeric')"
          >
            <VInput
              v-model="form.iso_numeric"
              placeholder="Enter ISO Numeric"
            />
          </VField>

          <VField
            class="col-span-6 sm:col-span-3 xl:col-span-2"
            label="Region"
            required
            :errors="form.errors.get('region_id')"
          >
            <CountryRegionCombobox v-model="form.region_id" />
          </VField>

          <VField
            class="col-span-2 lg:col-span-1"
            label="Is Enabled"
            help="Typically used for temporarily disabling a country from being used in the application."
            required
          >
            <VToggle v-model="form.is_enabled" class="mt-3">
              <span class="text-platinum-800 dark:text-midnight-100">
                {{ form.is_enabled ? 'Enabled' : 'Disabled' }}
              </span>
            </VToggle>
          </VField>

          <VField
            class="col-span-4 lg:col-span-2 xl:col-span-3"
            label="Status"
            help="Selecting anything other than 'Active' will prevent the country from being used in the application."
            required
            :errors="form.errors.get('status_id')"
          >
            <StatusCombobox
              v-model="form.status_id"
              statusable="Country"
              placeholder="Select a Status"
              :default-option="props.country ? null : 'Active'"
              required
            />
          </VField>

          <VField
            class="col-span-6 lg:col-span-3"
            label="Currencies"
            :errors="form.errors.get('Currencies')"
          >
            <CurrenciesCombobox v-model="form.currencies" />
          </VField>
        </div>
      </template>
    </FormContainer>

    <FormContainer
      title="Advanced Details"
      description="Provide additional advanced details about the country."
      :icon="BIconBraces"
    >
      <template #body>
        <div class="grid grid-cols-6 gap-6">
          <VField
            class="col-span-6 sm:col-span-3 xl:col-span-2"
            label="Geoname ID"
            :errors="form.errors.get('geoname_id')"
          >
            <VInput
              v-model="form.geoname_id"
              placeholder="Enter Geoname ID"
            />
          </VField>
          
          <VField
            class="col-span-6 sm:col-span-3 xl:col-span-2"
            label="International Phone Code"
            :errors="form.errors.get('international_phone')"
          >
            <VInput
              v-model="form.international_phone"
              placeholder="Enter International Phone Code"
            />
          </VField>

          <VField
            class="col-span-6 sm:col-span-3 xl:col-span-2"
            label="Languages"
            :errors="form.errors.get('languages')"
          >
            <!-- Potentially want to add a language combobox here -->
            <VInput
              v-model="form.languages"
              placeholder="Enter Languages"
            />
          </VField>

          <VField
            class="col-span-6 sm:col-span-3 xl:col-span-2"
            label="TLD"
            :errors="form.errors.get('tld')"
          >
            <VInput
              v-model="form.tld"
              placeholder="Enter TLD"
            />
          </VField>

          <VField
            class="col-span-6 sm:col-span-3 xl:col-span-2"
            label="WMO"
            :errors="form.errors.get('wmo')"
          >
            <VInput
              v-model="form.wmo"
              placeholder="Enter WMO"
            />
          </VField>
          
          <VField
            class="col-span-6 sm:col-span-3 xl:col-span-2"
            label="Emoji Code"
            :errors="form.errors.get('emoji')"
          >
            <VInput
              v-model="form.emoji"
              placeholder="Enter Emoji Code"
            />
          </VField>
          
          <VField
            class="col-span-6 sm:col-span-3 xl:col-span-2"
            label="Coordinates"
            :errors="form.errors.get('coordinates')"
          >
            <VInput
              v-model="form.coordinates"
              placeholder="Enter Coordinates"
            />
          </VField>
          
          <VField
            class="col-span-6 sm:col-span-3 xl:col-span-2"
            label="Coordinates Limit"
            :errors="form.errors.get('coordinates_limit')"
          >
            <VInput
              v-model="form.coordinates_limit"
              placeholder="Enter Coordinates Limit"
            />
          </VField>
        </div>
      </template>
    </FormContainer>
  </form>
</template>

<script setup>
import FormContainer from '@/components/ui/forms/FormContainer.vue';

import { useForm } from '@/hooks/form';
import BIconGlobeAmericas from '~icons/bi/globe-americas';
import BIconBraces from '~icons/bi/braces';
import CountryRegionCombobox from '@/components/ui/form/composables/CountryRegionCombobox.vue';
import StatusCombobox from '@/components/ui/form/composables/StatusCombobox.vue';
import CurrenciesCombobox from '@/components/ui/form/composables/CurrenciesCombobox.vue';

const props = defineProps({
  country: {
    type: Object,
    default: null,
  },
});

const emits = defineEmits(['submit']);

const submit = () => emits('submit', form.value);

const form = useForm({
  name: props.country?.name ?? '',
  official_name: props.country?.official_name ?? '',
  region_id: props.country?.region?.locked_id ?? '',
  iso_alpha_2: props.country?.iso_alpha_2 ?? '',
  iso_alpha_3: props.country?.iso_alpha_3 ?? '',
  iso_numeric: props.country?.iso_numeric ?? '',
  geoname_id: props.country?.geoname_id ?? '',
  international_phone: props.country?.international_phone ?? '',
  languages: props.country?.languages ?? '',
  tld: props.country?.tld ?? '',
  wmo: props.country?.wmo ?? '',
  emoji: props.country?.emoji ?? '',
  coordinates: props.country?.coordinates ?? '',
  coordinates_limit: props.country?.coordinates_limit ?? '',
  currencies: props.country?.currencies?.map((currency) => currency.locked_id),
  status_id: props.country?.status?.locked_id ?? '',
  is_enabled: props.country?.is_enabled ?? false,
});
</script>