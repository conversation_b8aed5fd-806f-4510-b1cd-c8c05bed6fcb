<template>
  <Table
    v-bind="$attrs"
    :columns="columns"
    :default-sort="[{
      id: 'name',
      desc: true,
    }]"
    paginated
    sortable
    searchable
    controlled
  />
</template>

<script setup>
import { h, ref } from 'vue';

import Table from '@/components/ui/tables/Table.vue';

import ActionCell from './CountryTableActionCell.vue';
import CountryStatusBadge from './CountryStatusBadge.vue';
import TableCell from '@/components/ui/tables/TableCell.vue';
import { humanizeTime } from '@/util/dateUtils';
import CountryTableLink from '@/modules/geo/components/CountryTableLink.vue';

const columns = ref([
  {
    accessorKey: 'name',
    header: 'Name',
    cell: (info) => h(CountryTableLink, { row: info.row.original }),
  },
  {
    accessorFn: (row) => row?.region?.name,
    id: 'region.name',
    header: 'Region',
    accessorKey: 'region.name',
    cell: (info) => h(TableCell, { value: info.getValue() }),
  },
  {
    accessorKey: 'created_at',
    header: 'Created Date',
    cell: (info) => humanizeTime(info.row.original.created_at),
  },
  {
    id: 'status.name',
    accessorKey: 'status',
    header: 'Status',
    cell: (info) => h(CountryStatusBadge, {
      status: info.row.original.status?.name,
      key: info.row.original.locked_id,
    }),
  },
  {
    id: 'actions',
    header: () => h('span', { class: 'w-full flex justify-end' }),
    cell: (info) => h(ActionCell, {
      row: info.row.original,
      key: info.row.original.locked_id,
    }),
    enableSorting: false,
    enableResizing: false,
    size: 50,
  },
]);
</script>
