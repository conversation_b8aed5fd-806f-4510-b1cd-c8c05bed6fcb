import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getCountryQueryOptions } from './get-country';

import { Country } from '@/types/api';

export const updateCountry = ({ countryId, data }: { countryId: string, data: UpdateCountryPayload }) => {
  return Reshape.http().put(`/api/countries/${countryId}`, data);
};

type UpdateCountryPayload = Partial<Omit<Country, 'locked_id'>>;

type UseUpdateCountryOptions = {
  countryId: string;
  mutationConfig?: MutationConfig<typeof updateCountry>;
};

export const useUpdateCountry = ({ countryId, mutationConfig }: UseUpdateCountryOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['countries', args[1].countryId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getCountryQueryOptions(args[1].countryId).queryKey,
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: updateCountry,
  });
};