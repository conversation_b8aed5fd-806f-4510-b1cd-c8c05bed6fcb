import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { Country } from '@/types/api';

export const createCountry = ({ data }: { data: CreateCountryPayload }) => {
  return Reshape.http().post(`/api/countries`, data);
};

type CreateCountryPayload = Omit<Country, 'id' | 'locked_id'>;

type UseCreateCountryOptions = {
  mutationConfig?: MutationConfig<typeof createCountry>;
};

export const useCreateCountry = ({ mutationConfig }: UseCreateCountryOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ['countries'],
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createCountry,
  });
};