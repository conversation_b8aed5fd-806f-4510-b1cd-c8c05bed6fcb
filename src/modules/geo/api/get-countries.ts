import { queryOptions, useQuery } from '@tanstack/vue-query';
import { Country, Meta } from '@/types/api';

import { QueryConfig } from '@/lib/vue-query';

export const getCountries = (
  params,
  signal
): Promise<{
  data: Country[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/countries`, {
    params,
    signal,
  });
};

export const getCountriesQueryOptions = ({ params }: { params?: unknown } = {}) => {
  return queryOptions({
    queryKey: params ? ['countries', params] : ['countries'],
    queryFn: ({ signal }) => getCountries(params, signal),
  });
};

type UseCountriesOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getCountriesQueryOptions>;
};

export const useCountries = ({ queryConfig, params }: UseCountriesOptions = {}) => {
  return useQuery({
    ...getCountriesQueryOptions({ params }),
    ...queryConfig,
  });
}; 