import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getCountriesQueryOptions } from './get-countries';

// Declare the Reshape global variable
declare const Reshape: {
  http: () => {
    delete: (url: string) => Promise<any>;
  };
};

export const deleteCountry = ({ countryId }: { countryId: string }) => {
  return Reshape.http().delete(`/api/countries/${countryId}`);
};

type UseDeleteCountryOptions = {
  mutationConfig?: {
    onSuccess?: (...args: any[]) => void;
    [key: string]: any;
  };
};

export const useDeleteCountry = ({ mutationConfig }: UseDeleteCountryOptions = {}) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['countries', args[1].countryId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getCountriesQueryOptions().queryKey,
      });
      if (onSuccess) {
        onSuccess(...args);
      }
    },
    ...restConfig,
    mutationFn: deleteCountry,
  });
}; 