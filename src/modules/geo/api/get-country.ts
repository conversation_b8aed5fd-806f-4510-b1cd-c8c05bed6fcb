import { useQuery, queryOptions } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Country } from '@/types/api';

export const getCountry = ({
  countryId,
}: {
  countryId: string;
}): Promise<{ data: Country }> => {
  return Reshape.http()
    .get(`/api/countries/${countryId}`)
    .then(({ data }) => data);
};

export const getCountryQueryOptions = (countryId: string) => {
  return queryOptions({
    queryKey: ['countries', countryId],
    queryFn: () => getCountry({ countryId }),
  });
};

type UseCountryOptions = {
  countryId: string;
  queryConfig?: QueryConfig<typeof getCountryQueryOptions>;
};

export const useCountry = ({ countryId, queryConfig }: UseCountryOptions) => {
  return useQuery({
    ...getCountryQueryOptions(countryId),
    ...queryConfig,
  });
}; 