import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getNotificationsQueryOptions } from './get-notifications';

// Declare the Reshape global variable
declare const Reshape: {
  http: () => {
    delete: (url: string) => Promise<any>;
  };
};

export const deleteNotification = ({ notificationId }: { notificationId: string }) => {
  return Reshape.http().delete(`/api/notifications/${notificationId}`);
};

type UseDeleteNotificationOptions = {
  mutationConfig?: MutationConfig<typeof deleteNotification>;
};

export const useDeleteNotification = ({ mutationConfig }: UseDeleteNotificationOptions = {}) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['notifications', args[1].notificationId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getNotificationsQueryOptions().queryKey,
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteNotification,
  });
}; 