import { queryOptions, useQuery } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Notification, Meta } from '@/types/api';

export const getNotifications = (
  params,
  signal
): Promise<{
  data: Notification[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/notifications`, {
    params,
    signal,
  });
};

export const getNotificationsQueryOptions = ({ params }: { params?: unknown } = {}) => {
  return queryOptions({
    queryKey: ['notifications'],
    queryFn: ({ signal }) => getNotifications(params, signal),
  });
};

type UseNotificationsOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getNotificationsQueryOptions>;
};

export const useNotifications = ({ queryConfig, params }: UseNotificationsOptions = {}) => {
  return useQuery({
    ...getNotificationsQueryOptions({ params }),
    ...queryConfig,
  });
};