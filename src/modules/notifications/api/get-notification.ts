import { useQuery, queryOptions } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Notification } from '@/types/api';

export const getNotification = ({
  notificationId,
}: {
  notificationId: string;
}): Promise<{ data: Notification }> => {
  return Reshape.http()
    .get(`/api/notifications/${notificationId}`)
    .then(({ data }) => data);
};

export const getNotificationQueryOptions = (notificationId: string) => {
  return queryOptions({
    queryKey: ['notifications', notificationId],
    queryFn: () => getNotification({ notificationId }),
  });
};

type UseNotificationOptions = {
  notificationId: string;
  queryConfig?: QueryConfig<typeof getNotificationQueryOptions>;
};

export const useNotification = ({ notificationId, queryConfig }: UseNotificationOptions) => {
  return useQuery({
    ...getNotificationQueryOptions(notificationId),
    ...queryConfig,
  });
}; 