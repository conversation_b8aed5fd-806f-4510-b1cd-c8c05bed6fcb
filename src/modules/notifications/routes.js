import UserLayout from '@/layouts/UserLayout.vue';
import { auth } from '@/router/middleware';

const routes = [
  {
    path: '/notifications',
    component: UserLayout,
    meta: {
      middleware: auth,
    },
    children: [
      {
        path: '',
        name: 'notifications.index',
        component: () => import('./pages/NotificationIndex.vue'),
        meta: {
          title: 'Notifications',
          headerTitle: 'Notifications',
          headerDescription: 'Notifications that have been sent to users within the application',
          breadcrumbs: [
            { label: 'Notifications', name: 'notifications.index' },
          ],
        },
      },
    ],
  },
  {
    path: '/settings/notifications',
    component: UserLayout,
    meta: {
      middleware: auth,
    },
    children: [
      {
        path: '',
        name: 'settings.notifications.index',
        component: () => import('./pages/NotificationIndex.vue'),
        meta: {
          title: 'Notifications',
          headerTitle: 'Notifications',
          headerDescription: 'Notifications that have been sent to users within the application',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Notifications', name: 'settings.notifications.index' },
          ],
        },
      },
      {
        path: ':id/view',
        name: 'settings.notifications.view',
        component: () => import('./pages/NotificationView.vue'),
        props: true,
        meta: {
          title: 'View Notification',
          breadcrumbs: [
            { label: 'Settings', name: 'settings.index' },
            { label: 'Notifications', name: 'settings.notifications.index' },
            { label: 'View', name: 'settings.notifications.view' },
          ],
        },
      },
    ],
  },
];

export default routes;
