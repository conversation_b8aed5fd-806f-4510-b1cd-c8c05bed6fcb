<template>
  <div>
    <VPageHeader title="View Notification" description="Overview of selected notification." />
    <VContainer class="my-3">
      <TransitionFade>
        <div v-if="isLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>
        <div v-else-if="notification" class="grid gap-3 lg:grid-cols-5 items-start">
          <ViewPanel class="col-span-6">
            <p class="text-red-500">TODO</p>
          </ViewPanel>
        </div>
        <template v-else>
          <VEmptyState
            title="Notification Not Found"
            description="The requested notification was not found."
            :contact="true"
          />
        </template>
      </TransitionFade>
    </VContainer>
  </div>
</template>

<script setup lang="ts">
import ViewPanel from '@/components/ui/panels/ViewPanel.vue';
import { useRouter } from 'vue-router';
import { useNotification } from '@/modules/notifications/api/get-notification';
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const router = useRouter();

const { data: notification, isLoading } = useNotification({
  notificationId: props.id,
});
</script>
