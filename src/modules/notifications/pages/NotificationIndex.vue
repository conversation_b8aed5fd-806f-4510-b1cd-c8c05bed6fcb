<template>
  <div>
    <VPageHeader/>

    <VContainer class="py-2 lg:py-6">
      <!-- {{ notifications }} -->
      <NotificationTable
        title="All Notifications"
        :records="notifications?.data"
        :total-records="notifications?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
      />
    </VContainer>
  </div>
</template>

<script setup lang="ts">
import { useFilterParams } from '@/hooks/query';
import NotificationTable from '@/modules/notifications/components/NotificationTable.vue';
import BIconPlusLg from '~icons/bi/plus-lg';
import { useNotifications } from '../api/get-notifications';

const { params, search, sort, paginate } = useFilterParams();

const { data: notifications, isLoading } = useNotifications({
  params: params.value,
});
</script>
