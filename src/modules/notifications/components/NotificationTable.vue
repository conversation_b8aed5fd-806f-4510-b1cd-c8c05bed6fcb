<template>
  <Table
    v-bind="$attrs"
    :columns="columns"
    :default-sort="[{
      id: 'name',
      desc: true,
    }]"
    paginated
    sortable
    searchable
    controlled
  />
</template>

<script setup>
import { h, ref } from 'vue';

import Table from '@/components/ui/tables/Table.vue';

import ActionCell from './NotificationTableActionCell.vue';
import NotificationStatusBadge from './NotificationStatusBadge.vue';
import TableCell from '@/components/ui/tables/TableCell.vue';
import { humanizeTime } from '@/util/dateUtils';

const flagLoaded = ref(false);

const columns = ref([
  {
    accessorFn: (row) => row?.type,
    id: 'type',
    header: 'Type',
    cell: (info) => h(TableCell, { value: info.getValue() }),
  },
  {
    accessorFn: (row) => row?.data?.user?.full_name,
    id: 'user',
    header: 'User',
    cell: (info) => h(TableCell, { value: info.getValue() }),
  },
  {
    accessorFn: (row) => row?.data?.company?.name,
    id: 'company',
    header: 'Company',
    cell: (info) => h(TableCell, { value: info.getValue() }),
  },
  {
    accessorKey: 'created_at',
    header: 'Created Date',
    cell: (info) => humanizeTime(info.row.original.created_at),
  },
  {
    accessorKey: 'read_at',
    header: 'Status',
    cell: (info) => h(NotificationStatusBadge, {
      read_at: info.row.original.read_at,
      key: info.row.id,
    }),
  },
  {
    id: 'actions',
    header: () => h('span', { class: 'w-full flex justify-end' }),
    cell: (info) => h(ActionCell, {
      row: info.row.original,
      key: info.row.original.locked_id,
    }),
    enableSorting: false,
    enableResizing: false,
    size: 50,
  },
]);
</script>
