<template>
  <div class="flex items-center justify-end space-x-1.5">
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <VButton intent="table">
          <BIconThreeDots class="h-5 w-5" />
        </VButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem as-child>
          <RouterLink
            :to="{
              name: 'settings.notifications.view',
              params: { id: row.id },
            }"
          >
            <BIconSearch />View
          </RouterLink>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<script setup lang="ts">
import BIconSearch from '~icons/bi/search';
import BIconThreeDots from '~icons/bi/three-dots';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from '@/components/ui/shadcn/dropdown-menu';
import { useDisclosure } from '@/hooks';

interface NotificationRow {
  locked_id: string;
  name: string;
  [key: string]: any;
}

const props = defineProps<{
  row: NotificationRow;
}>();
</script>