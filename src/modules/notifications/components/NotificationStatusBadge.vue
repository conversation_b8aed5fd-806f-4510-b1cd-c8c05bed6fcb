<template>
  <VBadge
    :color="statusColor"
    class="uppercase"
    :title="read_at ? 'Viewed on ' + humanizeTime(read_at) : 'Not yet viewed'"
  >
    {{ read_at ? 'Viewed' : 'Unread' }}
  </VBadge>
</template>

<script setup>
import { computed } from 'vue';
import { humanizeTime } from '@/util/dateUtils';

const props = defineProps({
  read_at: {
    type: String,
  },
});

const statusColor = computed(() => {
  if (props.read_at) {
    return 'success';
  } else {
    return 'warning';
  }
});
</script>