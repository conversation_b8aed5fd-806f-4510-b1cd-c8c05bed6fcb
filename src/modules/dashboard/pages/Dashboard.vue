<template>
  <VContainer class="py-2 md:py-3">
    <div class="grid grid-cols-1 items-start gap-2 md:gap-3 lg:grid-cols-3">
      <div class="grid grid-cols-1 gap-2 md:gap-3 lg:col-span-2">
        <WelcomePanel :user="user" />
        <ActionsPanel>
          <ActionsPanelLink
            v-for="(action, index) in filteredActions"
            :key="action.name"
            :action="action"
            :total-length="filteredActions.length"
            :index="index"
          />
        </ActionsPanel>
      </div>
      <DashboardAnnouncements />
    </div>
  </VContainer>
</template>

<script setup>
import { computed } from 'vue';
import { useAuth } from '@/store';
import { useOAuthToasts } from '@/composables/useOAuthToasts';
import BIconPersonWorkspace from '~icons/bi/person-workspace';
import BIconMegaphoneFill from '~icons/bi/megaphone-fill';
import BIconPersonCircle from '~icons/bi/person-circle';
import BIconJournalBookmarkFill from '~icons/bi/journal-bookmark-fill';
import BIconPaletteFill from '~icons/bi/palette-fill';
import ActionsPanel from '@/components/ui/panels/ActionsPanel.vue';
import ActionsPanelLink from '@/components/ui/panels/actions/ActionsPanelLink.vue';
import WelcomePanel from '@/modules/dashboard/components/WelcomePanel.vue';
import DashboardAnnouncements from '@/modules/dashboard/components/DashboardAnnouncements.vue';

const actions = [
  {
    icon: BIconPersonCircle,
    name: 'View My Profile',
    to: { name: 'profile.view' },
    description: 'Everything about you, in one place! Click to view your profile, make changes, and personalize your experience.',
    iconForeground: 'text-primary-500',
    iconBackground: 'bg-emerald-100 dark:bg-emerald-900',
    ability: 'users.profile.self'
  },
  {
    icon: BIconPersonWorkspace,
    name: 'Create a Customer',
    to: { name: 'customers.create' },
    description: 'Add a customer to your list of clients. This will allow you to manage their information and interactions.',
    iconForeground: 'text-primary-500',
    iconBackground: 'bg-yellow-50 dark:bg-yellow-500',
    ability: 'customers.create.self'
  },
  {
    icon: BIconMegaphoneFill,
    name: 'Schedule an Announcement',
    to: { name: 'settings.announcements.create' },
    description: 'Got something important to say? Inform your users about upcoming events or changes.',
    iconForeground: 'text-primary-500',
    iconBackground: 'bg-indigo-100 dark:bg-indigo-800',
    ability: 'announcements.create'
  },
  {
    icon: BIconJournalBookmarkFill,
    name: 'View My Contacts',
    to: { name: 'contacts.index' },
    description: 'View all your contacts in one place. Click to manage your contacts and social connections.',
    iconForeground: 'text-primary-500',
    iconBackground: 'bg-amber-200 dark:bg-amber-900',
    ability: 'contacts.index.self'
  },
  {
    icon: BIconPaletteFill,
    name: 'Create a Custom Theme',
    to: { name: 'themes.create' },
    description: 'Not a fan of our colours? No problem! Enhance your experience with a custom theme just for you.',
    iconForeground: 'text-primary-500',
    iconBackground: 'bg-sky-200 dark:bg-sky-950',
    ability: 'themes.create.self'
  }
];

// Initialize OAuth toast handling for dashboard (login success).
useOAuthToasts({ successType: 'login' });

const auth = useAuth();

// Initialised as it might not be available on logout.
const user = auth.user;

const filteredActions = computed(() => actions.filter(action => !action.ability || auth.can(action.ability)));

</script>
