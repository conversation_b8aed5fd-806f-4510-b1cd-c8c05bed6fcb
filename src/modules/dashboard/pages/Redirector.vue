<template>
  <TransitionFadeLeftToRight>
    <Dashboard v-if="auth.user && auth.user.profile" />
    <div v-else-if="auth.user && !auth.user.profile" class="h-full">
      <!-- Redirect to profile setup -->
      <div class="flex items-center justify-center h-full">
        <div class="text-center space-y-3">
          <h2 class="text-xl font-semibold text-primary-500">Welcome - Let's get started</h2>
          <VSpinner size="lg" />
        </div>
      </div>
    </div>
    <Homepage v-else />
  </TransitionFadeLeftToRight>
</template>

<script setup>
import Dashboard from '@/modules/dashboard/pages/Dashboard.vue';
import Homepage from '@/modules/forefront/pages/Home.vue';
import { useAuth } from '@/store';
import { useRouter } from 'vue-router';
import { watch } from 'vue';

const auth = useAuth();
const router = useRouter();

// Watch for user changes and redirect to profile setup if needed
watch(() => auth.user, (user) => {
  if (user && !user.profile) {
    router.push({ name: 'me.setup' });
  }
}, { immediate: true });

</script>
