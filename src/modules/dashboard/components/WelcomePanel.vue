<template>
  <section aria-labelledby="profile-overview-title">
    <div class="overflow-hidden rounded-lg bg-primary-500 shadow dark:bg-midnight-500">
      <h2 class="sr-only" id="profile-overview-title">Profile Overview</h2>
      <div class="p-6">
        <div class="sm:flex sm:items-center sm:justify-between w-full">
          <div class="sm:flex sm:space-x-5 w-full">
            <div class="flex-shrink-0 flex justify-center items-start ">
              <VAvatar
                class="flex-shrink-0"
                size="xl"
                :dot="user?.availability?.color"
                :name="user?.profile?.full_name"
                :src="user?.profile?.media?.profile_photo?.conversions?.medium"
              />
            </div>
            <div class="mt-4 text-center sm:mt-0 sm:pt-1 sm:text-left flex-1 min-w-0">
              <p class="text-md font-medium text-platinum-50/75 dark:text-midnight-50 truncate">{{ randomGreeting() }},</p>
              <p class="text-xl font-bold text-platinum-50 dark:text-primary-500 sm:text-3xl truncate">
                {{ user?.profile?.full_name }}
              </p>
              <p class="text-xs font-light text-platinum-50/75 dark:text-midnight-50 sm:text-sm truncate">
                {{ user?.profile?.job_title }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="divide-y divide-gray-200 border-gray-200 bg-platinum-50 dark:bg-primary-500 sm:grid-cols-3 sm:divide-x sm:divide-y-0">
        <div class="px-6 py-5 text-sm font-medium">
          <span class="text-platinum-950 dark:text-midnight-950">
            Last logged in {{ user.last_login_at ? humanizeTime(user.last_login_at) : 'Never' }}
          </span>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { humanizeTime } from '@/util/dateUtils';

const props = defineProps({
  user: Object,
});

function randomGreeting() {
  // Return a random greeting
  var greetings = [
    'Welcome back',
    'Nice to see you again',
    "Hope you're having a great day",
    'Howdy',
    'Great to see you again',
    'Glad you could make it',
    'Hi there',
    'Welcome to your dashboard',
    'Have a wonderful day',
    'Welcome to your dashboard',
    'Looking good',
    'Greetings',
    'Nice to see you',
  ];
  return greetings[Math.floor(Math.random() * greetings.length)];
}
</script>
