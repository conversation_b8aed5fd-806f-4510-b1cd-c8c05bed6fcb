<template>
  <VCard>
    <VCardHeader>
      <h2 class="text-platinum-50 dark:text-midnight-950">
        <BIconMegaphone class="-mt-1 mr-2 inline w-4" aria-hidden="true" />Announcements
      </h2>
    </VCardHeader>
    <div class="grid gap-px divide-y divide-platinum-200 dark:divide-midnight-700">
      <VCardBody class="!p-0">
        <TransitionFade>
          <div
            v-if="isLoading"
            class="relative px-3 divide-y divide-platinum-200 dark:divide-midnight-700"
          >
            <div
              v-for="i in perPageCount"
              :key="i"
              class="py-3"
            >
              <div class="p-3 flex flex-col space-y-3">
              <VSkeleton class="w-2/3 max-w-[175px] h-4 rounded-lg" />
              <VSkeleton class="h-3 w-4/5" />
              <VSkeleton class="h-3 w-2/3" />
              </div>
            </div>
            <div class="absolute inset-0 flex items-center justify-center">
              <VSpinner size="lg" />
            </div>
          </div>
          <ul
            v-else-if="announcements?.data?.length"
            role="list"
            class="divide-y divide-platinum-200 dark:divide-midnight-700"
          >
            <li v-for="announcement in announcements.data" :key="announcement.locked_id">
              <router-link
                :to="{
                  name: 'announcements.show',
                  params: { id: announcement.locked_id },
                }"
                class="block p-3 group cursor-pointer focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-300 dark:focus-within:ring-primary-600"
              >
                <div
                  class="relative rounded p-3 transition group-hover:bg-primary-100 dark:group-hover:bg-midnight-300 block"
                >
                  <div class="flex justify-between">
                    <h3 class="text-base font-bold text-primary-500 transition-colors group-hover:text-primary-600 focus:outline-none dark:group-hover:text-primary-400 -mt-1">
                      {{ announcement.title }}
                    </h3>
                    <BIconArrowRight
                      class="w-3.5 -translate-x-3 text-primary-600 opacity-0 transition group-hover:translate-x-0 group-hover:opacity-100 dark:text-primary-400"
                      aria-hidden="true"
                    />
                  </div>
                  <p class="mt-1 line-clamp-2 text-sm text-platinum-950 dark:text-midnight-50">{{ stripHtmlTags(announcement.body) }}</p>
                </div>
              </router-link>
            </li>
          </ul>
          <div v-else class="px-2 py-4 sm:p-6 text-center text-sm text-platinum-600 dark:text-midnight-200 italic">
            No announcements to display.
          </div>
        </TransitionFade>
      </VCardBody>
      <VCardFooter>
        <VButton
          as="router-link"
          :to="{ name: 'announcements.index' }"
          centered
          class="w-full"
          intent="primary"
        >
          <BIconSearch class="mr-2" />View All
        </VButton>
      </VCardFooter>
    </div>
  </VCard>
</template>

<script setup>
import BIconMegaphone from '~icons/bi/megaphone';
import BIconSearch from '~icons/bi/search';
import BIconArrowRight from '~icons/bi/arrow-right';
import { stripHtmlTags } from '@/util/textUtils';

import { useAnnouncements } from '@/modules/announcements/api/get-announcements';
import { useFilterParams } from '@/hooks/query';

const perPageCount = 5;

const { params, search, sort, paginate } = useFilterParams();

const { data: announcements, isLoading } = useAnnouncements({
  params: { ...params.value, per_page: perPageCount }, // Set a page limit of 5
});

</script>
