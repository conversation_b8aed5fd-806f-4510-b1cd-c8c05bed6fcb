<template>
  <div id="keeptalking" style="position: absolute; top: 85vh"></div>
  <section class="page bg-ascent2 justify-content-center flex" style="min-height: 120vh">
    <div class="container" style="margin-top: -10vh; z-index: 99">
      <div class="card">
        <div class="p-4">
          <div class="card-header mb-3">
            <h3>Current Progress</h3>
          </div>
          <div class="card-body">
            <ul class="list list-timeline">
              <li v-for="(entry, index) in timeline.entries" :key="index">
                <div class="list-timeline-icon" :class="'bg-' + entry.icon.backgroundColor"></div>
                <div class="list-timeline-content">
                  <div class="list-timeline-time">{{ entry.date }}</div>
                  <p class="list-timeline-title">{{ entry.title }}</p>
                  <p class="text-muted">{{ entry.desc }}</p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="flex-item align-items-end justify-content-center flex" style="z-index: 1">
        <svg width="100%" height="100%" version="1.1" xmlns="http://www.w3.org/2000/svg" class="wave">
          <defs></defs>
          <path id="wave3" d="" />
        </svg>
        <svg width="100%" height="100%" version="1.1" xmlns="http://www.w3.org/2000/svg" class="wave">
          <defs></defs>
          <path id="wave4" d="" />
        </svg>
      </div>
    </div>
    <div class="row" style="z-index: 999">
      <div class="col-12 mt-4 text-center">
        <a
          class="btn btn-standard animate__animated animate__headShake animate__infinite animate__slower"
          href="#partyon"
        >
          <span class="flex-column flex">
            <span class="align-self-center">{{ content.buttonText }}</span>
            <b-icon-chevron-compact-down class="align-self-center" style="font-size: 1.5em; margin-bottom: -5px" />
          </span>
        </a>
      </div>
    </div>
  </section>
</template>

<script>
import BIconChevronCompactDown from '~icons/bi/chevron-compact-down';

export default {
  name: 'Timeline',
  components: {
    BIconChevronCompactDown,
  },
  data() {
    return {
      timeline: {
        entries: [
          {
            date: '2020-01-01',
            title: 'Example',
            desc: 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Culpa dolores placeat, unde, laudantium, facilis ipsam eligendi odit a ratione nostrum assumenda aliquam velit aperiam temporibus!',
            icon: {
              backgroundColor: 'red',
              icon: null,
            },
          },
          {
            date: '2020-03-01',
            title: 'Example',
            desc: 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt eaque ipsam dignissimos quae optio magnam assumenda voluptate aspernatur libero, cum at, beatae expedita veritatis dolor, esse dolores. Qui debitis quia tempore ab doloremque eum!',
            icon: {
              backgroundColor: 'blue',
              icon: null,
            },
          },
          {
            date: '2020-05-01',
            title: 'Example',
            desc: 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Non corporis recusandae id omnis? Laborum illum asperiores fugit molestiae vitae nisi tenetur sequi aperiam similique. Aliquam voluptate quidem dolores dolorum, magni totam excepturi natus et voluptatem distinctio suscipit hic corporis! In tempore repudiandae repellendus culpa ducimus explicabo corporis.',
            icon: {
              backgroundColor: 'yellow',
              icon: null,
            },
          },
          {
            date: '2020-10-01',
            title: 'Example',
            desc: 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aut vero debitis a sit enim nulla suscipit exercitationem dignissimos, aspernatur, quasi nisi.',
            icon: {
              backgroundColor: 'green',
              icon: null,
            },
          },
        ],
      },
      content: {
        buttonText: 'Sure, sure... but what else?!',
      },
    };
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
h1 {
  font-size: 32pt;
}
</style>
