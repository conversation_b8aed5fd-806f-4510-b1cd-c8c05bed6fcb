<template>
  <div>
    <span v-if="$slots.default" @click="reveal" class="inline-block">
      <slot />
    </span>

    <VModal :open="typeof open === 'boolean' ? open : isRevealed" :size="size" @close="close">
      <template #title>
        <div class="flex items-center">
          <div
            class="mx-auto mr-2 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-primary-500 sm:hidden"
          >
            <BIconInfoCircleFill class="h-5 w-5 text-white dark:text-midnight-500" aria-hidden="true" />
          </div>
          <div class="mt-1 font-bold text-primary-500">
            <slot name="title" />
          </div>
        </div>
      </template>

      <template #aside>
        <div
          class="mx-auto hidden h-14 w-14 flex-shrink-0 items-center justify-center rounded-full bg-primary-500 sm:mx-0 sm:flex sm:h-10 sm:w-10"
        >
          <BIconInfoCircleFill class="h-5 w-5 text-white" aria-hidden="true" />
        </div>
      </template>

      <template #content>
        <div class="max-h-[75vh] overflow-y-auto">
          <div class="text-sm text-primary-500 dark:text-midnight-50">
            <slot name="body" />
          </div>
        </div>
      </template>

      <template #footer>
        <VButton size="sm" @click="close">
          <div class="flex items-center justify-center">
            <b-icon-x class="h-5 w-5" />
            Close
          </div>
        </VButton>
      </template>
    </VModal>
  </div>
</template>

<script setup>
import { useConfirmDialog } from '@vueuse/core';

import BIconCheck from '~icons/bi/check';
import BIconInfoCircleFill from '~icons/bi/info-circle-fill';
import BIconX from '~icons/bi/x';

const props = defineProps({
  controlled: {
    type: Boolean,
    default: false,
  },
  open: {
    type: Boolean,
    default: null,
  },
  size: {
    type: String,
    default: 'sm',
  },
});

const { isRevealed, reveal, cancel } = useConfirmDialog();

const emit = defineEmits(['close']);

const close = () => {
  cancel();
  emit('close');
};
</script>
