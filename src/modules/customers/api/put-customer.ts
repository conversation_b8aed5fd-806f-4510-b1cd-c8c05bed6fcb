import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getCustomerQueryOptions } from './get-customer';

import { Customer } from '@/types/api';

export const updateCustomer = ({ customerId, data }: { customerId: string, data: UpdateCustomerPayload }) => {
  return Reshape.http().put(`/api/customers/${customerId}`, data);
};

type UpdateCustomerPayload = Partial<Omit<Customer, 'locked_id'>>;

type UseUpdateCustomerOptions = {
  customerId: string;
  mutationConfig?: MutationConfig<typeof updateCustomer>;
};

export const useUpdateCustomer = ({ customerId, mutationConfig }: UseUpdateCustomerOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['customers', args[1].customerId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getCustomerQueryOptions(args[1].customerId).queryKey,
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: updateCustomer,
  });
};