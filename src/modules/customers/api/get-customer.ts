import { useQuery, queryOptions } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Customer } from '@/types/api';

export const getCustomer = ({
  customerId,
}: {
  customerId: string;
}): Promise<{ data: Customer }> => {
  return Reshape.http()
    .get(`/api/customers/${customerId}`, {
      params: {
        include: ['addresses', 'contacts', 'socials']
      }
    })
    .then(({ data }) => data);
};

export const getCustomerQueryOptions = (customerId: string) => {
  return queryOptions({
    queryKey: ['customers', customerId],
    queryFn: () => getCustomer({ customerId }),
  });
};

type UseCustomerOptions = {
  customerId: string;
  queryConfig?: QueryConfig<typeof getCustomerQueryOptions>;
};

export const useCustomer = ({ customerId, queryConfig }: UseCustomerOptions) => {
  return useQuery({
    ...getCustomerQueryOptions(customerId),
    ...queryConfig,
  });
}; 