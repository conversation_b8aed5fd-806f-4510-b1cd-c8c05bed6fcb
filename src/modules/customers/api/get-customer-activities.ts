import { queryOptions, useQuery } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Activity, Meta } from '@/types/api';
import { Reshape } from '@/Reshape';

export const getCustomerActivities = (
  params,
  signal,
  id,
): Promise<{ data: Activity[]; meta: Meta }> => {
  return Reshape.http().get(
    `/api/customers/${id}/activity`,
    {
      params,
      signal,
    }
  );
};

export const getCustomerActivitiesQueryOptions = ({
  params,
  id,
}: {
  params?: unknown;
  id?: string;
} = {}) => {
  return queryOptions({
    queryKey: params ? ['customer-activities', params] : ['customer-activities'],
    queryFn: ({ signal }) => getCustomerActivities(params, signal, id),
  });
};

type UseCustomerActivitiesOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getCustomerActivitiesQueryOptions>;
  id?: string;
};

export const useCustomerActivities = ({ queryConfig, params, id }: UseCustomerActivitiesOptions) => {
  return useQuery({
    ...getCustomerActivitiesQueryOptions({ params, id }),
    ...queryConfig,
  });
}; 