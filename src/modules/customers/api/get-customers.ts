import { queryOptions, useQuery } from '@tanstack/vue-query';
import { Customer, Meta } from '@/types/api';

import { QueryConfig } from '@/lib/vue-query';

export const getCustomers = (
  params,
  signal
): Promise<{
  data: Customer[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/customers`, {
    params,
    signal,
  });
};

export const getCustomersQueryOptions = ({ params }: { params?: unknown } = {}) => {
  return queryOptions({
    queryKey: params ? ['customers', params] : ['customers'],
    queryFn: ({ signal }) => getCustomers(params, signal),
  });
};

type UseCustomersOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getCustomersQueryOptions>;
};

export const useCustomers = ({ queryConfig, params }: UseCustomersOptions = {}) => {
  return useQuery({
    ...getCustomersQueryOptions({ params }),
    ...queryConfig,
  });
}; 