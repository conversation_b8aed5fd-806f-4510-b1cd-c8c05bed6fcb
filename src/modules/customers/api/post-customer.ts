import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { Customer } from '@/types/api';

export const createCustomer = ({ data }: { data: CreateCustomerPayload }) => {
  return Reshape.http().post(`/api/customers`, data);
};

type CreateCustomerPayload = Omit<Customer, 'id' | 'locked_id'>;

type UseCreateCustomerOptions = {
  mutationConfig?: MutationConfig<typeof createCustomer>;
};

export const useCreateCustomer = ({ mutationConfig }: UseCreateCustomerOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ['customers'],
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createCustomer,
  });
};