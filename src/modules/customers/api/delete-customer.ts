import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getCustomersQueryOptions } from './get-customers';

// Declare the Reshape global variable
declare const Reshape: {
  http: () => {
    delete: (url: string) => Promise<any>;
  };
};

export const deleteCustomer = ({ customerId }: { customerId: string }) => {
  return Reshape.http().delete(`/api/customers/${customerId}`);
};

type UseDeleteCustomerOptions = {
  mutationConfig?: {
    onSuccess?: (...args: any[]) => void;
    [key: string]: any;
  };
};

export const useDeleteCustomer = ({ mutationConfig }: UseDeleteCustomerOptions = {}) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['customers', args[1].customerId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getCustomersQueryOptions().queryKey,
      });
      if (onSuccess) {
        onSuccess(...args);
      }
    },
    ...restConfig,
    mutationFn: deleteCustomer,
  });
}; 