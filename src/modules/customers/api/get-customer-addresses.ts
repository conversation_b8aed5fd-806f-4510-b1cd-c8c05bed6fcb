import { queryOptions, useQuery } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Address, Meta } from '@/types/api';

export const getCustomerAddresses = (
  params,
  signal,
  id
): Promise<{
  data: Address[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/customers/${id}/addresses`, {
    params,
    signal,
  });
};

export const getCustomerAddressesQueryOptions = ({ params, id }: { params?: unknown, id?: string } = {}) => {
  return queryOptions({
    queryKey: params ? ['customer-addresses', params] : ['customer-addresses'],
    queryFn: ({ signal }) => getCustomerAddresses(params, signal, id),
  });
};

type UseCustomerAddressesOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getCustomerAddressesQueryOptions>;
};

export const useCustomerAddresses = ({ queryConfig, params, id }: UseCustomerAddressesOptions) => {
  return useQuery({
    ...getCustomerAddressesQueryOptions({ params, id }),
    ...queryConfig,
  });
};
