import { queryOptions, useQuery } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Contact, Meta } from '@/types/api';

export const getCustomerContacts = (
  params,
  signal,
  id
): Promise<{
  data: Contact[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/customers/${id}/contacts`, {
    params,
    signal,
  });
};

export const getCustomerContactsQueryOptions = ({ params, id }: { params?: unknown, id?: string } = {}) => {
  return queryOptions({
    queryKey: params ? ['customer-contacts', params] : ['customer-contacts'],
    queryFn: ({ signal }) => getCustomerContacts(params, signal, id),
  });
};

type UseCustomerContactsOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getCustomerContactsQueryOptions>;
  id?: string;
};

export const useCustomerContacts = ({ queryConfig, params, id }: UseCustomerContactsOptions) => {
  return useQuery({
    ...getCustomerContactsQueryOptions({ params, id }),
    ...queryConfig,
  });
};
