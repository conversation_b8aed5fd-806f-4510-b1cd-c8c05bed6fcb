<template>
  <div>
    <VPageHeader title="View Customer" description="Overview of selected customer." />
    <VContainer class="my-3">
      <TransitionFade>
        <div v-if="isLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>
        <div v-else-if="customer" class="grid gap-3 lg:grid-cols-5 items-start">
          <ViewPanel class="col-span-5 xl:col-span-4 order-2 xl:order-1">
            <VCardHeader>Customer Record</VCardHeader>
            <PanelDefaultField label="Customer Type" :value="customerType" />
            <PanelDefaultField label="Status" :value="customer?.status?.name">
              <template #default>
                <CustomerStatusBadge :status="customer?.status?.name" />
              </template>
            </PanelDefaultField>
            <PanelDefaultField
              label="Owning Entity"
              :value="customer?.owner?.name"
              help="The entity that this customer record belongs to."
              allowCopy
            >
              <template #default>
                <VEntityLink
                  :entityType="customer?.owner?.type"
                  :entityId="customer?.owner?.locked_id"
                  :value="customer?.owner?.name"
                />
              </template>
            </PanelDefaultField>
            <PanelDefaultField
              label="Created By"
              :value="customer?.created_by?.full_name"
              help="The user that created this customer record."
              allowCopy
            >
              <template #default>
                <VEntityLink
                  entityType="user"
                  :entityId="customer?.created_by?.locked_id"
                  :value="customer?.created_by?.full_name"
                />
              </template>
            </PanelDefaultField>
            <PanelDateTimeField label="Date Created" :value="customer.created_at" />
            <PanelDateTimeField label="Date Updated" :value="customer.updated_at" />
          </ViewPanel>

          <VCard class="col-span-5 xl:col-span-1 order-1 xl:order-2">
            <VCardHeader>Actions</VCardHeader>
            <VCardBody class="flex gap-3 justify-start flex-wrap">
              <!-- Button that returns the user to the referring page. -->
              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'customers.index' }"
              >All Customers
                <template #start>
                  <BIconListUl />
                </template>
              </VButton>

              <VButton
                intent="primary"
                as="router-link"
                :to="{ name: 'customers.activity', params: { id: customer.locked_id } }"
              >View Activity
                <template #start>
                  <BIconActivity />
                </template>
              </VButton>

              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <VButton intent="primary">
                    <template #start>
                      <BIconSearch />
                    </template>
                    <template #end>
                      <BIconChevronDown />
                    </template>
                    Related Records
                  </VButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{
                        name: 'customers.contacts.index',
                        params: { id: customer.locked_id },
                      }"
                      class="w-full"
                    >
                      <BIconPersonVCardFill />Contacts
                    </RouterLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{
                        name: 'customers.addresses.index',
                        params: { id: customer.locked_id },
                      }"
                      class="w-full"
                    >
                      <BIconJournalBookmarkFill />Addresses
                    </RouterLink>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <VButton intent="danger">
                    <template #start>
                      <BIconPencilSquare />
                    </template>
                    <template #end>
                      <BIconChevronDown />
                    </template>
                    Modify
                  </VButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{
                        name: 'customers.update',
                        params: { id: customer.locked_id },
                      }"
                      class="w-full"
                    >
                      <BIconPencilSquare />Edit
                    </RouterLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem @click="openCustomerDeleteConfirmationDialog">
                    <BIconTrash />Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </VCardBody>
          </VCard>

          <CustomerViewInformation :customer="customer" class="col-span-5 order-2 xl:order-3" />
        </div>
        <template v-else>
          <VEmptyState
            title="Customer Not Found"
            description="The requested customer was not found."
            :contact="true"
          />
        </template>
      </TransitionFade>
    </VContainer>

    <VConfirmModal
      v-model:open="isCustomerDeleteConfirmationDialog"
      @confirmed="deleteCustomerMutation.mutate({
        customerId: props.id
      })"
      title="Confirm Customer Deletion"
      :description="`<p>You're about to delete the requested customer: <b>${name}</b>.</p><p>Please confirm you wish to proceed.</p>`"
      :icon="BIconTrashFill"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BIconListUl from '~icons/bi/list-ul';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconChevronDown from '~icons/bi/chevron-down';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconPersonVCardFill from '~icons/bi/person-vcard-fill';
import BIconJournalBookmarkFill from '~icons/bi/journal-bookmark-fill';
import BIconSearch from '~icons/bi/search';
import BIconActivity from '~icons/bi/activity';
import ViewPanel from '@/components/ui/panels/ViewPanel.vue';
import PanelDefaultField from '@/components/ui/panels/fields/ViewPanel/PanelDefaultField.vue';
import PanelDateTimeField from '@/components/ui/panels/fields/ViewPanel/PanelDateTimeField.vue';
import CustomerStatusBadge from '@/modules/customers/components/CustomerStatusBadge.vue'
import CustomerViewInformation from '@/modules/customers/components/CustomerView/CustomerViewInformation.vue';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';

import { useDisclosure } from '@/hooks';
import { useRouter } from 'vue-router';
import { useDeleteCustomer } from '@/modules/customers/api/delete-customer';
import { useCustomer } from '@/modules/customers/api/get-customer';
import { ref } from 'vue';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const router = useRouter();

const {
  isOpen: isCustomerDeleteConfirmationDialog,
  open: openCustomerDeleteConfirmationDialog
} = useDisclosure(false);

const { data: customer, isLoading } = useCustomer({
  customerId: props.id,
});

// Computed property to return the customer name if set.
const name = computed(() => {
  if (customer.value?.informationable_type === 'CustomerPersonInformation') {
    return customer.value.information.full_name;
  } else if (customer.value?.informationable_type === 'CustomerCompanyInformation') {
    return customer.value.information.name;
  } else {
    return 'Undefined';
  }
});

// Computed property to return the customer type.
const customerType = computed(() => {
  if (customer.value?.informationable_type === 'CustomerPersonInformation') {
    return 'Person';
  } else if (customer.value?.informationable_type === 'CustomerCompanyInformation') {
    return 'Company';
  }
  return null;
});

const deleteCustomerMutation = useDeleteCustomer({
  mutationConfig: {
    onSuccess: () => {
      Reshape.toast(
        {
          title: 'Customer Deleted',
          message: `The requested customer <b>${name.value}</b> was successfully deleted.`,
        },
        'success'
      );
      router.push({
        name: 'customers.index',
      });
    },
  },
});

</script>
