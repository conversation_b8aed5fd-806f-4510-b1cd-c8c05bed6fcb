<template>
  <div>
    <VPageHeader
      title="Customers"
      description="Overview of all customers defined within the application."
    >
      <template #actions>
        <VButton
          as="router-link"
          intent="secondary"
          :to="{ name: 'customers.create' }"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Add Customer
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="py-2 lg:py-6">
      <CustomerTable
        title="All Customers"
        :records="customers?.data"
        :total-records="customers?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
      />
    </VContainer>
  </div>
</template>

<script setup lang="ts">
import { useFilterParams } from '@/hooks/query';
import CustomerTable from '@/modules/customers/components/CustomerTable.vue';
import BIconPlusLg from '~icons/bi/plus-lg';
import { useCustomers } from '../api/get-customers';

const { params, search, sort, paginate } = useFilterParams();

const { data: customers, isLoading } = useCustomers({
  params: params.value,
});
</script>
