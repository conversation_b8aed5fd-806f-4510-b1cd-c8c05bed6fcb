<template>
  <div>
    <VPageHeader
      title="Update Customer"
      description="Update an existing customer in the system."
    >
      <template #actions>
        <VButton type="submit"
          form="customer-form"
          intent="secondary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Confirm
        </VButton>
        <VButton
          intent="danger"
          @click="confirmModalOpen = true"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <TransitionFade>
        <CustomerForm
          v-if="!isCustomerLoading"
          :customer="customer"
          @submit="submit"
        />
        <div v-else class="flex w-full justify-center p-6">
          <VSpinner size="xl" />
        </div>
      </TransitionFade>

      <div class="flex w-full justify-start lg:justify-end space-x-3">
        <VButton
          type="submit"
          form="customer-form"
          intent="primary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Confirm
        </VButton>

        <VButton intent="danger" @click="confirmModalOpen = true">
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </div>
    </VContainer>
    
    <VConfirmModal
      @confirmed="router.push({ name: 'customers.view', params: { id: props.id } })"
      v-model:open="confirmModalOpen"
    />
  </div>
</template>

<script setup>
import CustomerForm from '@/modules/customers/components/CustomerForm.vue';
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconCheckLg from '~icons/bi/check-lg';
import { ref, computed } from 'vue';
import { useUpdateCustomer } from '@/modules/customers/api/put-customer';
import { useCustomer } from '@/modules/customers/api/get-customer';

const confirmModalOpen = ref(false);

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const updatedCustomer = ref({});

// Customer name for either a person or company.
const customerName = computed(() => {
  if (updatedCustomer.value.informationable_type === 'CustomerPersonInformation') {
    return updatedCustomer.value.information.full_name;
  } else if (updatedCustomer.value.informationable_type === 'CustomerCompanyInformation') {
    return updatedCustomer.value.information.name;
  } else {
    return 'Undefined';
  }
});

const updateCustomerMutation = useUpdateCustomer({
  mutationConfig: {
    onSuccess: (result) => {
      updatedCustomer.value = result.data;
      Reshape.toast(
        {
          title: 'Customer Successfully Updated',
          message: `The customer <b>${customerName.value}</b> was successfully updated.`,
        },
        'success'
      );
      router.push({ name: 'customers.view', params: { id: result.data.locked_id } });
    },
  },
});

const { data: customer, isLoading: isCustomerLoading } = useCustomer({
  customerId: props.id
});

const submit = (form) => {
  form.submit((data) => updateCustomerMutation.mutateAsync({
    customerId: props.id,
    data,
  }));
};

</script>
