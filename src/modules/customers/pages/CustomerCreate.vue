<template>
  <div>
    <VPageHeader
      title="Add Customer"
      description="Define a new customer for use throughout the application."
    >
      <template #actions>
        <VButton type="submit"
          form="customer-form"
          intent="secondary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>
        <VButton
          intent="danger"
          @click="confirmModalOpen = true"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <CustomerForm @submit="submit" />

      <div class="flex w-full justify-start lg:justify-end space-x-3">
        <VButton
          type="submit"
          form="customer-form"
          intent="primary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>

        <VButton intent="danger" @click="confirmModalOpen = true">
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </div>
    </VContainer>
    
    <VConfirmModal
      @confirmed="router.push({ name: 'customers.index' })"
      v-model:open="confirmModalOpen"
    />
  </div>
</template>

<script setup>
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconPlusLg from '~icons/bi/plus-lg';
import CustomerForm from '@/modules/customers/components/CustomerForm.vue';
import { ref, computed } from 'vue';
import { useCreateCustomer } from '@/modules/customers/api/post-customer';
const confirmModalOpen = ref(false);

const customer = ref({});

// Customer name for either a person or company.
const customerName = computed(() => {
  if (customer.value.informationable_type === 'CustomerPersonInformation') {
    return customer.value.information.full_name;
  } else if (customer.value.informationable_type === 'CustomerCompanyInformation') {
    return customer.value.information.name;
  } else {
    return 'Undefined';
  }
});

const createCustomerMutation = useCreateCustomer({
  mutationConfig: {
    onSuccess: (result) => {
      customer.value = result.data;
      Reshape.toast(
        {
          title: 'Customer Successfully Created',
          message: `The customer <b>${customerName.value}</b> was successfully created.`,
        },
        'success'
      );
      router.push({
        name: 'customers.view',
        params: { id: result.data.locked_id }
      });
    },
  },
});

const submit = (form) => {
  form.submit((data) => createCustomerMutation.mutateAsync({
    data,
  }));
};

</script>
