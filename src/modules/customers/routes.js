import UserLayout from '@/layouts/UserLayout.vue';
import { auth } from '@/router/middleware';

const routes = [
  {
    path: '/customers',
    component: UserLayout,
    meta: {
      middleware: auth,
    },
    children: [
      {
        path: '',
        name: 'customers.index',
        component: () => import('./pages/CustomerIndex.vue'),
        meta: {
          title: 'Customers',
          breadcrumbs: [
            { label: 'Customers', name: 'customers.index' },
          ],
        },
      },
      {
        path: ':id/view',
        name: 'customers.view',
        component: () => import('./pages/CustomerView.vue'),
        props: true,
        meta: {
          title: 'View Customer',
          breadcrumbs: [
            { label: 'Customers', name: 'customers.index' },
            { label: 'View', name: 'customers.view' },
          ],
        },
      },
      {
        path: 'add',
        name: 'customers.create',
        component: () => import('./pages/CustomerCreate.vue'),
        meta: {
          title: 'Add Customer',
          breadcrumbs: [
            { label: 'Customers', name: 'customers.index' },
            { label: 'Add', name: 'customers.create' },
          ],
        },
      },
      {
        path: ':id/update',
        name: 'customers.update',
        component: () => import('./pages/CustomerUpdate.vue'),
        props: true,
        meta: {
          title: 'Edit Customer',
          breadcrumbs: [
            { label: 'Customers', name: 'customers.index' },
            { label: 'Edit', name: 'customers.update' },
          ],
        },
      },
      {
        path: ':id/contacts',
        children: [
          {
            path: '',
            name: 'customers.contacts.index',
            component: () => import('@/modules/contacts/pages/ContactsIndex.vue'),
            props: route => ({
              contactableType: 'customers',
              contactableId: route.params.id,
            }),
            meta: {
              title: 'Customer Contacts',
              headerTitle: 'Customer Contacts',
              headerDescription: 'Overview of all contacts related to the selected customer.',
              breadcrumbs: [
                { label: 'Customers', name: 'customers.index' },
                { label: 'View', name: 'customers.view' },
                { label: 'Contacts', name: 'customers.contacts.index' },
              ],
            },
          },
          {
            path: 'add',
            name: 'customers.contacts.create',
            component: () => import('@/modules/contacts/pages/ContactsCreate.vue'),
            props: route => ({
              contactableType: 'customers',
              contactableId: route.params.id,
            }),
            meta: {
              title: 'Add Customer Contact',
              headerTitle: 'Add Customer Contact',
              headerDescription: 'Define a new contact for your chosen customer.',
              breadcrumbs: [
                { label: 'Customers', name: 'customers.index' },
                { label: 'View', name: 'customers.view' },
                { label: 'Contacts', name: 'customers.contacts.index' },
                { label: 'Add', name: 'customers.contacts.create' },
              ],
            },
          },
        ],
      },
      {
        path: ':id/addresses',
        children: [
          {
            path: '',
            name: 'customers.addresses.index',
            component: () => import('@/modules/addresses/pages/AddressIndex.vue'),
            props: route => ({
              addressableType: 'customers',
              addressableId: route.params.id,
            }),
            meta: {
              title: 'Customer Addresses',
              headerTitle: 'Customer Addresses',
              headerDescription: 'Overview of all addresses related to the selected customer.',
              breadcrumbs: [
                { label: 'Customers', name: 'customers.index' },
                { label: 'View', name: 'customers.view' },
                { label: 'Addresses', name: 'customers.addresses.index' },
              ],
            },
          },
          {
            path: 'add',
            name: 'customers.addresses.create',
            component: () => import('@/modules/addresses/pages/AddressCreate.vue'),
            props: route => ({
              addressableType: 'customers',
              addressableId: route.params.id,
            }),
            meta: {
              title: 'Add Customer Address',
              headerTitle: 'Add Customer Address',
              headerDescription: 'Define a new address for your chosen customer.',
              breadcrumbs: [
                { label: 'Customers', name: 'customers.index' },
                { label: 'View', name: 'customers.view' },
                { label: 'Addresses', name: 'customers.addresses.index' },
                { label: 'Add', name: 'customers.addresses.create' },
              ],
            },
          },
        ],
      },
      {
        path: ':id/activity',
        name: 'customers.activity',
        component: () => import('@/modules/activity/pages/ActivityIndex.vue'),
        props: (route) => ({
          activitableType: 'Customer',
          activitableId: route.params.id,
        }),
        meta: {
          title: 'Customer Activity',
          headerTitle: 'Customer Activity',
          headerDescription: 'View historical activity for the selected customer. This includes the customer and related detail records only.',
          breadcrumbs: [
            { label: 'Customers', name: 'customers.index' },
            { label: 'View', name: 'customers.view' },
            { label: 'Activity', name: 'customers.activity' },
          ],
        },
      },
    ],
  },
];

export default routes; 