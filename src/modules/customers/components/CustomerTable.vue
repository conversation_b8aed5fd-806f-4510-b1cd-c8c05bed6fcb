<template>
  <Table
    v-bind="$attrs"
    :columns="columns"
    :default-sort="[{
      id: 'name',
      desc: true,
    }]"
    paginated
    sortable
    searchable
    controlled
  />
</template>

<script setup>
import { h, ref } from 'vue';

import Table from '@/components/ui/tables/Table.vue';

import ActionCell from './CustomerTableActionCell.vue';
import CustomerStatusBadge from './CustomerStatusBadge.vue';
import TableCell from '@/components/ui/tables/TableCell.vue';
import CustomerTypeValue from '@/modules/customers/components/CustomerTypeValue.vue';
import { humanizeTime } from '@/util/dateUtils';
import EntityLink from '@/components/ui/links/EntityLink.vue';

const columns = ref([
  {
    accessorFn: (row) => row?.information?.full_name ?? row?.information?.name,
    id: 'name',
    header: 'Name',
    cell: (info) => h(EntityLink, {
      value: info.getValue(),
      entityType: 'Customer',
      entityId: info.row.original.locked_id,
      showIcon: false,
      showAvatar: true,
      avatarSize: 'sm',
    }),
  },
  {
    accessorFn: (row) => row?.informationable_type,
    id: 'informationable_type',
    header: 'Customer Type',
    cell: (info) => h(CustomerTypeValue, { value: info.getValue() }),
  },
  {
    accessorKey: 'created_at',
    header: 'Created Date',
    cell: (info) => humanizeTime(info.row.original.created_at),
  },
  {
    accessorKey: 'owner.name',
    header: 'Owning Entity',
    cell: (info) => h(EntityLink, {
      value: info.row.original.owner?.name,
      entityType: info.row.original.owner?.type,
      entityId: info.row.original.owner?.locked_id
    }),
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: (info) => h(CustomerStatusBadge, {
      status: info.row.original.status?.name,
      key: info.row.original.locked_id,
    }),
  },
  {
    id: 'actions',
    header: () => h('span', { class: 'w-full flex justify-end' }),
    cell: (info) => h(ActionCell, {
      row: info.row.original,
      key: info.row.original.locked_id,
    }),
    enableSorting: false,
    enableResizing: false,
    size: 50,
  },
]);
</script>
