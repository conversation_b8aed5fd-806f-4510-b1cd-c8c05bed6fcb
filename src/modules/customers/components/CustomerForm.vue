<template>
  <form id="customer-form" @submit.prevent="submit">
    <FormStructure v-model:sections="sections">

      <CustomerIntroSection
        v-model="form"
        name="intro"
        label="Customer Intro"
        title="Customer Introduction"
        description="Complete to display appropriate form fields relevant to the customer."
        required
        :customer="customer"
        :icon="icons.intro"
        show
      />

      <CustomerDetailsPersonSection
        v-model="form"
        name="personInformation"
        label="Person Information"
        title="Person Information"
        description="Relevant information that defines a individual person customer type."
        required
        :customer="customer"
        :icon="icons.detailsPerson"
        :show="form.intro.informationable_type === 'CustomerPersonInformation'"
        :showInNav="form.intro.informationable_type === 'CustomerPersonInformation'"
      />

      <CustomerDetailsCompanySection
        v-model="form"
        name="companyInformation"
        label="Company Information"
        title="Company Information"
        description="Relevant information that defines a company customer type."
        required
        :customer="customer"
        :icon="icons.detailsCompany"
        :show="form.intro.informationable_type === 'CustomerCompanyInformation'"
        :showInNav="form.intro.informationable_type === 'CustomerCompanyInformation'"
      />

      <ContactsPanel
        v-model="form"
        name="contacts"
        label="Contacts & Connections"
        title="Contact Methods & Connections"
        description="Setup this customer's contact methods and other connections, including external social media references."
        :entity="customer"
        entityType="Customer"
        :showSocials="true"
        :icon="icons.contacts"
      />

      <AddressesPanel
        v-model="form"
        name="addresses"
        label="Addresses"
        title="Address Records"
        description="Configure addresses in relation to this customer."
        :entity="customer"
        entityType="Customer"
        :icon="icons.addresses"
      />

      <template #actions v-if="$slots.actions">
        <slot name="actions" />
      </template>
    </FormStructure>
  </form>
</template>

<script setup>
import { markRaw, ref, watch } from 'vue';
import FormStructure from '@/components/ui/forms/tabbed/FormStructure.vue';
import { useForm } from '@/hooks/form';
import BIconPersonLinesFill from '~icons/bi/person-lines-fill';
import BIconUniversalAccessCircle from '~icons/bi/universal-access-circle';
import BIconChatQuoteFill from '~icons/bi/chat-quote-fill';
import BIconPinMapFill from '~icons/bi/pin-map-fill';
import CustomerIntroSection from '@/modules/customers/components/CustomerFormSections/CustomerIntroSection.vue';
import CustomerDetailsPersonSection from '@/modules/customers/components/CustomerFormSections/CustomerDetailsPersonSection.vue';
import CustomerDetailsCompanySection from '@/modules/customers/components/CustomerFormSections/CustomerDetailsCompanySection.vue';
import ContactsPanel from '@/components/ui/forms/tabbed/composables/ContactsPanel.vue';
import AddressesPanel from '@/components/ui/forms/tabbed/composables/AddressesPanel.vue';

const props = defineProps({
  customer: {
    type: Object,
    default: null,
  },
});

const emits = defineEmits(['submit']);

const sections = ref([]);

const icons = {
  intro: markRaw(BIconUniversalAccessCircle),
  detailsPerson: markRaw(BIconPersonLinesFill),
  detailsCompany: markRaw(BIconPersonLinesFill),
  contacts: markRaw(BIconChatQuoteFill),
  addresses: markRaw(BIconPinMapFill),
};

const form = useForm({
  intro: {
    informationable_type: props.customer?.informationable_type ?? '',
    status_id: props.customer?.status_id ?? null,
    owning_customerable_type: props.customer?.owner?.type ?? '',
    owning_customerable_id: props.customer?.owner?.locked_id ?? '',
  },

  personInformation: {
    email: props.customer?.information?.email ?? '',
    first_name: props.customer?.information?.first_name ?? '',
    middle_names: props.customer?.information?.middle_names ?? '',
    last_name: props.customer?.information?.last_name ?? '',
    honorific_id: props.customer?.information?.honorific?.locked_id ?? null,
    // Note: This is a bit of a hack to prevent shared fields from being populated when the customer type is changed.
    type_id: props.customer?.informationable_type === 'CustomerPersonInformation' && props.customer?.information?.type?.locked_id
      ? props.customer?.information?.type?.locked_id
      : null,
    website_url: props.customer?.informationable_type === 'CustomerPersonInformation'
      ? props.customer?.information?.website_url ?? ''
      : null,
    timezone_id: props.customer?.informationable_type === 'CustomerPersonInformation'
      ? props.customer?.information?.timezone?.locked_id ?? null
      : null,
    country_id: props.customer?.informationable_type === 'CustomerPersonInformation'
      ? props.customer?.information?.country?.locked_id ?? null
      : null,
  },

  companyInformation: {
    name: props.customer?.information?.name ?? '',
    legal_name: props.customer?.information?.legal_name ?? '',
    structure_type_id: props.customer?.information?.structure_type?.locked_id ?? null,
    industry_id: props.customer?.information?.industry?.locked_id ?? null,
    type_id: props.customer?.informationable_type === 'CustomerCompanyInformation' && props.customer?.information?.type?.locked_id
      ? props.customer?.information?.type?.locked_id
      : null,
    representative_email: props.customer?.information?.representative_email ?? '',
    representative_honorific_id: props.customer?.information?.representative_honorific?.locked_id ?? null,
    representative_first_name: props.customer?.information?.representative_first_name ?? '',
    representative_middle_names: props.customer?.information?.representative_middle_names ?? '',
    representative_last_name: props.customer?.information?.representative_last_name ?? '',
    representative_job_title: props.customer?.information?.representative_job_title ?? '',
    country_id: props.customer?.informationable_type === 'CustomerCompanyInformation'
      ? props.customer?.information?.country?.locked_id ?? null
      : null,
    timezone_id: props.customer?.informationable_type === 'CustomerCompanyInformation'
      ? props.customer?.information?.timezone?.locked_id ?? null
      : null,
    business_identification_type_id: props.customer?.information?.business_identification_type?.locked_id ?? null,
    business_identification_number: props.customer?.information?.business_identification_number ?? '',
    tax_type_id: props.customer?.information?.tax_type?.locked_id ?? null,
    tax_identification_number: props.customer?.information?.tax_identification_number ?? '',
    website_url: props.customer?.informationable_type === 'CustomerCompanyInformation'
      ? props.customer?.information?.website_url ?? ''
      : null,
  },
  contacts: [],
  addresses: [],
});

// Set the visibility of the sections based on the selected customer type..
watch(() => form.value.intro.informationable_type, (value) => {
  const personIndex = sections.value.findIndex((section) => section.name === 'personInformation');
  const companyIndex = sections.value.findIndex((section) => section.name === 'companyInformation');
  if (value === 'CustomerPersonInformation') {
    sections.value[personIndex].show = sections.value[personIndex].showInNav = true;
    sections.value[companyIndex].show = sections.value[companyIndex].showInNav = false;
  } else if (value === 'CustomerCompanyInformation') {
    sections.value[personIndex].show = sections.value[personIndex].showInNav = false;
    sections.value[companyIndex].show = sections.value[companyIndex].showInNav = true;
  }
});

const submit = () =>
  emits(
    'submit',
    form.value.transform((data) => {
      let payload = {};

      const enabledSections = sections.value.filter((section) => section.show).map((section) => section.name);

      if (enabledSections.includes('intro')) {
        payload = {
          ...payload,
          ...data.intro,
        };
      }

      if (enabledSections.includes('personInformation')) {
        payload = {
          ...payload,
          personInformation: data.personInformation,
        };
      }

      if (enabledSections.includes('companyInformation')) {
        payload = {
          ...payload,
          companyInformation: data.companyInformation,
        };
      }

      if (enabledSections.includes('addresses')) {
        payload = {
          ...payload,
          addresses: data.addresses,
        };
      }

      if (enabledSections.includes('contacts')) {
        payload = {
          ...payload,
          contacts: data.contacts,
          socials: data.socials,
        };
      }

      return payload;
    })
  );
</script>
