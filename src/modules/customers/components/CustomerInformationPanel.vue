<template>
  <VInformationPanel :loading="isCustomerLoading">
    <VDefaultField label="Name" :value="customerName" />
    <VDefaultField label="Type" :value="customerType" />
    <VTimeField label="Created At" :value="customer.created_at" />
    <VTimeField label="Updated At" :value="customer.updated_at" />
  </VInformationPanel>
</template>

<script setup>
import { computed } from 'vue';
import { useCustomer } from '@/modules/customers/api/get-customer';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const { data: customer, isLoading: isCustomerLoading } = useCustomer({
  customerId: props.id,
});

// Computed property to return the customer name if set.
const customerName = computed(() => {
  if (customer.value?.informationable_type === 'CustomerPersonInformation') {
    return customer.value.information.full_name;
  } else if (customer.value?.informationable_type === 'CustomerCompanyInformation') {
    return customer.value.information.name;
  } else {
    return 'Undefined';
  }
});

// Computed property to return the customer type.
const customerType = computed(() => {
  if (customer.value?.informationable_type === 'CustomerPersonInformation') {
    return 'Person (Customer)';
  } else if (customer.value?.informationable_type === 'CustomerCompanyInformation') {
    return 'Company (Customer)';
  }
  return null;
});

</script>
