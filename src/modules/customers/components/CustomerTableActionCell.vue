<template>
  <div class="flex items-center justify-end space-x-1.5">
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <VButton intent="table">
          <BIconThreeDots class="h-5 w-5" />
        </VButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem as-child>
          <RouterLink
            :to="{
              name: 'customers.view',
              params: { id: row.locked_id },
            }"
          >
            <BIconSearch />View
          </RouterLink>
        </DropdownMenuItem>
        <DropdownMenuItem as-child>
          <RouterLink
            :to="{ name: 'customers.activity', params: { id: row.locked_id } }"
            class="w-full"
          >
            <BIconActivity />Activity
          </RouterLink>
        </DropdownMenuItem>

        <DropdownMenuGroup>
          <DropdownMenuLabel>Modify</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem as-child>
            <RouterLink
              :to="{
                name: 'customers.update',
                params: { id: row.locked_id },
              }"
              class="w-full"
            >
              <BIconPencilSquare />Edit
            </RouterLink>
          </DropdownMenuItem>
          <DropdownMenuItem @click="openCustomerDeleteConfirmationDialog">
            <BIconTrash />Delete
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>

    <VConfirmModal
      v-model:open="isCustomerDeleteConfirmationDialog"
      @confirmed="deleteCustomerMutation.mutate({
        customerId: props.row.locked_id
      })"
      title="Confirm Customer Deletion"
      :description="`<p>You're about to delete the customer: <b>${customerName}</b>.</p><p>Please confirm you wish to proceed.</p>`"
      :icon="BIconTrashFill"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BIconSearch from '~icons/bi/search';
import BIconThreeDots from '~icons/bi/three-dots';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconActivity from '~icons/bi/activity';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/shadcn/dropdown-menu';
import { useDisclosure } from '@/hooks';
import { useDeleteCustomer } from '../api/delete-customer';

interface CustomerRow {
  locked_id: string;
  name: string;
  [key: string]: any;
}

const props = defineProps<{
  row: CustomerRow;
}>();

const customerName = computed(() => {
  if (props.row.informationable_type === 'CustomerPersonInformation') {
    return props.row?.information?.full_name || 'Undefined';
  } else if (props.row.informationable_type === 'CustomerCompanyInformation') {
    return props.row?.information?.name || 'Undefined';
  }
  return 'Undefined';
});

const deleteCustomerMutation = useDeleteCustomer({
  mutationConfig: {
    onSuccess: () => {
      Reshape.toast(
        {
          title: 'Customer Deleted',
          message: `The customer <b>${customerName.value}</b> was successfully deleted.`,
        },
        'success'
      );
    },
  },
});

const {
  isOpen: isCustomerDeleteConfirmationDialog,
  open: openCustomerDeleteConfirmationDialog
} = useDisclosure(false);
</script>
