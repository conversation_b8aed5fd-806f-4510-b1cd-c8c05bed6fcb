<template>
  <FormPanel>
    <template #default>
      <div class="grid grid-cols-6 gap-6">
        <VField
          label="Customer Type"
          class="col-span-6 xl:col-span-3"
          required
          :errors="form.errors.get('informationable_type')"
        >
          <CustomerTypeCombobox v-model="form.intro.informationable_type" />
        </VField>

        <TransitionFade>
          <div v-if="form.intro.informationable_type" class="col-span-6 grid grid-cols-6 gap-6">
            <VField
              label="Status"
              class="col-span-6 md:col-span-6 xl:col-span-2"
              required
              :errors="form.errors.get('status_id')"
            >
              <StatusCombobox
                v-model="form.intro.status_id"
                :statusable="form.intro.informationable_type"
                default-option="Active"
              />
            </VField>

            <VField
              label="Owning Entity"
              class="col-span-6 md:col-span-3 xl:col-span-1"
              required
              :errors="form.errors.get('owning_customerable_type')"
            >
              <EntityCombobox
                v-model="form.intro.owning_customerable_type"
                value-prop="name"
                :excluded-names="['Customer']"
              />
            </VField>

            <VField
              :label="owningCustomerableTypeLabel"
              class="col-span-6 md:col-span-3 xl:col-span-3"
              required
              :errors="form.errors.get('owning_customerable_id')"
            >
              <TransitionFade>
                <span
                  v-if="!form.intro.owning_customerable_type"
                  class="mt-2.5 inline-block select-none italic text-platinum-600 dark:text-midnight-200"
                >Select Entity to continue.</span>
                <UserCombobox
                  v-else-if="form.intro.owning_customerable_type === 'User'"
                  v-model="form.intro.owning_customerable_id"
                />
                <CompanyCombobox
                  v-else-if="form.intro.owning_customerable_type === 'Company'"
                  v-model="form.intro.owning_customerable_id"
                />
                <span
                  v-else
                  class="mt-2 inline-block select-none italic text-platinum-500 dark:text-midnight-100"
                >Selection Invalid</span>
              </TransitionFade>
            </VField>
          </div>
        </TransitionFade>
      </div>
    </template>
  </FormPanel>
</template>

<script setup>
import { ref, computed } from 'vue';
import StatusCombobox from '@/components/ui/form/composables/StatusCombobox.vue';
import FormPanel from '@/components/ui/forms/tabbed/FormPanel.vue';
import { useVModel } from '@/hooks/model';
import CustomerTypeCombobox from '@/components/ui/form/composables/CustomerTypeCombobox.vue';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
  customer: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(['update:modelValue']);

const form = useVModel(props, 'modelValue', emit, {
  passive: true,
  deep: true,
});

const owningCustomerableTypeLabel = computed(() => {
  return 'Owning ' + (form.value.intro.owning_customerable_type ? form.value.intro.owning_customerable_type + ' Reference' : 'Entity Reference');
});

</script>