<template>
  <FormPanel>
    <template #default>
      <div class="grid grid-cols-6 gap-6">

        <VField
          label="Company Name"
          class="col-span-6 md:col-span-3"
          required
          :errors="form.errors.get('companyInformation.name')"
        >
          <VInput
            v-model="form.companyInformation.name"
            placeholder="Enter Company Name"
            required
          />
        </VField>
        
        <VField
          label="Company Legal Name"
          class="col-span-6 md:col-span-3"
          required
          :errors="form.errors.get('companyInformation.legal_name')"
        >
          <VInput
            v-model="form.companyInformation.legal_name"
            placeholder="Enter Company Legal Name"
            required
          />
        </VField>

        <VField
          label="Company Structure"
          class="col-span-6 md:col-span-3 xl:col-span-2"
          :errors="form.errors.get('companyInformation.structure_type_id')"
          required
        >
          <CompanyStructureTypeCombobox v-model="form.companyInformation.structure_type_id" />
        </VField>

        <VField
          label="Company Industry"
          class="col-span-6 md:col-span-3 xl:col-span-2"
          :errors="form.errors.get('companyInformation.industry_id')"
          required
        >
          <IndustryCombobox v-model="form.companyInformation.industry_id" />
        </VField>

        <VField
          label="Customer Type"
          class="col-span-6 md:col-span-3 xl:col-span-2"
          :errors="form.errors.get('companyInformation.type_id')"
          required
        >
          <TypeCombobox
            v-model="form.companyInformation.type_id"
            typeable="CustomerCompanyInformation"
            :default-option="defaultCompanyCustomerType"
            required
          />
        </VField>

        <VField
          label="Representative Email Address"
          class="col-span-6"
          :class="!customer ? 'xl:col-span-4' : ''"
          required
          :errors="form.errors.get('companyInformation.representative_email')"
        >
          <VInput
            v-model="form.companyInformation.representative_email"
            type="email"
            placeholder="Enter Representative Email Address"
            required
          />
        </VField>

        <VField
          v-if="!customer"
          label="Customer Email Notification"
          class="col-span-6 md:col-span-3 xl:col-span-2"
          help="Send a notification to the new customer to let them know they have been added to the system."
          :errors="form.errors.get('companyInformation.email_confirmation')"
        >
          <VToggle v-model="form.personInformation.email_confirmation" class="mt-2 h-9">
            <span v-if="form.personInformation.email_confirmation" class="flex flex-col">
              <span>Send Email Notification</span>
            </span>
            <span v-else class="text-platinum-600 dark:text-midnight-200">No Email Notification</span>
          </VToggle>
        </VField>

        <VField
          label="Representative Honorific"
          class="col-span-6 md:col-span-3 xl:col-span-2"
          :errors="form.errors.get('companyInformation.representative_honorific_id')"
        >
          <HonorificCombobox v-model="form.companyInformation.representative_honorific_id" />
        </VField>

        <VField
          label="Representative First Name"
          class="col-span-6 md:col-span-3 xl:col-span-4"
          required
          :errors="form.errors.get('companyInformation.representative_first_name')"
        >
          <VInput
            v-model="form.companyInformation.representative_first_name"
            placeholder="Enter Representative First Name"
            required
          />
        </VField>

        <VField
          label="Representative Middle Name(s)"
          class="col-span-6 md:col-span-3"
          :errors="form.errors.get('companyInformation.representative_middle_names')"
        >
          <VInput
            v-model="form.companyInformation.representative_middle_names"
            placeholder="Enter Representative Middle Name(s)"
            required
          />
        </VField>

        <VField
          label="Representative Last Name"
          class="col-span-6 md:col-span-3"
          required
          :errors="form.errors.get('companyInformation.representative_last_name')"
        >
          <VInput
            v-model="form.companyInformation.representative_last_name"
            placeholder="Enter Representative Last Name"
            required
          />
        </VField>

        <VField
          label="Representative Job Title"
          class="col-span-6 md:col-span-3 xl:col-span-2"
          :errors="form.errors.get('companyInformation.representative_job_title')"
        >
          <VInput
            v-model="form.companyInformation.representative_job_title"
            placeholder="Enter Representative Job Title"
          />
        </VField>

        <VField
          label="Country Based"
          class="col-span-6 md:col-span-3 xl:col-span-2"
          :errors="form.errors.get('companyInformation.country_id')"
          required
        >
          <CountryCombobox
            v-model="form.companyInformation.country_id"
            required
          />
        </VField>

        <VField
          label="Timezone"
          class="col-span-6 md:col-span-3 xl:col-span-2"
          :errors="form.errors.get('companyInformation.timezone_id')"
        >
          <TimezoneCombobox
            v-model="form.companyInformation.timezone_id"
            placeholder="Select Local Timezone"
            value-prop="locked_id"
          />
        </VField>
        
        <VField
          label="Business Identification Type"
          class="col-span-6 md:col-span-3"
          :errors="form.errors.get('companyInformation.business_identification_type_id')"
        >
          <TypeCombobox
            v-model="form.companyInformation.business_identification_type_id"
            typeable="BusinessIdentification"
          />
        </VField>

        <VField
          label="Business Identification Number"
          class="col-span-6 md:col-span-3"
          :errors="form.errors.get('companyInformation.business_identification_number')"
        >
          <VInput
            v-model="form.companyInformation.business_identification_number"
            placeholder="Enter Business Identification Number"
          />
        </VField>

        <VField
          label="Tax Type"
          class="col-span-6 md:col-span-3"
          :errors="form.errors.get('companyInformation.tax_type_id')"
        >
          <TypeCombobox
            v-model="form.companyInformation.tax_type_id"
            typeable="CustomerCompanyTaxIdentification"
          />
        </VField>

        <VField
          label="Tax Identification Number"
          class="col-span-6 md:col-span-3"
          :errors="form.errors.get('companyInformation.tax_identification_number')"
        >
          <VInput
            v-model="form.companyInformation.tax_identification_number"
            placeholder="Enter Tax Identification Number"
          />
        </VField>

        <VField
          label="Website URL"
          class="col-span-6"
          :errors="form.errors.get('companyInformation.website_url')"
        >
          <VInput
            v-model="form.companyInformation.website_url"
            placeholder="Enter Website URL"
          />
        </VField>
      </div>
    </template>
    <template v-if="!customer" #footer>
      <p class="col-span-3">
        <BIconArrowUp class="-mt-0.5 mr-2 inline lg:hidden" />
        <BIconArrowLeft class="-mt-0.5 mr-2 hidden lg:inline" />
        Configure further customer values using the optional checkboxes using the menu or continue and add the customer immediately.
      </p>
    </template>
  </FormPanel>
</template>

<script setup>
import FormPanel from '@/components/ui/forms/tabbed/FormPanel.vue';
import { useVModel } from '@/hooks/model';
import BIconArrowLeft from '~icons/bi/arrow-left';
import BIconArrowUp from '~icons/bi/arrow-up';

import CompanyStructureTypeCombobox from '@/components/ui/form/composables/CompanyStructureTypeCombobox.vue';
import TypeCombobox from '@/components/ui/form/composables/TypeCombobox.vue';
import CountryCombobox from '@/components/ui/form/composables/CountryCombobox.vue';
import TimezoneCombobox from '@/components/ui/form/composables/TimezoneCombobox.vue';
import HonorificCombobox from '@/components/ui/form/composables/HonorificCombobox.vue';
import IndustryCombobox from '@/components/ui/form/composables/IndustryCombobox.vue';
const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
  customer: {
    type: Object,
    default: null,
  },
});

const defaultCompanyCustomerType = import.meta.env.VITE_DEFAULT_COMPANY_CUSTOMER_TYPE ?? '';

const emit = defineEmits(['update:modelValue']);

const form = useVModel(props, 'modelValue', emit, {
  passive: true,
  deep: true,
});
</script>
