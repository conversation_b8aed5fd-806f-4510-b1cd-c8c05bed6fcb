<template>
  <FormPanel>
    <template #default>
      <div class="grid grid-cols-6 gap-6">
        <VField
          label="Customer Email Address"
          class="col-span-6 "
          :class="!customer ? 'xl:col-span-4' : 'xl:col-span-6'"
          required
          :errors="form.errors.get('personInformation.email')"
        >
          <VInput
            v-model="form.personInformation.email"
            type="email"
            placeholder="Enter Email Address"
            required
          />
        </VField>

        <VField
          v-if="!customer"
          label="Customer Email Notification"
          class="col-span-6 md:col-span-3 xl:col-span-2"
          help="Send a notification to the new customer to let them know they have been added to the system."
          :errors="form.errors.get('personInformation.email_confirmation')"
          required
        >
          <VToggle v-model="form.personInformation.email_confirmation" class="mt-2 h-9">
            <TransitionFade>
              <span v-if="form.personInformation.email_confirmation" class="flex flex-col">Send Email Notification</span>
              <span v-else class="text-platinum-600 dark:text-midnight-200">No Email Notification</span>
            </TransitionFade>
          </VToggle>
        </VField>

        <VField
          label="Customer Type"
          class="col-span-6 md:col-span-3 2xl:col-span-2"
          :errors="form.errors.get('personInformation.type_id')"
          required
        >
          <TypeCombobox
            v-model="form.personInformation.type_id"
            typeable="CustomerPersonInformation"
            :default-option="defaultPersonCustomerType"
            required
          />
        </VField>

        <VField
          label="Honorific"
          class="col-span-6 md:col-span-3 2xl:col-span-1"
          :errors="form.errors.get('profile.honorific')"
        >
          <HonorificCombobox v-model="form.personInformation.honorific_id" />
        </VField>

        <VField
          label="First Name"
          class="col-span-6 md:col-span-3 xl:col-span-3"
          required
          :errors="form.errors.get('personInformation.first_name')"
        >
          <VInput
            v-model="form.personInformation.first_name"
            placeholder="Enter First Name"
            required
          />
        </VField>

        <VField
          label="Middle Name(s)"
          class="col-span-6 md:col-span-3"
          :errors="form.errors.get('personInformation.middle_names')"
        >
          <VInput
            v-model="form.personInformation.middle_names"
            placeholder="Enter Middle Name(s)"
            required
          />
        </VField>

        <VField
          label="Last Name"
          class="col-span-6 md:col-span-3"
          required
          :errors="form.errors.get('personInformation.last_name')"
        >
          <VInput
            v-model="form.personInformation.last_name"
            placeholder="Enter Last Name"
            required
          />
        </VField>

        <VField
          label="Country Based"
          class="col-span-6 md:col-span-3"
          :errors="form.errors.get('personInformation.country_id')"
          required
        >
          <CountryCombobox
            v-model="form.personInformation.country_id"
            required
          />
        </VField>

        <VField
          label="Timezone"
          class="col-span-6 md:col-span-3"
          :errors="form.errors.get('personInformation.timezone_id')"
        >
          <TimezoneCombobox
            v-model="form.personInformation.timezone_id"
            placeholder="Select Local Timezone"
            value-prop="locked_id"
          />
        </VField>

        <VField
          label="Website URL"
          class="col-span-6"
          :errors="form.errors.get('personInformation.website_url')"
        >
          <VInput
            v-model="form.personInformation.website_url"
            placeholder="Enter Website URL"
          />
        </VField>
      </div>
    </template>

    <template v-if="!customer" #footer>
      <p class="col-span-3">
        <BIconArrowUp class="-mt-0.5 mr-2 inline lg:hidden" />
        <BIconArrowLeft class="-mt-0.5 mr-2 hidden lg:inline" />
        Configure further customer values using the optional checkboxes using the menu or continue and add the customer immediately.
      </p>
    </template>
  </FormPanel>
</template>

<script setup>
import FormPanel from '@/components/ui/forms/tabbed/FormPanel.vue';
import { useVModel } from '@/hooks/model';
import BIconArrowLeft from '~icons/bi/arrow-left';
import BIconArrowUp from '~icons/bi/arrow-up';
import TimezoneCombobox from '@/components/ui/form/composables/TimezoneCombobox.vue';
import CountryCombobox from '@/components/ui/form/composables/CountryCombobox.vue';
import TypeCombobox from '@/components/ui/form/composables/TypeCombobox.vue';

const defaultPersonCustomerType = import.meta.env.VITE_DEFAULT_PERSON_CUSTOMER_TYPE ?? '';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
  customer: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(['update:modelValue']);

const form = useVModel(props, 'modelValue', emit, {
  passive: true,
  deep: true,
});
</script>
