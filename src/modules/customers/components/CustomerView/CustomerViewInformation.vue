
<template v-if="customer">
  <div class="grid gap-3 lg:grid-cols-5 items-start">
    <ViewPanel
      v-if="customer?.informationable_type === 'CustomerPersonInformation'"
      class="col-span-5 xl:col-span-4"
    >
      <VCardHeader>Person Information Record</VCardHeader>
      <PanelDefaultField label="Customer Subtype" :value="customer?.information?.type?.name" allowCopy />
      <PanelDefaultField label="Honorific" :value="customer?.information?.honorific?.name" allowCopy />
      <PanelDefaultField label="Full Name" :value="customer?.information?.full_name" allowCopy>
        <template #dropdown>
          <PanelDefaultField label="First Name" :value="customer?.information?.first_name" allowCopy />
          <PanelDefaultField label="Middle Name(s)" :value="customer?.information?.middle_names" allowCopy />
          <PanelDefaultField label="Last Name" :value="customer?.information?.last_name" allowCopy />
        </template>
      </PanelDefaultField>
      <PanelDefaultField label="Email" :value="customer?.information?.email" allowCopy>
        <ContactLink :value="customer?.information?.email" method="email" />
      </PanelDefaultField>
      <PanelCountryField
        label="Country Based"
        :value="customer?.information?.country?.name"
        :countryId="customer?.information?.country?.locked_id"
        :iso2="customer?.information?.country?.iso_alpha_2"
        allowCopy
      />
      <PanelDefaultField label="Timezone" :value="customer?.information?.timezone?.value" />
      <PanelDefaultField label="Website URL" :value="customer?.information?.website_url" allowCopy>
        <template v-if="customer?.information?.website_url" #default>
          <VTextLink
            :to="customer?.information?.website_url"
            external
          >{{ customer?.information?.website_url }}</VTextLink>
        </template>
      </PanelDefaultField>
      <PanelDateTimeField label="Date Created" :value="customer?.information?.created_at" />
      <PanelDateTimeField label="Date Updated" :value="customer?.information?.updated_at" />
    </ViewPanel>

    <template v-else-if="customer?.informationable_type === 'CustomerCompanyInformation'">
      <ViewPanel class="col-span-5 xl:col-span-4">
        <VCardHeader>Company Information Record</VCardHeader>
        <PanelDefaultField label="Company Name" :value="customer?.information?.name" allowCopy />
        <PanelDefaultField label="Legal Name" :value="customer?.information?.legal_name" allowCopy />
        <PanelDefaultField label="Customer Subtype" :value="customer?.information?.type?.name" allowCopy />
        <PanelDefaultField label="Industry" :value="customer?.information?.industry?.name" allowCopy />
        <PanelDefaultField label="Structure Type" :value="customer?.information?.structure_type?.name" allowCopy />
        <PanelCountryField
          label="Country Based"
          :value="customer?.information?.country?.name"
          :countryId="customer?.information?.country?.locked_id"
          :iso2="customer?.information?.country?.iso_alpha_2"
          allowCopy
        />
        <PanelDefaultField label="Timezone" :value="customer?.information?.timezone?.value" />
        <PanelDefaultField label="Website URL" :value="customer?.information?.website_url" allowCopy>
          <template #default>
            <VTextLink
              :to="customer?.information?.website_url"
              external
            >{{ customer?.information?.website_url }}</VTextLink>
          </template>
        </PanelDefaultField>
        <PanelDefaultField label="Business Identification Type" :value="customer?.information?.business_identification_type?.name" allowCopy />
        <PanelDefaultField label="Business Identification Number" :value="customer?.information?.business_identification_number" allowCopy />
        <PanelDefaultField label="Tax Identification Type" :value="customer?.information?.tax_type?.name" allowCopy />
        <PanelDefaultField label="Tax Identification Number" :value="customer?.information?.tax_identification_number" allowCopy />
        <PanelDateTimeField label="Date Created" :value="customer?.information?.created_at" />
        <PanelDateTimeField label="Date Updated" :value="customer?.information?.updated_at" />
      </ViewPanel>

      <ViewPanel class="col-span-5 xl:col-span-4">
        <VCardHeader>Representative Information</VCardHeader>
        <PanelDefaultField label="Representative Honorific" :value="customer?.information?.representative_honorific?.name" allowCopy />
        <PanelDefaultField label="Representative Full Name" :value="customer?.information?.representative_full_name" allowCopy>
          <template #dropdown>
            <PanelDefaultField label="First Name" :value="customer?.information?.representative_first_name" allowCopy />
            <PanelDefaultField label="Middle Name(s)" :value="customer?.information?.representative_middle_names" allowCopy />
            <PanelDefaultField label="Last Name" :value="customer?.information?.representative_last_name" allowCopy />
          </template>
        </PanelDefaultField>
        <PanelDefaultField label="Representative Email" :value="customer?.information?.representative_email" allowCopy>
          <ContactLink :value="customer?.information?.representative_email" method="email" />
        </PanelDefaultField>
        <PanelDefaultField label="Representative Job Title" :value="customer?.information?.representative_job_title" allowCopy />
      </ViewPanel>
    </template>
  </div>
</template>

<script setup>
import ContactLink from '@/components/ui/links/ContactLink.vue';

const props = defineProps({
  customer: {
    type: Object,
    required: false,
  },
});

</script>