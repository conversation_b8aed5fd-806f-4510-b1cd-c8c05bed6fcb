<template>
  <VBadge v-if="status" :color="statusColor" class="uppercase">
    {{ status }}
  </VBadge>
  <template v-else>
    <span class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
  </template>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  status: {
    type: String,
  },
});

const statusColor = computed(() => {
  switch (props.status.toLowerCase()) {
    case 'active':
      return 'success';
    case 'inactive':
      return 'danger';
    case 'pending approval':
      return 'warning';
    default:
      return 'info';
  }
});
</script>