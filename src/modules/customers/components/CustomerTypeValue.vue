<template>
  <template v-if="customerTypeString">
    {{ customerTypeString }}
  </template>
  <template v-else>
    <span class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
  </template>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  value: {
    type: String,
    default: null,
  },
});

const customerTypeString = computed(() => {
  switch (props.value) {
    case 'CustomerPersonInformation':
      return 'Person';
    case 'CustomerCompanyInformation':
      return 'Company';
    default:
      return false;
  }
});
</script>