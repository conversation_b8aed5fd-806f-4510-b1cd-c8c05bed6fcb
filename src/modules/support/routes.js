import UserLayout from '@/layouts/UserLayout.vue';
import { authAttempt } from '@/router/middleware';

const routes = [
  {
    path: '/support',
    component: UserLayout,
    meta: {
      middleware: authAttempt,
    },
    children: [
      {
        path: '',
        name: 'support.index',
        component: () => import('./pages/SupportIndex.vue'),
        meta: {
          title: 'Support',
          headerTitle: 'Support',
          headerDescription: 'Get the support and answers you need.',
          breadcrumbs: [
            { label: 'Support', name: 'support.index' },
          ],
        },
      },
      {
        path: 'faq',
        name: 'support.faq',
        component: () => import('@/modules/support/pages/Faq.vue'),
        meta: {
          title: 'FAQ',
          headerTitle: 'FAQ',
          headerDescription: 'Answers to the most frequently asked questions.',
          breadcrumbs: [
            { label: 'Support', name: 'support.index' },
            { label: 'FAQ', name: 'support.faq' },
          ],
        },
      },
      {
        path: 'contact',
        name: 'support.contact',
        component: () => import('./pages/Contact.vue'),
        meta: {
          title: 'Contact',
          headerTitle: 'Contact Us',
          headerDescription: 'Get in contact with our dedicated support team.',
          breadcrumbs: [
            { label: 'Support', name: 'support.index' },
            { label: 'Contact Us', name: 'support.contact' },
          ],
          middleware: [authAttempt],
        },
      },
    ],
  },
];

export default routes;
