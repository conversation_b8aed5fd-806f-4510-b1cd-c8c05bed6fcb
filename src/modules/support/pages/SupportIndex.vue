<template>
  <div>
    <VPageHeader v-if="auth?.authenticated" />
    <VContainer class="py-2 lg:py-6 space-y-6">
      <div class="text-center py-10">
        <h1 class="text-3xl lg:text-4xl font-bold text-primary-500 mb-3">{{ randomSupportTitle() }}</h1>
        <p class="text-platinum-950 dark:text-midnight-50 border-t border-platinum-300 dark:border-midnight-500 pt-1.5 mt-1.5 inline">
          Select your preferred support option below.
        </p>
      </div>
      <ActionsPanel>
        <ActionsPanelLink
          v-for="(action, index) in supportActions"
          :key="action.name"
          :action="action"
          :total-length="supportActions.length"
          :index="index"
        />
      </ActionsPanel>
    </VContainer>
  </div>
</template>

<script setup>
import ActionsPanel from '@/components/ui/panels/ActionsPanel.vue';
import ActionsPanelLink from '@/components/ui/panels/actions/ActionsPanelLink.vue';

import BIconQuestionCircleFill from '~icons/bi/question-circle-fill';
import BIconChatDotsFill from '~icons/bi/chat-dots-fill';
import BIconBookFill from '~icons/bi/book-fill';

const supportActions = [
  {
    icon: BIconQuestionCircleFill,
    name: 'FAQ',
    description: 'Browse frequently asked questions and find quick answers to common issues.',
    to: { name: 'support.faq' },
    iconForeground: 'text-primary-500',
    iconBackground: 'bg-blue-100 dark:bg-blue-950'
  },
  {
    icon: BIconChatDotsFill,
    name: 'Contact Us',
    description: 'Get in touch with our support team for personalized assistance.',
    to: { name: 'support.contact' },
    iconForeground: 'text-primary-500',
    iconBackground: 'bg-green-100 dark:bg-green-950'
  },
];

function randomSupportTitle() {
  // Return a random greeting
  var supportTitle = [
    'Welcome to Support',
    'Need a hand?',
    'How can we help?',
    'Support is just a click away',
    'Get the support you need',
    'Your support hub',
    'Get the support you need',
    'What can we do for you?',
    'Need some help?',
    'Do you require assistance?'
  ];
  return supportTitle[Math.floor(Math.random() * supportTitle.length)];
}

</script>
