<template>
  <div>
    <VPageHeader
      v-if="auth?.authenticated"
      :showTitle="false"
      :showDescription="false"
    />
    <VContainer class="py-2 lg:py-6">
      <div class="text-center">
        <h1 class="text-3xl lg:text-4xl font-bold text-primary-500 mb-3">Frequently Asked Questions</h1>
        <p class="text-platinum-950 dark:text-midnight-50 border-t border-platinum-300 dark:border-midnight-500 pt-1.5 mt-1.5 inline ">
          Your <span class='text-primary-500 font-semibold'>{{ appTitle }}</span> Questions, Answered.
        </p>
      </div>
      <div class="relative max-w-md m-auto my-6">
        <VInput
          v-model="searchQuery"
          size="lg"
          placeholder="Looking for something specific?"
          clearable
        />
      </div>
      <div class="space-y-6">
        <div class="space-y-6">
          <div v-for="(section, sectionIndex) in filteredFaqSections" :key="sectionIndex" class="space-y-3">
            <div>
              <h1 class="text-2xl text-primary-500 font-bold">{{ section.title }}</h1>
              <p class="text-sm text-platinum-950 dark:text-midnight-50">{{ section.description }}</p>
            </div>
            <accordionContainer :opened="globalOpenedState" @change="handleAccordionChange">
              <accordion
                v-for="(question, questionIndex) in section.questions"
                :key="`${sectionIndex}-${questionIndex}`"
                :id="getQuestionId(sectionIndex, questionIndex)"
              >
                <template #header>
                  <h3 class="p-2">{{ question.question }}</h3>
                </template>
                <template #body>
                  <div class="p-2">
                    <MarkdownField :value="question.answer" />
                  </div>
                </template>
              </accordion>
            </accordionContainer>
          </div>
        </div>
        <p class="text-sm text-center text-platinum-950 dark:text-midnight-50">
          Still need help?
          <VTextLink
            :to="{ name: 'support.contact', query: { topic: 'general-enquiry' } }"
          >Get in touch!</VTextLink>
        </p>
      </div>
    </VContainer>
  </div>
</template>

<script setup>
import accordion from '@/components/ui/accordions/Accordion/Accordion.vue';
import accordionContainer from '@/components/ui/accordions/Accordion/AccordionContainer.vue';
import { useAuth } from '@/store';
import MarkdownField from '@/components/ui/fields/MarkdownField.vue';
import { ref, computed } from 'vue';

const appTitle = import.meta.env.VITE_APP_TITLE || 'reShape';
const auth = useAuth();
const searchQuery = ref('');

const faqSections = [
  {
    title: 'General',
    description: 'General questions about the platform.',
    questions: [
      {
        question: 'What is reShape?',
        answer: 'reShape is management suite intended to be a flexible starting point for a wide variety of web applications dealing with large amounts of customer data.\n\nBy providing a solid foundation, you can focus on building your application rather than reinventing the wheel.',
      },
      {
        question: 'What kind of applications is reShape suitable for?',
        answer: 'At the heart of it, reShape is primarily a customer relationship management (CRM) suite, capable of managing large amounts of customer data and associated metadata. \n\nThis makes it suitable for a wide variety of applications, from simple customer management to complex subscription-based services.',
      },
      {
        question: 'What kind of technology is used under the hood?',
        answer: 'The reShape project is built using a variety of technologies, but the fundamental tech stack consists of ==Vue 3==, ==Vite== and ==Tailwind CSS== on the frontend.\n\nThe backend serving the frontend consists of a PHP ==Laravel== API and ==PostgreSQL== database.\n\nMany of the features showcased within in the demo are easily extendable or removable, allowing for a great deal of flexibility and customisation to support a wide variety of use cases.',
      },
      {
        question: 'What features are available?',
        answer: 'This project is under active development and features are being added and updated on a regular basis.\n\nThe following is a simple list of features offered:\n\n| Feature | Description |\n| --- | --- |\n| User Management | Manage the full lifecycle of users, including creation, updates & profile management. |\n| Advanced Authentication | Secure and flexible authentication system supporting email-based registration, one-click features and session management. |\n| Company Management | Create and manage companies, including user & customer assignment. |\n| Customer Management | Maintain customer records, categorized as either individuals or companies but easily extendable. |\n| Subscriptions | Enable users or companies to subscribe to plans via Stripe. Supports custom pricing, discounts, and flexible billing intervals. |\n| Subscription Invoices | Complete billing history with downloadable PDF invoices for past subscriptions. |\n| Announcements | Create and distribute announcements targeted to individual users, companies, or globally, with permission-based visibility controls. |\n| Abilities & Roles | A permissions framework where roles encapsulate specific abilities, and can be assigned to users or companies to control access. |\n| Themes | Customize the interface with support for light and dark modes, brand colors, and theming on a per-user or per-company basis. |\n| Addresses | Centralized address management for users, companies, and customers with intuitive filtering and access control. |\n| Contacts | Store and manage contact methods including phone numbers, email, and social profiles. Integrated with user and company profiles. |\n| Countries | Manage a list of countries for standardized use across forms and records in the application. |\n| Currencies | Define and manage available currencies for potential use for transactions and financial records. |\n| Dashboard | A personalized landing page post-login, displaying welcome messages, contextual actions, and recent announcements. |\n| Contact Us | A general support system for contacting a support team including basic context and rate limiting. |\n| Activity | Logs all key data events across the platform, providing visibility into user and system activity for auditing and support. |\n| Comments | Add, edit, and manage comments on various entities like profiles and announcements. Includes moderation tools and reaction features. |\n| Industries | Maintain a structured list of industries to better categorize companies and customers. |\n| Notifications | Real-time notifications triggered by important events, delivered efficiently using WebSockets. |\n| Smooth SPA Experience | Fully single-page application (SPA) architecture offering seamless, fast interactions without full page reloads. |\n| Quick Overviews | Hover-based previews give privileged users instant access to key record details, reducing unnecessary navigation. |\n| Optimized Index Pages | Every major resource includes high-performance index pages with pagination, sorting, and searching capabilities. |\n| Security by Obscurity | Enhanced API security by default with obfuscated, hashed identifiers and minimal exposed data. |\n| Demo Pages | A handful of public pages for guest users including homepage, about, FAQ, contact, and legal pages to get you started. |'
      }
    ],
  },
  {
    title: 'Account Management',
    description: 'Questions related to user accounts, companies & customers.',
    questions: [
      {
        question: 'How do I create a user?',
        answer: 'New users can register themselves via the \'Get Started\' link at the top right of the navigation.\n\nPrivileged users can also create users on behalf of others. To create a user, navigate to the \'Users\' section of the navigation, then select the \'Add User\' action button.',
      },
      {
        question: 'How do I create a company?',
        answer: 'Companies can be created by privileged users. To create a company, navigate to the \'Companies\' section of the navigation, then select the \'Add Company\' action button.',
      },
      {
        question: 'How do I create a customer?',
        answer: 'Customers can be created by privileged users. To create a customer, navigate to the \'Customers\' section of the navigation, then select the \'Add Customer\' action button. Customers can be either individual people or companies. Customers are generally assigned to companies but can also be assigned to users.',
      },
      {
        question: 'How do I change my password?',
        answer: 'If you are not able to login, please use the \'Forgot Password\' link on the login page to reset your password. \n\nTo change your password once logged in, navigate to your profile by clicking on your avatar in the top right of the navigation, go to \'My Profile\' then select the \'Change Password\' action under the \'Profile Actions\' dropdown button.',
      }
    ],
  },
  {
    title: 'Subscriptions',
    description: 'Questions related to subscriptions.',
    questions: [
      {
        question: 'What is a subscription?',
        answer: 'A subscription is a recurring payment for the service. Subscriptions are available (by default) for users or companies and provide additional features and access to the service. All payments are processed via our payment partner, Stripe.',
      },
      {
        question: 'How do I subscribe to a plan?',
        answer: 'To subscribe to a plan, navigate to the relevant user or company profile and select \'Subscription\' under the \'Profile Actions\' dropdown button. Simply pick your preferred plan and click \'Subscribe\'. You will be redirected to the Stripe checkout page to complete your subscription.',
      },
    ],
  },
];

// Computed property to filter FAQ sections.
const filteredFaqSections = computed(() => {
  if (!searchQuery.value.trim()) {
    return faqSections;
  }

  const query = searchQuery.value.toLowerCase().trim();

  return faqSections
    .map(section => ({
      ...section,
      questions: section.questions.filter(question =>
        question.question.toLowerCase().includes(query) ||
        question.answer.toLowerCase().includes(query)
      )
    }))
    .filter(section => section.questions.length > 0);
});

// Helper function to generate unique IDs for each question.
const getQuestionId = (sectionIndex, questionIndex) => {
  return `section-${sectionIndex}-question-${questionIndex}`;
};

// Global state to track which accordion is open across all sections.
const globalOpenedState = ref([getQuestionId(0, 0)]); // Default first question.

// We want to ensure only one is open across all sections.
const handleAccordionChange = (newOpenedIds) => {
  globalOpenedState.value = newOpenedIds;
};

</script>