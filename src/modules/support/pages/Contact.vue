<template>
  <div class="h-full flex flex-col">
    <VPageHeader
      v-if="auth?.authenticated"
      class="z-30"
    />

    <div class="absolute top-0 left-0 h-full w-full">
      <div class="w-full h-full absolute inset-0 left-0 z-10">
        <div class="absolute inset-0 bg-gradient-to-b from-platinum-200 dark:from-midnight-900 to-transparent">
          <VImage class="w-full h-full object-cover -z-10"/> <!-- Empty image, uses default placeholder -->
        </div>
      </div>
    </div>

    <VContainer class="py-2 lg:py-6 w-full my-auto flex items-center justify-center">

      <TransitionFade>
        <VSpinner
          v-if="isSubmitting"
          size="xl"
          class="text-center p-3 z-20"
          :contactUsMessage="false"
        />
        <div v-else-if="!submitted" class="w-full z-20 bg-platinum-200 dark:bg-midnight-700/90 p-6 rounded-md shadow shadow-platinum-500 dark:shadow-midnight-900">
          <form
            id="contact-form"
            class="w-full space-y-6"
            @submit.prevent="submit"
          >

            <div class="divide-y divide-platinum-300 dark:divide-midnight-500">
              <div class="pb-3">
                <h2 class="text-3xl font-bold text-primary-500">{{ contactTitle }}</h2>
                <p class="text-platinum-950 dark:text-midnight-50">We aim to answer all enquiries within 24 hours. For a faster response, consider subscribing to a plan including priority support.</p>
              </div>
              <div class="pt-6 grid grid-cols-6 gap-6">
                <VField
                  v-if="!auth.authenticated"
                  class="col-span-6 md:col-span-4"
                  label="Full Name"
                  required
                  :errors="form.errors.get('full_name')"
                >
                  <VInput
                    v-model="form.full_name"
                    placeholder="Enter Full Name"
                    tabindex="1"
                    required
                  />
                </VField>
                <VField
                  label="Email Address"
                  required
                  :errors="form.errors.get('email')"
                  class="col-span-6 order-3"
                >
                  <VInput
                    v-model="form.email"
                    type="email"
                    placeholder="Enter Email Address"
                    tabindex="3"
                    :required="!auth.authenticated"
                  />
                </VField>
                <VField
                  label="Support Topic"
                  required
                  :errors="form.errors.get('type_id')"
                  class="order-2 col-span-6"
                  :class="auth.authenticated ? '' : 'md:col-span-2'"
                >
                  <TypeCombobox
                    v-model="form.type_id"
                    typeable="SupportContact"
                    placeholder="Select Support Topic"
                    tabindex="2"
                    :defaultOption="queryTopic"
                    required
                  />
                </VField>
                <VField
                  label="Message"
                  required
                  :errors="form.errors.get('message')"
                  class="col-span-6 order-last"
                >
                  <VTextarea
                    v-model="form.message"
                    rows="3"
                    placeholder="How can we help?"
                    resize
                    :maxRowsVisible="8"
                    tabindex="4"
                    required
                  />
                </VField>
              </div>
            </div>

            <transition name="button-pop">
              <div class="flex justify-center">
                <VButton
                  type="submit"
                  intent="primary"
                  :disabled="isSubmitting"
                  :class="!sm ? 'w-full' : ''"
                  :centered="!sm"
                  tabindex="5"
                >
                  <template #start>
                    <BIconSendCheck />
                  </template>
                  Submit Enquiry
                </VButton>
              </div>
            </transition>
          </form>
        </div>
        <VCard
          v-else
          class="w-full max-w-2xl z-20"
        >
          <VCardBody>
            <div class="divide-y divide-platinum-300 dark:divide-midnight-300 space-y-3">
              <h2 class="text-2xl font-bold text-primary-600 dark:text-primary-500 flex items-center gap-3">
                <BIconEnvelopeHeart /> Enquiry Sent!</h2>
              <div class="pt-3">
                <p class="text-platinum-950 dark:text-midnight-50">Thanks for reaching out. We'll review and reply to your enquiry as soon as we can.</p>
                <p class="text-platinum-950 dark:text-midnight-50">You should receive a confirmation email shortly.</p>
              </div>
            </div>
          </VCardBody>
          <VCardFooter>
            <VButton
              as="router-link"
              :to="{ name: 'index' }"
              centered
              class="w-full"
              intent="primary"
            >
              <component
                :is="auth?.authenticated ? BIconSpeedometer : BIconHouseFill"
                class="mr-2"
              />
              Return {{ auth?.authenticated ? 'to Dashboard' : 'to Home' }}
            </VButton>
          </VCardFooter>
        </VCard>
      </TransitionFade>
    </VContainer>
  </div>
</template>

<script setup>
import { useForm } from '@/hooks/form';
import { useAuth } from '@/store';
import { ref, watch, onMounted, computed } from 'vue';
import { axios } from '@/lib/axios';
import { ucwords } from '@/util/textUtils';
import BIconSendCheck from '~icons/bi/send-check';
import BIconEnvelopeHeart from '~icons/bi/envelope-heart';
import BIconSpeedometer from '~icons/bi/speedometer';
import BIconHouseFill from '~icons/bi/house-fill';
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core';
import { useRoute } from 'vue-router';

const breakpoints = useBreakpoints(breakpointsTailwind);
const auth = useAuth();
const submitted = ref(false);
const isSubmitting = ref(false);
const route = useRoute();

const form = useForm({
  full_name: '',
  email: auth.user?.email ?? '',
  type_id: '',
  message: '',
}, { resetOnSuccess: false });

watch(
  () => auth.authenticated,
  (val) => {
    if (val) {
      form.value.email = auth.user?.email ?? '';
    } else {
      form.value.email = '';
    }
  }
);

const submit = async () => {
  isSubmitting.value = true;
  try {
    await form.value.submit(async (formData) => {
      // Remove full_name if empty or only whitespace
      if (!formData.full_name || !formData.full_name.trim()) {
        delete formData.full_name;
      }
      const response = await axios.post('/api/contactus', formData);
      if (response?.data?.success) {
        Reshape.toast({
          title: 'Enquiry Sent',
          message: 'Thank you. You should receive a confirmation email shortly.',
        }, 'success');
        form.value.reset();
        submitted.value = true;
      } else {
        Reshape.toast({
          title: 'Message Failed',
          message: 'Unable to send your message. Please try again later.',
        }, 'danger');
      }
    });
  } catch (error) {
    Reshape.toast({
      title: 'Message Failed',
      message:
        error?.response?.data?.message ||
        'Unable to send your message. Please try again later.',
    }, 'danger');
  } finally {
    isSubmitting.value = false;
  }
};

const contactTitle = ref('');

function randomContactUsString() {
  const strings = [
    "Reach out to us",
    "Get in touch",
    "Send us a message",
    "Drop us a line",
    "How can we help?",
    "Need something?",
    "Start a conversation!",
    "Connect with our team",
    "Need a hand?",
    "Let's talk",
  ];
  return strings[Math.floor(Math.random() * strings.length)];
}

onMounted(() => {
  contactTitle.value = randomContactUsString();
});


const sm = breakpoints.greaterOrEqual('sm');

const queryTopic = computed(() => {
  const topic = route.query.topic || false;
  return topic ? ucwords(topic.replace(/-/g, ' ')) : false;
});

</script>