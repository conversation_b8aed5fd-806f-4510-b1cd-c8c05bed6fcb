import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { Role } from '@/types/api';

export const createRole = ({ data }: { data: CreateRolePayload }) => {
  return Reshape.http().post(`/api/roles`, data);
};

type CreateRolePayload = Omit<Role, 'id' | 'locked_id'>;

type UseCreateRoleOptions = {
  mutationConfig?: MutationConfig<typeof createRole>;
};

export const useCreateRole = ({ mutationConfig }: UseCreateRoleOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ['roles'],
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createRole,
  });
};