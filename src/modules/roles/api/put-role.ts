import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getRoleQueryOptions } from './get-role';

import { Role } from '@/types/api';

export const updateRole = ({ roleId, data }: { roleId: string, data: UpdateRolePayload }) => {
  return Reshape.http().put(`/api/roles/${roleId}`, data);
};

type UpdateRolePayload = Partial<Omit<Role, 'locked_id'>>;

type UseUpdateRoleOptions = {
  roleId: string;
  mutationConfig?: MutationConfig<typeof updateRole>;
};

export const useUpdateRole = ({ roleId, mutationConfig }: UseUpdateRoleOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['roles', args[1].roleId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getRoleQueryOptions(args[1].roleId).queryKey,
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: updateRole,
  });
};