<template>
  <div>
    <VPageHeader title="Companies" description="Table overview of all related relevant companies.">
      <VButton
        v-if="canCreate"
        as="router-link"
        intent="secondary"
        :to="{ name: 'companies.create' }"
        centered
      >
        <template #start>
          <BIconBuildingFillAdd />
        </template>
        Add Company
      </VButton>
    </VPageHeader>

    <VContainer class="py-2 lg:py-6">
      <CompaniesTable
        title="All Companies"
        :records="data?.data"
        :total-records="data?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
      />
    </VContainer>
  </div>
</template>

<script setup>
import CompaniesTable from '@/modules/companies/components/CompaniesTable.vue';
import { useCompanies } from '@/modules/companies/api/get-companies';
import { useFilterParams } from '@/hooks/query';
import { ref } from 'vue';
import BIconBuildingFillAdd from '~icons/bi/building-fill-add';

const canCreate = ref(true); // TODO: Update with permission logic.

const { params, search, sort, paginate } = useFilterParams();

const { data, isLoading } = useCompanies({
  params: params.value
});
</script>
