<template>
  <div>
    <VPageHeader />
      <template #actions>
        <VButton
          intent="secondary"
          as="router-link"
          :to="{ name: 'companies.subscription.index', params: { id: props.id } }"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Company Subscription
        </VButton>
      </template>
    </VPageHeader>
    <VContainer class="my-6 space-y-6">
      <!-- DEMO ONLY - Needs replacing with invoice table -->
      <SubscriptionTierTable
        title="Subscription Invoices"
        :records="subscriptionTiers?.data"
        :total-records="subscriptionTiers?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
      />
    </VContainer>
  </div>
</template>

<script setup lang="ts">
import { useFilterParams } from '@/hooks/query';
import SubscriptionTierTable from '@/modules/subscriptions/components/tiers/SubscriptionTierTable.vue';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import { useSubscriptionTiers } from '@/modules/subscriptions/api/tiers/get-subscription-tiers';

const props = defineProps({
  id: {
    type: String,
    default: null,
  },
});

const { params, search, sort, paginate } = useFilterParams();

const {
  data: subscriptionTiers,
  isLoading,
} = useSubscriptionTiers({
  params: params.value,
});
</script>