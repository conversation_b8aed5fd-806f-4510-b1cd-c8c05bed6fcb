<template>
  <div>
    <VPageHeader return-action>
      <template #actions>
        <VButton
          intent="secondary"
          as="router-link"
          :to="{ name: 'companies.subscription.invoices', params: { id: props.id } }"
          :disabled="!hasInvoices"
        >
          <template #start>
            <BIconFileEarmarkMedicalFill />
          </template>
          View Company Invoices
        </VButton>
      </template>
    </VPageHeader>
    <VContainer class="mt-3 py-3">
      <SubscriptionTierOptions
        tierableType="Company"
        :tierableId="props.id"
      >
        <template #default>
          <div class="space-y-2 text-platinum-950 dark:text-midnight-50">
            <h1 class="text-3xl font-bold text-primary-500">Ready to get started?</h1>
            <p>Your organisation doesn't seem to have a subscription just yet. Pick the plan that's right for you below!</p>
            <p>
              Got a query or have more specific needs?
              <VTextLink
                :to="{ name: 'support.contact', query: { topic: 'sales-enquiry' } }"
                disable-icon
              >
                Get in contact
              </VTextLink>
              and we'll be happy to help!
            </p>
          </div>
        </template>
      </SubscriptionTierOptions>
    </VContainer>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import BIconFileEarmarkMedicalFill from '~icons/bi/file-earmark-medical-fill';
import SubscriptionTierOptions from '@/modules/subscriptions/components/tiers/SubscriptionTierOptions.vue';

const props = defineProps({
  id: {
    type: String,
    default: null,
    required: true,
  }
});

const hasInvoices = ref(false);

</script>

