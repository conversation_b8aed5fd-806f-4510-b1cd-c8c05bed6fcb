<template>
  <div>
    <VPageHeader return-action />
    <VContainer class="py-2 lg:py-6">
      <UsersTable
        :columns="columns"
        title="Company Users"
        :records="users?.data"
        :total-records="users?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
      />
    </VContainer>
  </div>
</template>

<script setup>
import UsersTable from '@/modules/users/components/UsersTable.vue';
import { useCompanyUsers } from '@/modules/companies/api/get-company-users';
import { useFilterParams } from '@/hooks/query';
import { ref, h } from 'vue';
import EntityLink from '@/components/ui/links/EntityLink.vue';
import UserStatusCell from '@/modules/users/components/UserStatusBadge.vue';
import CompanyMembershipStatusCell from '@/modules/companies/components/CompanyMembershipStatusBadge.vue';
import ActionCell from '@/modules/users/components/UsersTableActionCell.vue';
import BooleanField from '@/components/ui/fields/BooleanField.vue';
import BIconArrowLeft from '~icons/bi/arrow-left';

const props = defineProps({
  id: {
    type: String,
    default: null,
  },
});

const { params, search, sort, paginate } = useFilterParams();

const { data: users, isLoading } = useCompanyUsers({
  params: params.value,
  id: props.id,
});

const columns = [
  {
    accessorKey: 'profile.full_name',
    header: 'Name',
    cell: (info) => {
      return h(EntityLink, {
        value: info.row.original.profile?.full_name,
        entityType: 'User',
        entityId: info.row.original.locked_id,
        undefinedText: 'No Profile Available',
        showAvatar: true,
        avatarSize: 'sm',
      });
    }
  },
  {
    accessorKey: 'company_memberships.position',
    header: 'Position',
    // enableSorting: false,
    cell: (info) => info.row.original.company_membership?.position?.name,
  },
  {
    accessorKey: 'is_primary',
    header: 'Primary',
    cell: (info) => h(BooleanField, { value: info.row.original.company_membership?.is_primary }),
  },
  {
    accessorKey: 'membership_status',
    header: 'Membership Status',
    cell: (info) => h(CompanyMembershipStatusCell, { 
      status: info.row.original.company_membership?.status?.name,
      created_at: info.row.original.company_membership?.created_at,
    }),
  },
  {
    accessorKey: 'status',
    header: 'User Status',
    cell: (info) => h(UserStatusCell, { status: info.row.original.status.name }),
  },
  {
    id: 'actions',
    header: () => h('span', { class: 'w-full flex justify-end' }),
    cell: (info) => h(ActionCell, { row: info.row.original }),
    enableResizing: false,
    size: 50,
  },
];

</script>
