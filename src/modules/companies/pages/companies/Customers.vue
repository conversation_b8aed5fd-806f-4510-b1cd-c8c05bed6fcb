<template>
  <div>
    <VPageHeader return-action />
    <VContainer class="py-2 lg:py-6">
      <CustomerTable
        title="Related Customers"
        :records="customers?.data"
        :total-records="customers?.meta?.total"
        :loading="isLoading"
        :per-page="params.per_page"
        @sort="sort"
        @paginate="paginate"
        @search="search"
      />
    </VContainer>
  </div>
</template>

<script setup>
import CustomerTable from '@customers/components/CustomerTable.vue';
import { useCompanyCustomers } from '@/modules/companies/api/get-company-customers';
import { useFilterParams } from '@/hooks/query';

const props = defineProps({
  id: {
    type: String,
    default: null,
  },
});

const { params, search, sort, paginate } = useFilterParams();

const { data: customers, isLoading } = useCompanyCustomers({
  params: params.value,
  id: props.id,
});
</script>
