<template>
  <div>
    <BackgroundBanner :show="showBanner" :src="banner" />
    <TransitionFade>
      <VContainer v-if="isCompanyLoading || company?.information" class="relative z-20 py-2 lg:py-3 space-y-3">
        <div class="flex flex-col md:flex-row gap-3">
          <ProfileHeader
            class="grow"
            :company="company || null"
            :socials="company?.socials || []"
          />
          <ProfileActions
            class="w-full md:max-w-[300px]"
            :company="company || null"
          />
        </div>
        <TransitionFade>
          <div
            v-if="company"
            class="grid grid-cols-1 lg:grid-cols-12 gap-y-3 lg:gap-x-3 lg:col-span-2"
          >
            <div class="col-span-8 space-y-3">
              <CompanyInfoButtonContainer :company="company" />
              <CommentPoster
                commentable-type="Company"
                :commentable-id="companyId"
              />
              <CommentFeed
                commentable-type="Company"
                :commentable-id="companyId"
                :can-react="true"
              >
                <template #empty>
                  <span class="text-sm text-midnight-100">
                    Comments will appear here. Be the first to comment on the {{ company?.name }} company profile!
                  </span>
                </template>
              </CommentFeed>
            </div>
            <div class="col-span-4 space-y-3">
              <ActivityFeed 
                activitable-type="Company"
                :activitable-id="companyId"
                title="Recent Company User Actions"
              />
              <ProfileStats :company="company" class="col-span-3" />
            </div>
          </div>
        </TransitionFade>
      </VContainer>
      <VContainer v-else class="relative z-20 py-2 md:py-3 flex items-center justify-center w-full h-full">
        <div class="flex-col text-center">
          <VEmptyState
            title="Company Not Found"
            description="The requested company does not exist or has been deleted."
          />
          <VButton
            as="router-link"
            intent="primary"
            :to="previousRoute ? { name: previousRoute.name } : { name: 'index' }"
          >
            <template #start>
              <BIconArrowReturnLeft />
            </template>
            Go Back
          </VButton>
        </div>
      </VContainer>
    </TransitionFade>
  </div>

</template>

<script setup>
import { ref, watch } from 'vue';
import { useCompany } from '@/modules/companies/api/get-company';
import { useRouter } from 'vue-router';

import CompanyInfoButtonContainer from '@/modules/companies/components/profile/CompanyInfoButtonContainer.vue';
import BackgroundBanner from '@/modules/companies/components/profile/BackgroundBanner.vue';
import ProfileHeader from '@/modules/companies/components/profile/ProfileHeader.vue';
import ProfileActions from '@/modules/companies/components/profile/ProfileActions.vue';
import ProfileStats from '@/modules/companies/components/profile/ProfileStats.vue';
import CommentFeed from '@/modules/comments/components/Feed/CommentFeed.vue';
import ActivityFeed from '@/modules/activity/components/ActivityFeed.vue';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import CommentPoster from '@/modules/comments/components/Feed/CommentPoster.vue';

const props = defineProps({
  companyId: {
    type: String,
  },
});

const router = useRouter();
const showBanner = ref(true);
const banner = ref(null);

const {
  data: company,
  isLoading: isCompanyLoading,
} = useCompany({
  companyId: props.companyId,
  queryConfig: {
    params: {
      include: ['contacts.method', 'socials.method', 'users'],
    },
  },
});

watch(company, (newValue) => {
  // Random chance of setting banner 25% of the time. Pending image upload features.
  if (Math.random() < 0.5) {
    banner.value = "https://picsum.photos/1600/400";
  } else {
    banner.value = null;
  }

  if (newValue && !newValue?.information) {
    showBanner.value = false;
  }
});

const previousRoute = typeof router.options.history.state.back === 'string'
  ? router.resolve(router.options.history.state.back)
  : null;

</script>