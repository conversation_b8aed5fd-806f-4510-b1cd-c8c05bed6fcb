<template>
  <div>
    <VPageHeader
      title="Add Company"
      description="Register a company for the system. Provide as much or as little information as you wish."
    >
      <template #actions>
        <VButton
          type="submit"
          form="company-form"
          intent="secondary"
        >
          <template #start>
            <BIconPlusLg />
          </template>
          Confirm
        </VButton>
        <VButton
          @click="confirmModalOpen = true"
          intent="danger"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
        <CompanyForm @submit="submit" />

        <div class="flex w-full justify-end space-x-3">
          <VButton
            type="submit"
            form="company-form"
            intent="primary"
          >
            <template #start>
              <BIconPlusLg />
            </template>
            Confirm
          </VButton>

          <VButton
            @click="confirmModalOpen = true"
            intent="danger"
          >
            <template #start>
              <BIconArrowReturnLeft />
            </template>
            Cancel
          </VButton>
        </div>
    </VContainer>

    <VConfirmModal
      @confirmed="router.push({ name: 'companies.index' })"
      v-model:open="confirmModalOpen"
    />
  </div>
</template>

<script setup>
import router from '@/router';
import { ref } from 'vue';
import CompanyForm from '@/modules/companies/components/CompanyForm.vue';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconPlusLg from '~icons/bi/plus-lg';
import { useCreateCompany } from '@/modules/companies/api/post-company';

const confirmModalOpen = ref(false);

const createCompanyMutation = useCreateCompany({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Company Successfully Created',
          message: `The company <b>${result.data?.name}</b> was successfully created.`
        },
        'success'
      );
      router.push({
        name: 'companies.profile',
        params: { id: result.data.locked_id }
      });
    },
  },
});

const submit = (form) => {
  form.submit((data) => createCompanyMutation.mutateAsync({
    data,
  }));
};

</script>
