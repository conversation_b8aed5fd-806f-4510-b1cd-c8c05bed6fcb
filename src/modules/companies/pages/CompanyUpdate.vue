<template>
  <div>
    <VPageHeader title="Update Company" description="Update a company that exists the system.">
      <template #actions>
        <VButton
          type="submit"
          form="company-form"
          intent="secondary"
        >
          <template #start>
            <BIconCheckLg />
          </template>
          Confirm
        </VButton>
        <VButton
          intent="danger"
          @click="confirmModalOpen = true"
        >
          <template #start>
            <BIconArrowReturnLeft />
          </template>
          Cancel
        </VButton>
      </template>
    </VPageHeader>

    <VContainer class="space-y-3 py-3">
      <TransitionFade>
        <CompanyForm v-if="!isCompanyLoading" :company="company" @submit="submit">
          <template #actions>
            <div class="flex w-full justify-start lg:justify-end space-x-3">
              <VButton
                type="submit"
                form="user-form"
                intent="primary"
              >
                <template #start>
                  <BIconCheckLg />
                </template>
                Confirm
              </VButton>

              <VButton intent="danger" @click="confirmModalOpen = true">
                <template #start>
                  <BIconArrowReturnLeft />
                </template>
                Cancel
              </VButton>
            </div>
          </template>
        </CompanyForm>
        <div v-else class="flex w-full justify-center p-6">
          <VSpinner size="xl" />
        </div>
      </TransitionFade>
    </VContainer>
    
    <VConfirmModal
      @confirmed="router.push({ name: 'companies.index' })"
      v-model:open="confirmModalOpen"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useUpdateCompany } from '@/modules/companies/api/put-company';
import { useCompany } from '@/modules/companies/api/get-company';
import CompanyForm from '@/modules/companies/components/CompanyForm.vue';
import router from '@/router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconCheckLg from '~icons/bi/check-lg';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const confirmModalOpen = ref(false);

const updateCompanyMutation = useUpdateCompany({
  mutationConfig: {
    onSuccess: (result) => {
      Reshape.toast(
        {
          title: 'Company Updated',
          message: `The company <b>${company.value.name}</b> was successfully updated.`,
        },
        'success'
      );
      // router.push({ name: 'companies.profile', params: { id: result.data.locked_id } });
    },
  },
});

const { data: company, isLoading: isCompanyLoading } = useCompany({
  companyId: props.id,
  queryConfig: {
    params: {
      include: ['addresses', 'contacts', 'socials'],
    },
  },
});

const submit = (form) => {
  form.submit((data) => updateCompanyMutation.mutateAsync({
    companyId: props.id,
    data,
  }));
};

</script>
