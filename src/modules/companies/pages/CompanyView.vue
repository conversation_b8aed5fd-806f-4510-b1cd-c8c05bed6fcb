<template>
  <div>
    <VPageHeader title="View Company" description="Overview of the selected company." />
    <VContainer class="my-3">
      <TransitionFade>
        <div v-if="isCompanyLoading" class="flex items-center justify-center p-6">
          <VSpinner size="xl" />
        </div>
        <div v-else-if="company" class="grid gap-3">
          <div class="grid gap-3 lg:grid-cols-5 items-start">
            <ViewPanel class="lg:col-span-4 space-y-3">
              <VCardHeader>Company Record</VCardHeader>
              <PanelDefaultField label="Name" :value="company.name" allowCopy />
              <PanelEntityField
                label="Owning User"
                :value="company?.owner?.full_name"
                entityType="User"
                :entityId="company?.owner?.locked_id"
                allowCopy
              />
              <PanelEntityField
                label="Creator User"
                :value="company?.creator?.full_name"
                entityType="User"
                :entityId="company?.creator?.locked_id"
                allowCopy
              />
              <PanelDefaultField label="Status" :value="company.status?.name">
                <template #default>
                  <CompanyStatusBadge :status="company?.status?.name" />
                </template>
              </PanelDefaultField>
              <PanelDateTimeField label="Date Created" :value="company.created_at" />
              <PanelDateTimeField label="Date Updated" :value="company.updated_at" />
            </ViewPanel>

            <VCard class="lg:col-span-1">
              <VCardHeader>Actions</VCardHeader>
              <VCardBody class="flex gap-3 flex-wrap">
                <VButton
                  intent="primary"
                  as="router-link"
                  :to="{
                    name: 'companies.profile',
                    params: { id: company.locked_id },
                  }"
                >View Profile
                  <template #start>
                    <BIconSearch />
                  </template>
                </VButton>
                <VButton
                  intent="primary"
                  as="router-link"
                  :to="{ name: 'companies.index' }"
                >
                  <template #start>
                    <BIconListUl />
                  </template>
                  View All Companies
                </VButton>
                <DropdownMenu>
                  <DropdownMenuTrigger as-child>
                    <VButton intent="primary">
                      <template #start>
                        <BIconSearch />
                      </template>
                      <template #end>
                        <BIconChevronDown />
                      </template>
                      Related Records
                    </VButton>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem as-child>
                      <RouterLink
                        :to="{
                          name: 'companies.contacts.index',
                          params: { id: company.locked_id },
                        }"
                        class="w-full"
                      >
                        <BIconPersonVCardFill />Contacts
                      </RouterLink>
                    </DropdownMenuItem>
                    <DropdownMenuItem as-child>
                      <RouterLink
                        :to="{
                          name: 'companies.addresses.index',
                          params: { id: company.locked_id },
                        }"
                        class="w-full"
                      >
                        <BIconJournalBookmarkFill />Addresses
                      </RouterLink>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                <DropdownMenu>
                  <DropdownMenuTrigger as-child>
                    <VButton intent="danger">
                      <template #start>
                        <BIconPencilSquare />
                      </template>
                      <template #end>
                        <BIconChevronDown />
                      </template>
                      Modify
                    </VButton>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem as-child>
                      <RouterLink
                        :to="{
                          name: 'companies.update',
                          params: { id: company.locked_id },
                        }"
                        class="w-full"
                      >
                        <BIconPencilSquare />Edit
                      </RouterLink>
                    </DropdownMenuItem>
                    <DropdownMenuItem @click="openCompanyDeleteConfirmationDialog">
                      <BIconTrash />Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </VCardBody>
            </VCard>
          </div>
          <div class="grid gap-3 lg:grid-cols-5 items-start">
            <ViewPanel class="lg:col-span-4 space-y-3">
              <VCardHeader>Information Record</VCardHeader>
              <VEmptyState
                v-if="!company.information"
                size="md"
                title="No Company Information"
                description="The requested company has not yet created a company information record."
                :contact="false"
              />
              <div v-else>
                <PanelDefaultField label="Legal Name" :value="company.information?.legal_name" allowCopy />
                <PanelCountryField
                  label="Country Based"
                  :value="company.information?.country?.name"
                  :countryId="company.information?.country?.locked_id"
                  :iso2="company.information?.country?.iso_alpha_2"
                  allowCopy
                />
                <PanelDefaultField label="Company Structure" :value="company.information?.structure_type?.name" allowCopy />
                <PanelDefaultField label="Industry" :value="company.information?.industry?.name" allowCopy />
                <PanelDefaultField label="Tagline/Slogan" :value="company.information?.tagline_slogan" allowCopy />
                <PanelDefaultField label="Description" :value="company.information?.description" allowCopy />
                <PanelDefaultField label="Founded Year" :value="company.information?.founded_year" allowCopy />
                <PanelDefaultField label="VAT Number" :value="company.information?.vat_number" allowCopy />
                <PanelDefaultField label="Website URL" :value="company.information?.website_url" allowCopy>
                  <template #default>
                    <VTextLink :to="company.information?.website_url" external>
                      {{ company.information?.website_url }}
                    </VTextLink>
                  </template>
                </PanelDefaultField>
                <PanelDefaultField label="Timezone" :value="company.information?.timezone?.value" />
                <PanelDefaultField label="Media" :value="company.information?.media">
                  <template #default>
                    <span class="mr-2">{{ company.information?.media }}</span>
                    <span class="text-red-500 font-bold">TO DO</span>
                  </template>
                </PanelDefaultField>
                <PanelDateTimeField label="Date Last Indexed" :value="company.information?.last_indexed_at" />
                <PanelDateTimeField label="Date Created" :value="company.created_at" />
                <PanelDateTimeField label="Date Updated" :value="company.updated_at" />
              </div>
            </ViewPanel>
          </div>
        </div>
        <template v-else>
          <VEmptyState
            title="Company Not Found"
            description="The requested company was not found."
            :contact="true"
          />
        </template>
      </TransitionFade>
    </VContainer>
    <VConfirmModal
      v-model:open="isCompanyDeleteConfirmationDialog"
      @confirmed="deleteCompanyMutation.mutate({ companyId: company.locked_id })"
      title="Confirm Company Deletion"
      :description="`<p>You're about to delete the requested company: <b>${fullname}</b>.</p><p>Please confirm you wish to proceed.</p>`"
      :icon="BIconTrashFill"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconSearch from '~icons/bi/search';
import BIconChevronDown from '~icons/bi/chevron-down';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconListUl from '~icons/bi/list-ul';
import BIconPersonVCardFill from '~icons/bi/person-vcard-fill';
import BIconJournalBookmarkFill from '~icons/bi/journal-bookmark-fill';
import ViewPanel from '@/components/ui/panels/ViewPanel.vue';
import PanelDefaultField from '@/components/ui/panels/fields/ViewPanel/PanelDefaultField.vue';
import PanelCountryField from '@/components/ui/panels/fields/ViewPanel/PanelCountryField.vue';
import PanelDateTimeField from '@/components/ui/panels/fields/ViewPanel/PanelDateTimeField.vue';
import PanelEntityField from '@/components/ui/panels/fields/ViewPanel/PanelEntityField.vue';
import CompanyStatusBadge from '@/modules/companies/components/CompanyStatusBadge.vue';

import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';

import { useDisclosure } from '@/hooks';
import { Company } from '@/types';
import { useRouter } from 'vue-router';
import { useDeleteCompany } from '@/modules/companies/api/delete-company';
import { useCompany } from '@/modules/companies/api/get-company';

const router = useRouter();

const {
  isOpen: isCompanyDeleteConfirmationDialog,
  open: openCompanyDeleteConfirmationDialog,
} = useDisclosure(false);

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const {
  data: company,
  isLoading: isCompanyLoading,
} = useCompany({ companyId: props.id });

const fullname = computed(() => {
  return company.value?.profile?.full_name || 'Undefined';
});

const deleteCompanyMutation = useDeleteCompany({
  mutationConfig: {
    onSuccess: () => {
      router.push({
        name: 'companies.index',
      });
      Reshape.toast(
        {
          title: 'Company Deleted',
          message: `The requested company <b>${fullname.value}</b> was successfully deleted.`,
        },
        'success'
      );
    },
  },
});

</script>
