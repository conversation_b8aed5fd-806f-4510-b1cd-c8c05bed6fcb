import { axios } from '@/lib/axios';

export const CompanyContactService = {
  /**
   * Get a paginated listing of all company contacts.
   */
  query(params, id) {
    return axios.get(`api/companies/${id}/contacts`, { params });
  },

  /**
   * Get a paginated listing of all company contacts.
   */
  getSocialContacts(id) {
    return axios.get(`api/companies/${id}/contacts?filter[is_social]=true`);
  },
};
