import { queryOptions, useQuery } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Customer, Meta } from '@/types/api';
import { Reshape } from '@/Reshape';

export const getCompanyCustomers = (
  params,
  signal,
  id
): Promise<{
  data: Customer[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/companies/${id}/customers`, {
    params,
    signal,
  });
};

export const getCompanyCustomersQueryOptions = ({ params, id }: { params?: unknown, id?: string } = {}) => {
  return queryOptions({
    queryKey: params ? ['company-customers', params] : ['company-customers'],
    queryFn: ({ signal }) => getCompanyCustomers(params, signal, id),
  });
};

type UseCompanyCustomersOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getCompanyCustomersQueryOptions>;
};

export const useCompanyCustomers = ({ queryConfig, params, id }: UseCompanyCustomersOptions) => {
  return useQuery({
    ...getCompanyCustomersQueryOptions({ params, id }),
    ...queryConfig,
  });
};
