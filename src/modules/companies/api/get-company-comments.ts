import { queryOptions, useQuery } from '@tanstack/vue-query';

import { Comment, Meta } from '@/types/api';
import { QueryConfig } from '@/lib/vue-query';
import { Reshape } from '@/Reshape';

export const getCompanyComments = (
    params: any,
    signal: AbortSignal,
    id: string
): Promise<{
    data: Comment[];
    meta: Meta;
}> => {
    return Reshape.http().get(`/api/companies/${id}/comments`, {
        params,
        signal,
    });
};

export const getCompanyCommentsQueryOptions = ({ params, id }: { params?: unknown, id?: string } = {}) => {
    return queryOptions({
        queryKey: params ? ['comments', params] : ['comments'],
        queryFn: ({ signal }) => getCompanyComments(params, signal as AbortSignal, id as string),
    });
};

type UseCompanyCommentsOptions = {
    params?: unknown;
    queryConfig?: QueryConfig<typeof getCompanyCommentsQueryOptions>;
    id?: string;
};

export const useCompanyComments = ({ queryConfig, params, id }: UseCompanyCommentsOptions = {}) => {
    return useQuery({
        ...getCompanyCommentsQueryOptions({ params, id }),
        ...queryConfig,
    });
};
