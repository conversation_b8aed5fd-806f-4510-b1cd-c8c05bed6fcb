import { useMutation, useQueryClient } from '@tanstack/vue-query';
import { MutationConfig } from '@/lib/vue-query';

// Declare the Reshape global variable
declare const Reshape: {
  http: () => {
    delete: (url: string) => Promise<any>;
  };
};

export const deleteCompanyComment = ({ commentId }: { commentId: string }) => {
  return Reshape.http().delete(`/api/companies/comments/${commentId}`);
};

type UseDeleteCompanyCommentOptions = {
  mutationConfig?: {
    onSuccess?: (...args: any[]) => void;
    [key: string]: any;
  };
};

export const useDeleteCompanyComment = ({ mutationConfig }: UseDeleteCompanyCommentOptions = {}) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      // Remove the specific comment from cache
      queryClient.removeQueries({
        queryKey: ['comments', args[1]?.commentId],
        exact: true,
      });

      // Invalidate all comment lists
      queryClient.invalidateQueries({
        queryKey: ['comments'],
        refetchType: 'all',
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteCompanyComment,
  });
};
