import { queryOptions, useQuery } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Activity, Meta } from '@/types/api';
import { Reshape } from '@/Reshape';

export const getCompanyUserActivities = (
  params,
  signal,
  id,
): Promise<{ data: Activity[]; meta: Meta }> => {
  return Reshape.http().get(
    `/api/companies/${id}/user-activity`,
    {
      params,
      signal,
    }
  );
};

export const getCompanyUserActivitiesQueryOptions = ({
  params,
  id,
}: {
  params?: unknown;
  id?: string;
} = {}) => {
  return queryOptions({
    queryKey: params ? ['company-user-activities', params] : ['company-user-activities'],
    queryFn: ({ signal }) => getCompanyUserActivities(params, signal, id),
  });
};

type UseCompanyUserActivitiesOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getCompanyUserActivitiesQueryOptions>;
  id?: string;
};

export const useCompanyUserActivities = ({ queryConfig, params, id }: UseCompanyUserActivitiesOptions) => {
  return useQuery({
    ...getCompanyUserActivitiesQueryOptions({ params, id }),
    ...queryConfig,
  });
}; 