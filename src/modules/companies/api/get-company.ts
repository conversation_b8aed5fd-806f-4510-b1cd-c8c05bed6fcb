import { useQuery, queryOptions } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Company } from '@/types/api';

export const getCompany = ({
  companyId,
  params,
}: {
  companyId: string;
  params?: {
    include?: string[];
    [key: string]: any;
  };
}): Promise<{ data: Company }> => {
  return Reshape.http()
    .get(`/api/companies/${companyId}`, { params })
    .then(({ data }) => data);
};

export const getCompanyQueryOptions = (
  companyId: string,
  params?: { include?: string[]; [key: string]: any }
) => {
  return queryOptions({
    queryKey: ['companies', companyId, params],
    queryFn: () => getCompany({ companyId, params }),
  });
};

type UseCompanyOptions = {
  companyId: string;
  queryConfig?: QueryConfig<typeof getCompanyQueryOptions> & {
    params?: {
      include?: string[];
      [key: string]: any;
    };
  };
};

export const useCompany = ({ companyId, queryConfig }: UseCompanyOptions) => {
  const { params, ...restQueryConfig } = queryConfig || {};
  
  return useQuery({
    ...getCompanyQueryOptions(companyId, params),
    ...restQueryConfig,
  });
}; 