import { queryOptions, useQuery } from '@tanstack/vue-query';
import { Company, Meta } from '@/types/api';

import { QueryConfig } from '@/lib/vue-query';

export const getCompanies = (
  params,
  signal
): Promise<{
  data: Company[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/companies`, {
    params,
    signal,
  });
};

export const getCompaniesQueryOptions = ({ params }: { params?: unknown } = {}) => {
  return queryOptions({
    queryKey: params ? ['companies', params] : ['companies'],
    queryFn: ({ signal }) => getCompanies(params, signal),
  });
};

type UseCompaniesOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getCompaniesQueryOptions>;
};

export const useCompanies = ({ queryConfig, params }: UseCompaniesOptions = {}) => {
  return useQuery({
    ...getCompaniesQueryOptions({ params }),
    ...queryConfig,
  });
}; 