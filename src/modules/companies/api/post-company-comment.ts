import { useMutation, useQueryClient } from '@tanstack/vue-query';
import { MutationConfig } from '@/lib/vue-query';

// Declare the Reshape global variable
declare const Reshape: {
  http: () => {
    post: (url: string, data: any) => Promise<any>;
  };
};

export const createCompanyComment = ({ data }: { data: CreateCompanyCommentPayload }) => {
  return Reshape.http().post(`/api/companies/${data.commentable_id}/comments`, data);
};

type CreateCompanyCommentPayload = {
  body: string;
  commentable_type: 'Company';
  commentable_id: string;
  parent_id?: string;
};

type useCreateCompanyCommentOptions = {
  mutationConfig?: MutationConfig<typeof createCompanyComment>;
};

export const useCreateCompanyComment = ({ mutationConfig }: useCreateCompanyCommentOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ['comments'],
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createCompanyComment,
  });
};
