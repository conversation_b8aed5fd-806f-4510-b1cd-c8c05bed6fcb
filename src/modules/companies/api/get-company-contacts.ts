import { queryOptions, useQuery } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Contact, Meta } from '@/types/api';

export const getCompanyContacts = (
  params,
  signal,
  id
): Promise<{
  data: Contact[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/companies/${id}/contacts`, {
    params,
    signal,
  });
};

export const getCompanyContactsQueryOptions = ({ params, id }: { params?: unknown, id?: string } = {}) => {
  return queryOptions({
    queryKey: params ? ['company-contacts', params] : ['company-contacts'],
    queryFn: ({ signal }) => getCompanyContacts(params, signal, id),
  });
};

type UseCompanyContactsOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getCompanyContactsQueryOptions>;
  id?: string;
};

export const useCompanyContacts = ({ queryConfig, params, id }: UseCompanyContactsOptions) => {
  return useQuery({
    ...getCompanyContactsQueryOptions({ params, id }),
    ...queryConfig,
  });
};
