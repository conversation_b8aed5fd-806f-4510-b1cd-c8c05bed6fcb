import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { Company } from '@/types/api';

export const createCompany = ({ data }: { data: CreateCompanyPayload }) => {
  return Reshape.http().post(`/api/companies`, data);
};

type CreateCompanyPayload = Omit<Company, 'id' | 'locked_id'>;

type UseCreateCompanyOptions = {
  mutationConfig?: MutationConfig<typeof createCompany>;
};

export const useCreateCompany = ({ mutationConfig }: UseCreateCompanyOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.invalidateQueries({
        queryKey: ['companies'],
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createCompany,
  });
};