import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getCompaniesQueryOptions } from './get-companies';

// Declare the Reshape global variable
declare const Reshape: {
  http: () => {
    delete: (url: string) => Promise<any>;
  };
};

export const deleteCompany = ({ companyId }: { companyId: string }) => {
  return Reshape.http().delete(`/api/companies/${companyId}`);
};

type UseDeleteCompanyOptions = {
  mutationConfig?: {
    onSuccess?: (...args: any[]) => void;
    [key: string]: any;
  };
};

export const useDeleteCompany = ({ mutationConfig }: UseDeleteCompanyOptions = {}) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['companies', args[1].companyId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getCompaniesQueryOptions().queryKey,
      });
      if (onSuccess) {
        onSuccess(...args);
      }
    },
    ...restConfig,
    mutationFn: deleteCompany,
  });
}; 