import { queryOptions, useQuery } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Address, Meta } from '@/types/api';

export const getCompanyAddresses = (
  params,
  signal,
  id
): Promise<{
  data: Address[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/companies/${id}/addresses`, {
    params,
    signal,
  });
};

export const getCompanyAddressesQueryOptions = ({ params, id }: { params?: unknown, id?: string } = {}) => {
  return queryOptions({
    queryKey: params ? ['company-addresses', params] : ['company-addresses'],
    queryFn: ({ signal }) => getCompanyAddresses(params, signal, id),
  });
};

type UseCompanyAddressesOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getCompanyAddressesQueryOptions>;
};

export const useCompanyAddresses = ({ queryConfig, params, id }: UseCompanyAddressesOptions) => {
  return useQuery({
    ...getCompanyAddressesQueryOptions({ params, id }),
    ...queryConfig,
  });
};
