import { queryOptions, useQuery } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Contact, Meta } from '@/types/api';
import { Reshape } from '@/Reshape';

export const getCompanyUsers = (
  params,
  signal,
  id
): Promise<{
  data: Contact[];
  meta: Meta;
}> => {
  return Reshape.http().get(`/api/companies/${id}/users`, {
    params,
    signal,
  });
};

export const getCompanyUsersQueryOptions = ({ params, id }: { params?: unknown; id?: string } = {}) => {
  return queryOptions({
    queryKey: params ? ['company-users', params, id] : ['company-users', id],
    queryFn: ({ signal }) => getCompanyUsers(params, signal, id),
  });
};

type UseCompanyUsersOptions = {
  params?: unknown;
  id?: string;
  queryConfig?: QueryConfig<typeof getCompanyUsersQueryOptions>;
};

export const useCompanyUsers = ({ queryConfig, params, id }: UseCompanyUsersOptions) => {
  return useQuery({
    ...getCompanyUsersQueryOptions({ params, id }),
    ...queryConfig,
  });
};
