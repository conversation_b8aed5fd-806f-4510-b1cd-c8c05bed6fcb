import { queryOptions, useQuery } from '@tanstack/vue-query';

import { QueryConfig } from '@/lib/vue-query';
import { Activity, Meta } from '@/types/api';
import { Reshape } from '@/Reshape';

export const getCompanyActivities = (
  params,
  signal,
  id,
): Promise<{ data: Activity[]; meta: Meta }> => {
  return Reshape.http().get(
    `/api/companies/${id}/activity`,
    {
      params,
      signal,
    }
  );
};

export const getCompanyActivitiesQueryOptions = ({
  params,
  id,
}: {
  params?: unknown;
  id?: string;
} = {}) => {
  return queryOptions({
    queryKey: params ? ['company-activities', params] : ['company-activities'],
    queryFn: ({ signal }) => getCompanyActivities(params, signal, id),
  });
};

type UseCompanyActivitiesOptions = {
  params?: unknown;
  queryConfig?: QueryConfig<typeof getCompanyActivitiesQueryOptions>;
  id?: string;
};

export const useCompanyActivities = ({ queryConfig, params, id }: UseCompanyActivitiesOptions) => {
  return useQuery({
    ...getCompanyActivitiesQueryOptions({ params, id }),
    ...queryConfig,
  });
}; 