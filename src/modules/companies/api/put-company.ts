import { useMutation, useQueryClient } from '@tanstack/vue-query';

import { MutationConfig } from '@/lib/vue-query';

import { getCompanyQueryOptions } from './get-company';

import { Company } from '@/types/api';

export const updateCompany = ({ companyId, data }: { companyId: string, data: UpdateCompanyPayload }) => {
  return Reshape.http().put(`/api/companies/${companyId}`, data);
};

type UpdateCompanyPayload = Partial<Omit<Company, 'locked_id'>>;

type UseUpdateCompanyOptions = {
  companyId: string;
  mutationConfig?: MutationConfig<typeof updateCompany>;
};

export const useUpdateCompany = ({ companyId, mutationConfig }: UseUpdateCompanyOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      queryClient.removeQueries({
        queryKey: ['companies', args[1].companyId],
        exact: true,
      });

      queryClient.invalidateQueries({
        queryKey: getCompanyQueryOptions(args[1].companyId).queryKey,
      });
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: updateCompany,
  });
};