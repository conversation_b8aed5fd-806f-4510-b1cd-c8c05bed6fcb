import UserLayout from '@/layouts/UserLayout.vue';
import { auth } from '@/router/middleware';

export default [
  {
    path: '/companies',
    component: UserLayout,
    meta: {
      middleware: auth,
    },
    children: [
      {
        path: '',
        name: 'companies.index',
        component: () => import('./pages/CompanyIndex.vue'),
        meta: {
          title: 'Companies',
          breadcrumbs: [{ label: 'Companies', name: 'companies.index' }],
        },
      },
      {
        path: 'add',
        name: 'companies.create',
        component: () => import('./pages/CompanyCreate.vue'),
        meta: {
          title: 'Add Company',
          breadcrumbs: [
            { label: 'Companies', name: 'companies.index' },
            { label: 'Add', name: 'companies.create' },
          ],
        },
      },
      {
        path: ':id/edit',
        name: 'companies.update',
        component: () => import('./pages/CompanyUpdate.vue'),
        props: true,
        meta: {
          title: 'Edit Company',
          breadcrumbs: [
            { label: 'Companies', name: 'companies.index' },
            { label: 'Edit', name: 'companies.update' },
          ],
        },
      },
      {
        path: ':id',
        name: 'companies.profile',
        component: () => import('./pages/Profile.vue'),
        props: route => ({
          companyId: route.params.id,
        }),
        meta: {
          title: 'Company Profile',
          breadcrumbs: [
            { label: 'Companies', name: 'companies.index' },
            { label: 'Profile', name: 'companies.profile' },
          ],
        },
      },
      {
        path: ':id/activity',
        name: 'companies.activity',
        component: () => import('@/modules/activity/pages/ActivityIndex.vue'),
        props: route => ({
          activitableType: 'company',
          activitableId: route.params.id,
        }),
        meta: {
          title: 'Company Record Activity',
          headerTitle: 'Company Record Activity',
          headerDescription: 'View historical activity for the selected company. This includes the company and related detail records only.',
          breadcrumbs: [
            { label: 'Companies', name: 'companies.index' },
            { label: 'Profile', name: 'companies.profile' },
            { label: 'Activity', name: 'companies.activity' },
          ],
        },
      },
      {
        path: ':id/user-activity',
        name: 'companies.user-activity',
        component: () => import('@/modules/activity/pages/ActivityIndex.vue'),
        props: route => ({
          activitableType: 'company',
          activitableId: route.params.id,
          caused: true,
        }),
        meta: {
          title: 'Company User Activity',
          headerTitle: 'Company User Activity',
          headerDescription: 'Overview of activity caused by primary members of the selected company.',
          breadcrumbs: [
            { label: 'Companies', name: 'companies.index' },
            { label: 'Profile', name: 'companies.profile' },
            { label: 'User Activity', name: 'companies.user-activity' },
          ],
        },
      },
      {
        path: ':id/view',
        name: 'companies.view',
        component: () => import('./pages/CompanyView.vue'),
        props: true,
        meta: {
          title: 'View Company',
          breadcrumbs: [
            { label: 'Companies', name: 'companies.index' },
            { label: 'View', name: 'companies.view' },
          ],
        },
      },
      {
        path: ':id/subscriptions',
        children: [
          {
            path: '',
            name: 'companies.subscription.index',
            component: () => import('@/modules/companies/pages/subscriptions/CompanySubscriptions.vue'),
            props: route => ({
              self: false,
              id: route.params.id,
            }),
            meta: {
              title: 'Company Subscription',
              headerTitle: 'Company Subscription',
              headerDescription: 'Manage the subscription for the selected company.',
              breadcrumbs: [
                { label: 'Companies', name: 'companies.index' },
                { label: 'Profile', name: 'companies.profile' },
                { label: 'Subscription', name: 'companies.subscription.index' },
              ],
            },
          },
          {
            path: 'invoices',
            name: 'companies.subscription.invoices',
            component: () => import('@/modules/companies/pages/subscriptions/CompanySubscriptionInvoices.vue'),
            props: route => ({
              self: false,
              id: route.params.id,
            }),
            meta: {
              title: 'Company Subscription Invoices',
              headerTitle: 'Company Subscription Invoices',
              headerDescription: 'View invoices for the company.',
              breadcrumbs: [
                { label: 'Companies', name: 'companies.index' },
                { label: 'Profile', name: 'companies.profile' },
                { label: 'Subscription', name: 'companies.subscription.index' },
                { label: 'Invoices', name: 'companies.subscription.invoices' },
              ],
            },
          },
        ],
      },
      {
        path: ':id/contacts',
        children: [
          {
            path: '',
            name: 'companies.contacts.index',
            component: () => import('@/modules/contacts/pages/ContactsIndex.vue'),
            props: route => ({
              contactableType: 'companies',
              contactableId: route.params.id,
            }),
            meta: {
              title: 'Company Contacts',
              headerTitle: 'Company Contacts',
              headerDescription: 'Overview of all contacts related to the selected company.',
              breadcrumbs: [
                { label: 'Companies', name: 'companies.index' },
                { label: 'View', name: 'companies.view' },
                { label: 'Contacts', name: 'companies.contacts.index' },
              ],
              returnAction: true,
            },
          },
          {
            path: 'add',
            name: 'companies.contacts.create',
            component: () => import('@/modules/contacts/pages/ContactsCreate.vue'),
            props: route => ({
              contactableType: 'companies',
              contactableId: route.params.id,
            }),
            meta: {
              title: 'Add Company Contact',
              headerTitle: 'Add Company Contact',
              headerDescription: 'Define a new contact for your chosen company.',
              breadcrumbs: [
                { label: 'Companies', name: 'companies.index' },
                { label: 'View', name: 'companies.view' },
                { label: 'Contacts', name: 'companies.contacts.index' },
                { label: 'Add', name: 'companies.contacts.create' },
              ],
            },
          },
        ],
      },
      {
        path: ':id/addresses',
        children: [
          {
            path: '',
            name: 'companies.addresses.index',
            component: () => import('@/modules/addresses/pages/AddressIndex.vue'),
            props: route => ({
              addressableType: 'companies',
              addressableId: route.params.id,
            }),
            meta: {
              title: 'Company Addresses',
              headerTitle: 'Company Addresses',
              headerDescription: 'Overview of all addresses related to the selected company.',
              breadcrumbs: [
                { label: 'Companies', name: 'companies.index' },
                { label: 'View', name: 'companies.view' },
                { label: 'Addresses', name: 'companies.addresses.index' },
              ],
              returnAction: true,
            },
          },
          {
            path: 'add',
            name: 'companies.addresses.create',
            component: () => import('@/modules/addresses/pages/AddressCreate.vue'),
            props: route => ({
              addressableType: 'companies',
              addressableId: route.params.id,
            }),
            meta: {
              title: 'Add Company Address',
              headerTitle: 'Add Company Address',
              headerDescription: 'Define a new address for your chosen company.',
              breadcrumbs: [
                { label: 'Companies', name: 'companies.index' },
                { label: 'View', name: 'companies.view' },
                { label: 'Addresses', name: 'companies.addresses.index' },
                { label: 'Add', name: 'companies.addresses.create' },
              ],
            },
          },
        ],
      },
      {
        path: ':id/customers',
        name: 'companies.customers.index',
        component: () => import('./pages/companies/Customers.vue'),
        props: true,
        meta: {
          title: 'Company Customers',
          headerTitle: 'Company Customers',
          headerDescription: 'Overview of all related company customers.',
          breadcrumbs: [
            { label: 'Companies', name: 'companies.index' },
            { label: 'Profile', name: 'companies.profile' },
            { label: 'Customers', name: 'companies.customers.index' },
          ],
        },
      },
      {
        path: ':id/users',
        name: 'companies.users.index',
        component: () => import('./pages/companies/Users.vue'),
        props: true,
        meta: {
          title: 'Company Users',
          headerTitle: 'Company Users',
          headerDescription: 'Overview of all users with a membership to the company.',
          breadcrumbs: [
            { label: 'Companies', name: 'companies.index' },
            { label: 'Profile', name: 'companies.profile' },
            { label: 'Users', name: 'companies.users.index' },
          ],
        },
      },
    ],
  },
];
