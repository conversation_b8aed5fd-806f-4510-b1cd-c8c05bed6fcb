<template>
  <VTooltip v-if="created_at" :content="`Membership since ${humanizeTime(created_at)}`">
    <VBadge v-if="status" :color="statusColor" class="uppercase">{{ status }}</VBadge>
  </VTooltip>
  <VBadge v-else-if="status" :color="statusColor" class="uppercase">{{ status }}</VBadge>
  <template v-else>
    <span class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
  </template>
</template>
  
<script setup>
import { computed } from 'vue';
import { humanizeTime } from '@/util/dateUtils';

const props = defineProps({
status: {
  type: String,
},
// Simply don't pass this if we don't want it to include a tooltip.
created_at: {
  type: String,
  required: false,
},
});

const statusColor = computed(() => {
  switch (props.status.toLowerCase()) {
    case 'active':
      return 'success';
    case 'inactive':
      return 'danger';
    case 'pending approval':
      return 'warning';
    default:
      return 'info';
  }
});

</script>