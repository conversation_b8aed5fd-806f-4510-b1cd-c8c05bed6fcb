<template>
  <section aria-labelledby="profile-overview-title">
    <h2 class="sr-only" id="profile-overview-title">Profile Overview</h2>
    <transition-fade>
      <div
        v-if="company?.information"
        class="min-h-[150px] overflow-hidden rounded-lg bg-platinum-200/75 shadow dark:bg-midnight-500/75"
      >
        <div class="p-2 md:p-4 lg:p-6">
          <div class="">
            <div class="mt-3 md:mt-0 md:flex md:space-x-5 w-full">
              <div class="flex justify-center md:block">
                <VAvatar
                  class="flex-shrink-0"
                  size="2xl"
                  :name="company?.name"
                  :src="company?.information?.media?.profile_photo?.conversions?.medium"
                />
              </div>
              <div class="mt-4 text-center md:mt-0 md:pt-1 md:text-left flex flex-col space-y-1">
                <h2 class="text-xl font-bold text-primary-500 md:text-3xl">
                  {{ company?.name }}
                </h2>
                <div
                  v-if="company?.information?.tagline_slogan"
                  class="text-sm text-primary-500 space-x-0.5 leading-none"
                >
                  <BIconQuote class="inline h-3 w-3 -mt-2.5 text-platinum-950 dark:text-midnight-100"/>
                  <span class="italic">{{ company?.information?.tagline_slogan }}</span>
                  <BIconQuote class="inline h-3 w-3 -mt-2.5 -scale-x-100 text-platinum-950 dark:text-midnight-100"/>
                </div>
                <span class="text-sm font-light text-platinum-950 dark:text-midnight-50">
                  <span>{{ company?.information?.description }}</span>
                  <!-- <span class="mr-1">{{ description.value }}</span>
                  <span class="align-middle">
                    <VCountryField
                      class="mt-px [&>dd]:mt-0 inline-block align-top"
                      :value="description.country.name + '.'"
                      :iso2="description.country.iso2"
                    />
                  </span> -->
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="divide-y divide-gray-200 border-gray-200 bg-primary-500 md:grid-cols-3 md:divide-x md:divide-y-0">
          <div class="px-6 py-4 text-sm font-medium lg:flex w-full items-stretch md:justify-between space-y-1 lg:space-y-0 text-center md:text-left">
            <span class="text-platinum-100 dark:text-midnight-800 my-auto space-x-1.5">
              <span>No Subscription</span>
              <span>—</span>
              <VTextLink
                class="underline underline-offset-2"
                intent="secondary"
                :to="{
                  name: 'companies.subscription.index',
                  params: { id: company?.locked_id }
                }"
              >Get started in seconds!</VTextLink>
            </span>

            <div v-if="socials.length" class="flex flex-none gap-2 justify-center md:justify-start">
              <VTooltip
                v-for="social in socials"
                :key="social"
                :content="`Connect via ${social.method.name}`"
                side="bottom"
              >
                <div
                  class="group overflow-visible"
                  :style="{ '--hover-color': social.method.color }"
                >
                  <VTextLink
                    :to="contactLinkString(social)"
                    class="flex scale-[0.8] transition-transform duration-200 ease-in-out hover:scale-100"
                    :external="true"
                    :disable-icon="true"
                  >
                    <VIcon
                      :value="social.method.icon"
                      class="text-platinum-50 dark:text-midnight-500 [&>svg]:h-6 [&>svg]:w-6 transition-all duration-200 group-hover:text-[color:var(--hover-color)] group-hover:[&>svg]:drop-shadow-[0_0_4px_rgba(255,255,255,0.15)] dark:group-hover:[&>svg]:drop-shadow-[0_0_4px_rgba(0,0,0,0.2)]"
                    />
                  </VTextLink>
                </div>
              </VTooltip>
            </div>
          </div>
        </div>
      </div>

      <!-- Skeleton for when loading/etc -->
      <div
        v-else
        class="min-h-[180px] cursor-wait overflow-hidden rounded-lg bg-platinum-200/75 shadow dark:bg-midnight-500/75"
      >
        <div class="p-6">
          <div class="md:flex md:items-center md:justify-between">
            <div class="flex-grow md:flex md:space-x-5">
              <div class="flex flex-grow-0 justify-center">
                <div class="relative h-24 w-24 rounded-full">
                  <VSkeleton class="absolute inset-0" circle />
                </div>
              </div>
              <div class="mt-4 flex-grow md:mt-0 md:pt-1 md:text-left">
                <VSkeleton class="mb-3 mt-1 h-6 max-w-[28rem]"></VSkeleton>
                <VSkeleton class="mt-1 h-4 max-w-36"></VSkeleton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition-fade>
  </section>
</template>

<script setup>
import { computed } from 'vue';
import BIconQuote from '~icons/bi/quote';

const props = defineProps({
  company: {
    type: Object,
    default: null,
  },
  socials: {
    type: Array,
    default: () => [],
  },
});

// Helper function to generate the contact link string.
const contactLinkString = (contact) => {
  return [
    contact.method.url,
    contact.method.prefix,
    contact.value,
    contact.method.suffix,
  ].join('');
};

</script>
