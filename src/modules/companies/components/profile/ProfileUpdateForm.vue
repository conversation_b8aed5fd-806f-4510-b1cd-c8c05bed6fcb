<template>
  <form
    id="profile-form"
    class="space-y-4"
    @submit.prevent="submit"
  >
    <FormContainer
      title="My Details"
      description="Adjust your personal profile details."
      :icon="BIconPersonFillLock"
    >
      <template #body>
        <div class="grid grid-cols-6 gap-6">

          <VField
            label="Title"
            class="col-span-6 md:col-span-2"
            :errors="form.errors.get('honorific_id')"
            :loading="loading"
          >
            <HonorificCombobox v-model="form.honorific_id" />
          </VField>

          <VField
            label="First Name"
            class="col-span-6 md:col-span-4"
            required
            :errors="form.errors.get('first_name')"
            :loading="loading"
          >
            <VInput
              v-model="form.first_name"
              type="text"
              placeholder="Enter First Name"
              required
            />
          </VField>

          <VField
            label="Middle Name"
            class="col-span-6 md:col-span-3"
            :errors="form.errors.get('middle_names')"
            :loading="loading"
          >
            <VInput
              v-model="form.middle_names"
              type="text"
              placeholder="Enter Middle Name"
            />
          </VField>

          <VField
            label="Last Name"
            class="col-span-6 md:col-span-3"
            required
            :errors="form.errors.get('last_name')"
            :loading="loading"
          >
            <VInput
              v-model="form.last_name"
              type="text"
              placeholder="Enter Last Name"
              required
            />
          </VField>

          <VField
            label="Job Title"
            class="col-span-6 md:col-span-3"
            :errors="form.errors.get('job_title')"
            :loading="loading"
          >
            <VInput
              v-model="form.job_title"
              type="text"
              placeholder="Enter Job Title"
            />
          </VField>

          <VField
            label="Local Timezone"
            class="col-span-3 md:col-span-3"
            :errors="form.errors.get('timezone_id')"
            :loading="loading"
          >
            <TimezoneCombobox
              v-model="form.timezone_id"
              placeholder="Select Local Timezone"
              value-prop="locked_id"
            />
          </VField>

          <VField
            label="Current Country Based"
            class="col-span-6 lg:col-span-2"
            :errors="form.errors.get('current_country_id')"
            :loading="loading"
          >
            <CountryCombobox v-model="form.current_country_id" />
          </VField>

          <VField
            label="Current Country Area"
            class="col-span-6 lg:col-span-4"
            :errors="form.errors.get('current_country_extra')"
            :loading="loading"
          >
            <VInput
              v-model="form.current_country_extra"
              type="text"
              placeholder="Enter City, etc"
            />
          </VField>

          <VField
            label="Country of Birth"
            class="col-span-6 lg:col-span-2"
            :errors="form.errors.get('from_country_id')"
            :loading="loading"
          >
            <CountryCombobox v-model="form.from_country_id" />
          </VField>

          <VField
            label="Birth Country Area"
            class="col-span-6 lg:col-span-4"
            :errors="form.errors.get('from_country_extra')"
            :loading="loading"
          >
            <VInput
              v-model="form.from_country_extra"
              type="text"
              placeholder="Enter City, etc"
            />
          </VField>
        </div>
      </template>
    </FormContainer>

    <FormContainer
      title="Introducing Myself"
      description="Make your profile stand out with a custom profile image, banner and bio."
      :icon="BIconPersonBadgeFill"
    >
      <template #body>
        <div class="grid grid-cols-6 gap-6">

          <VField
            label="Profile Intro"
            class="col-span-6"
            help="Introductory text displayed at the top of your profile."
          >
            <VTextarea
              v-model="form.intro"
              placeholder="Enter Intro"
              rows="1"
            />
          </VField>

          <VField
            label="Profile Bio"
            class="col-span-6"
            help="Longer expanding area on your profile to expand on your introduction."
          >
            <VTextarea
              v-model="form.bio"
              placeholder="Enter Bio"
              rows="3"
            />
          </VField>

          <VField
            label="Profile Image"
            class="col-span-6"
            :errors="form.errors.get('profilePhoto')"
            :loading="loading"
          >
            <VInput
              type="file"
              v-model="form.profilePhoto"
            />
          </VField>
          <VField
            label="Banner Image"
            class="col-span-6"
            :errors="form.errors.get('bannerPhoto')"
            :loading="loading"
          >
            <VInput
              type="file"
              v-model="form.bannerPhoto"
            />
          </VField>

        </div>
      </template>
    </FormContainer>
    
  </form>
</template>

<script setup>
import HonorificCombobox from '@/components/ui/form/composables/HonorificCombobox.vue';
import TimezoneCombobox from '@/components/ui/form/composables/TimezoneCombobox.vue';
import CountryCombobox from '@/components/ui/form/composables/CountryCombobox.vue';

import FormContainer from '@/components/ui/forms/FormContainer.vue';
import { useForm } from '@/hooks/form';
import BIconPersonFillLock from '~icons/bi/person-fill-lock';
import BIconPersonBadgeFill from '~icons/bi/person-badge-fill';
import { watch } from 'vue';

const props = defineProps({
  profile: {
    type: Object,
    default: null,
  },
  loading: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(['submit']);
const submit = () => {
  emits('submit', form.value);
};

// Helper function to extract profile data with defaults
const getProfileData = (profile) => {
  const data = profile || {};
  return {
    first_name: data.first_name ?? '',
    middle_names: data.middle_names ?? '',
    last_name: data.last_name ?? '',
    job_title: data.job_title ?? '',
    intro: data.intro ?? '',
    bio: data.bio ?? '',
    
    honorific_id: data.honorific?.locked_id ?? '',
    timezone_id: data.timezone?.locked_id ?? '',
    current_country_id: data.current_country?.locked_id ?? '',
    current_country_extra: data.current_country_extra ?? '',
    from_country_id: data.from_country?.locked_id ?? '',
    from_country_extra: data.from_country_extra ?? '',
  };
};

// Initialize form with relevant data if available.
const form = useForm(getProfileData(props.profile));

// Watch for changes to the user prop and update form when it changes.
watch(() => props.profile, (newProfile) => {
  if (newProfile) {
    form.value.populate(getProfileData(newProfile));
  }
});
</script>
