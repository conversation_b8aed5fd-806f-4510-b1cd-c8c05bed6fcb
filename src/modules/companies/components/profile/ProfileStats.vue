<template>
  <transition-fade>
    <div v-if="company" class="rounded-lg bg-platinum-50 p-2 shadow dark:bg-midnight-500 md:p-3">
      <div class="space-y-3">
        <!-- Referals Pie Chart -->
        <div class="flex flex-col items-center justify-center rounded-lg bg-platinum-300 dark:bg-midnight-400">
          <div class="w-full">
            <h3 class="mt-3 text-center text-lg text-platinum-950 dark:text-midnight-50">Referals</h3>
            <PieChart :chartData="pieData" :chartColors="referalColors" :legend="false" :border-color="borderColor" />
          </div>
        </div>

        <!-- Line Chart -->
        <div class="col-span-2 flex flex-col items-center justify-center rounded-lg bg-platinum-300 p-6 dark:bg-midnight-400 lg:col-span-3">
          <div class="w-full">
            <h3 class="mt-3 text-center text-lg text-platinum-950 dark:text-midnight-50">Customers</h3>
            <LineChart
              :chartData="[13, 14, 18, 13, 15, 12]"
              :chartColor="`hsl(${rootStyles.getPropertyValue('--primary-500').replaceAll(' ', ', ')}, 1)`"
              :pointColor="pointColor"
            />
          </div>
        </div>
        <!-- <div class="bg-midnight-400 rounded-lg shadow-lg p-6 flex flex-col items-center justify-center">
          <h3 class="text-lg font-semibold mb-4">Monthly Posts</h3>
          <BarChart :chartData="barData" />
        </div> -->
      </div>
    </div>
  </transition-fade>
</template>

<script setup>
import { ref, computed } from 'vue';
import PieChart from '@/components/ui/charts/PieChart.vue';
import LineChart from '@/components/ui/charts/LineChart.vue';
// import BarChart from '@/components/ui/charts/BarChart.vue';
import { useDark } from '@vueuse/core';

import { useTailwind } from '@/hooks/tailwind.js'; // Adjust the path if necessary

const props = defineProps({
  company: {
    type: Object,
    default: null,
  },
});

const { theme } = useTailwind();
const tailwindColors = theme.colors;

const isDark = useDark();

// Get current CSS variable values.
const rootStyles = getComputedStyle(document.documentElement);
const referalColors = ref([]);

// Might want to get this watched later on (when theme changes, etc).
referalColors.value = [
  `rgba(${rootStyles.getPropertyValue('--primary-300').trim()}, 0.8)`,
  `rgba(${rootStyles.getPropertyValue('--primary-400').trim()}, 0.8)`,
  `rgba(${rootStyles.getPropertyValue('--primary-500').trim()}, 0.8)`,
  `rgba(${rootStyles.getPropertyValue('--primary-600').trim()}, 0.8)`,
  `rgba(${rootStyles.getPropertyValue('--primary-700').trim()}, 0.8)`,
];

// Dummy Data for Pie Chart (User Activity)
const pieData = ref({
  labels: ['Completed Registrations', 'Incomplete Registration', 'Outstanding Registrations'],
  datasets: [
    {
      data: [6, 2, 45],
    },
  ],
});

// Dummy Data for Bar Chart (Monthly Posts)
const barData = ref({
  labels: ['January', 'February', 'March', 'April', 'May', 'June'],
  datasets: [
    {
      label: 'Posts',
      backgroundColor: '#42A5F5',
      borderColor: '#1E88E5',
      borderWidth: 2,
      data: [60, 75, 80, 50, 95, 65],
    },
  ],
});

// Check if data is present
const hasData = pieData.value.datasets[0].data.length > 0 || barData.value.datasets[0].data.length > 0;

const borderColor = computed(() => {
  return isDark.value ? tailwindColors.midnight[400] : tailwindColors.platinum[300];
});

const pointColor = computed(() => {
  return isDark.value ? tailwindColors.midnight[900] : tailwindColors.platinum[800];
});
</script>
