<template>
  <section aria-labelledby="profile-actions-title" class="flex w-full">
    <h2 class="sr-only" id="profile-actions-title">Company Profile Actions</h2>
    <transition-fade>
      <div v-if="company" class="w-full rounded-lg bg-platinum-200/75 shadow dark:bg-midnight-500/75">
        <div class="flex grow flex-col">
          <div class="flex flex-col gap-2 rounded-b-lg p-2 md:gap-3 md:p-3">

            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <VButton
                  centered
                  size="sm"
                >Company Actions
                  <template #start>
                    <BIconPersonCircle aria-hidden="true" />
                  </template>
                  <template #end>
                    <BIconChevronDown aria-hidden="true" />
                  </template>
                </VButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem as-child>
                  <RouterLink
                    :to="{ name: 'companies.update', params: { id: company.locked_id } }"
                  >
                    <BIconPencilSquare aria-hidden="true" />Edit Company
                  </RouterLink>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <VButton centered size="sm">
                  <template #start>
                    <BIconSearch aria-hidden="true" />
                  </template>
                  View Details
                  <template #end>
                    <BIconChevronDown aria-hidden="true" />
                  </template>
                </VButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuGroup>
                  <DropdownMenuLabel class="pt-0">Related Records</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{ name: 'companies.users.index', params: { id: company.locked_id } }"
                    >
                      <BIconPeopleFill />Users
                    </RouterLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{ name: 'companies.customers.index', params: { id: company.locked_id } }"
                    >
                      <BIconPersonWorkspace />Customers
                    </RouterLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{ name: 'companies.contacts.index', params: { id: company.locked_id } }"
                    >
                      <BIconPersonVCardFill />Contacts & Socials
                    </RouterLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{ name: 'companies.addresses.index', params: { id: company.locked_id } }"
                    >
                      <BIconJournalBookmarkFill />Addresses
                    </RouterLink>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuGroup>
                  <DropdownMenuLabel>Activity</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{ name: 'companies.activity', params: { id: company.locked_id } }"
                    >
                      <BIconActivity />Company Update Activity
                    </RouterLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{ name: 'companies.user-activity', params: { id: company.locked_id } }"
                    >
                      <BIconPeopleFill />Company User Caused Activity
                    </RouterLink>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuGroup>
                  <DropdownMenuLabel>Subscription</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{ name: 'companies.subscription.index', params: { id: company.locked_id } }"
                    >
                      <BIconBookmarkStarFill />Company Subscription
                    </RouterLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem as-child>
                    <RouterLink
                      :to="{ name: 'companies.subscription.invoices', params: { id: company.locked_id } }"
                    >
                      <BIconReceiptCutoff />Invoices
                    </RouterLink>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <!-- Skeleton for when loading/etc -->
      <div
        v-else
        class="w-full cursor-wait overflow-hidden rounded-lg bg-platinum-200/75 shadow dark:bg-midnight-500/75"
      >
        <div class="p-2 md:p-3">
          <div class="sm:flex sm:items-center sm:justify-between">
            <div class="flex-grow md:flex md:flex-col">
              <VSkeleton class="mb-3 h-6" rounded />
              <VSkeleton class="mb-3 h-5 max-w-60 md:max-w-40" rounded />
              <VSkeleton class="h-4 max-w-40 md:max-w-20" rounded />
            </div>
          </div>
        </div>
      </div>
    </transition-fade>
  </section>
</template>

<script setup>
import BIconChevronDown from '~icons/bi/chevron-down';
import BIconJournalBookmarkFill from '~icons/bi/journal-bookmark-fill';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconPersonCircle from '~icons/bi/person-circle';
import BIconSearch from '~icons/bi/search';
import BIconPersonVCardFill from '~icons/bi/person-vcard-fill';
import BIconBookmarkStarFill from '~icons/bi/bookmark-star-fill';
import BIconReceiptCutoff from '~icons/bi/receipt-cutoff';
import BIconActivity from '~icons/bi/activity';
import BIconPeopleFill from '~icons/bi/people-fill';
import BIconPersonWorkspace from '~icons/bi/person-workspace';
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from '@/components/ui/shadcn/dropdown-menu';
import { computed } from 'vue';

const props = defineProps({
  company: {
    type: Object,
    default: null,
  },
});

const name = computed(() => {
  return props.company?.name || 'Undefined';
});

</script>
