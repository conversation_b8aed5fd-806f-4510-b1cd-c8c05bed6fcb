<template>
  <div class="grid grid-cols-1 md:grid-cols-4 gap-3 w-full">
    <template v-for="item in info" :key="item.title">
      <VTooltip
        v-if="item.link && item.tooltip"
        :content="item.tooltip"
        contentAsChild
      >
        <RouterLink
          :to="{ name: item.link.name, params: item.link.params }"
          class="h-full flex flex-col gap-1 bg-platinum-200/75 dark:bg-midnight-500/75 hover:bg-platinum-200/90 dark:hover:bg-midnight-500/90 rounded-lg px-3 py-2 shadow ring-1 ring-platinum-900/5 dark:ring-midnight-900/5 select-none transition cursor-pointer hover:ring-primary-500 dark:hover:ring-primary-500 focus-visible:outline-primary-500 focus:ring-4 focus:ring-primary-500 dark:focus:ring-primary-500  group"
        >
          <CompanyInfoButton :item="item" />
        </RouterLink>
      </VTooltip>
      <div
        v-else
        class="flex flex-col gap-1 bg-platinum-200/75 dark:bg-midnight-500/75 rounded-lg px-3 py-2 shadow ring-1 ring-platinum-900/5 dark:ring-midnight-900/5 select-none transition"
      >
        <CompanyInfoButton :item="item" />
      </div>
    </template>
  </div>
</template>


<script setup>
import { computed } from 'vue';
import BIconPeople from '~icons/bi/people-fill';
import BIconBriefcase from '~icons/bi/briefcase-fill';
import BiconStack from '~icons/bi/stack';
import BiconGlobe from '~icons/bi/globe-americas';
import CompanyInfoButton from '@/modules/companies/components/profile/CompanyInfoButton.vue';

const props = defineProps({
  company: {
    type: Object,
    default: null,
    required: true,
  },
});

const info = computed(() => {
  let data = [];

  if (props.company?.information?.industry?.name) {
    data.push({
      type: 'text',
      title: "Industry",
      value: props.company?.information?.industry?.name,
      icon: BIconBriefcase,
    });
  }

  if (props.company?.information?.structure_type?.name) {
    data.push({
      type: 'text',
      title: "Structure",
      value: props.company?.information?.structure_type?.name,
      icon: BiconStack,
    });
  }
  
  if (props.company?.information?.country?.name) {
    data.push({
      type: 'country',
      title: "Based In",
      value: {
        name: props.company?.information?.country?.name,
        iso2: props.company?.information?.country?.iso_alpha_2,
      },
      icon: BiconGlobe,
    });
  }

  // Users stat with link
  data.push({
    type: 'text',
    title: "Users",
    tooltip: "View Company Users",
    value: props.company?.user_count ?? 0,
    icon: BIconPeople,
    link: {
      name: 'companies.users.index',
      params: { id: props.company?.id }
    },
  });

  return data;
});

</script>