<template>
  <div>
    <div class="absolute top-0 z-10 h-[26rem] w-full group">
      <transition-fade>
        <div class="absolute z-30 p-3" v-if="showBanner">
          <VTooltip :content="uploadBackgroundText" placement="right" class="hidden lg:inline">
            <VButton
              intent="secondary"
              size="icon"
              class="p-2.5 rounded-full opacity-0 group-hover:opacity-50 hover:!opacity-100 transition-opacity duration-300"
              @click="isUploadModalOpen = true"
            >
              <BIconImage class="h-4 w-4 text-primary-600 dark:text-primary-400" />
            </VButton>
          </VTooltip>
        </div>
      </transition-fade>
      <div class="relative h-full w-full banner-fade-mask">
        <transition name="bannerFade">
          <div
            v-if="imageLoaded && showBanner && show"
            class="absolute z-0 h-full w-full bg-cover bg-center"
            :style="{ backgroundImage: `url(${src})` }"
          ></div>
          <div
            v-else-if="showBanner && show"
            class="absolute z-0 h-full w-full bg-cover bg-center banner-fade-mask"
            :style="{ backgroundImage: `url('data:image/svg+xml;utf8,${svgContent}')` }"
          ></div>
        </transition>
        <img :src="src" @load="onImageLoad" class="hidden" alt="banner image" />
      </div>
    </div>

    <VModal
      v-model:open="isUploadModalOpen"
      :title="uploadBackgroundText"
      :icon="BIconImage"
      showClose
      :showCancel="false"
      :showConfirm="true"
      @confirmed="handleConfirm"
    >
      <template #description>
        <div class="space-y-2 w-full">
          <VFileUpload
            maxSize="10"
            @handleFiles="alert(1)"
          />
        </div>
      </template>
    </VModal>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted, computed } from 'vue';
import { generateSvg } from '@/util/imageUtils';
import layout from '@/store/layout';
import BIconImage from '~icons/bi/image';

const props = defineProps({
  src: {
    type: String,
    default: null,
  },
  show: {
    type: Boolean,
    default: true,
  },
});

const isUploadModalOpen = ref(false);
const imageLoaded = ref(false);
const svgContent = ref(null);
const computedStyle = ref(getComputedStyle(document.documentElement).getPropertyValue('--primary-500'));
const showBanner = ref(false);
const onImageLoad = () => {
  imageLoaded.value = true;
};

onMounted(() => {
  // Create a mutation observer to watch for theme changes.
  const observer = new MutationObserver(() => {
    computedStyle.value = getComputedStyle(document.documentElement).getPropertyValue('--primary-500');
  });
  observer.observe(document.documentElement, { attributes: true, attributeFilter: ['style', 'class'] });
  onUnmounted(() => observer.disconnect());

  generateSvgContent(); // The default banner image.

  // src is set, attempt to load the image.
  if (props.src) {
    const img = new Image();
    img.src = props.src;
    img.onload = onImageLoad;
  }

  // Regardless of src, load the banner image after a short delay.
  setTimeout(() => {
    showBanner.value = true;
  }, 500);
});

// Regenerate if dark mode is toggled or the theme is changed.
watch([layout.getters.isDarkMode, computedStyle], () => {
  if (!props.src) {
    generateSvgContent();
  }
});

const uploadBackgroundText = computed(() => {
  return imageLoaded.value ? 'Replace Banner Image' : 'Upload Banner Image';
});

const handleConfirm = () => {
  // TODO: Handle the file upload confirmation
  isUploadModalOpen.value = false;
};

// Generate a SVG banner image using utility function.
const generateSvgContent = () => {
  svgContent.value = generateSvg(layout.getters.isDarkMode(), {
    width: 1920,
    height: 500,
    variance: 0.75,
    encodeUri: false
  });
};
</script>

<style scoped>
.bannerFade-enter-active,
.bannerFade-leave-active {
  transition: opacity 1.5s ease-in-out 0.5s;
}

.bannerFade-enter-from,
.bannerFade-leave-to {
  opacity: 0;
}

.banner-fade-mask {
  mask-image: linear-gradient(to bottom,
    rgba(0, 0, 0, 1) 0%,
    rgba(0, 0, 0, 0.3) 70%,
    rgba(0, 0, 0, 0) 100%
  );
  -webkit-mask-image: linear-gradient(to bottom,
    rgba(0, 0, 0, 1) 0%,
    rgba(0, 0, 0, 0.3) 70%,
    rgba(0, 0, 0, 0) 100%
  );
}
</style>
