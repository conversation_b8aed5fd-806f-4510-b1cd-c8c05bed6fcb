<template>
  <div class="space-y-1 h-full flex flex-col">
    <div class="flex items-center gap-1.5 justify-center pb-1">
      <component
        :is="item.icon"
        class="text-primary-400 dark:text-primary-600 h-3 w-3 transition-colors"
        :class="item?.link ? 'group-hover:text-primary-500 dark:group-hover:text-primary-400' : ''"
      />
      <span
        class="text-xxs font-semibold uppercase tracking-wide transition-colors"
        :class="[
            'text-platinum-900 dark:text-midnight-100',
            item?.link ? 'group-hover:text-platinum-950 dark:group-hover:text-midnight-50' : '',
        ]"
      >
        {{ item?.title }}
      </span>
    </div>
    <div class="flex-1 flex items-center justify-center w-full text-center font-semibold group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors"
     :class="[valueLengthClass, 'text-base text-primary-500 dark:text-primary-500']">

      <template v-if="item.type === 'country'">
        <span class="inline-flex items-center gap-2 align-middle -mt-1">
          <VCountryField :iso2="item.value.iso2" :showValue="false" class="-mt-0.5" />
          {{ item.value.name }}
        </span>
      </template>
      <template v-else>
        {{ item.value }}
      </template>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
const props = defineProps({
    item: {
        type: Object,
        required: true,
    },
});

const valueLengthClass = computed(() => {
  let value = '';
  if (props.item.type === 'country' && props.item.value && props.item.value.name) {
    value = props.item.value.name;
  } else if (typeof props.item.value === 'string' || typeof props.item.value === 'number') {
    value = String(props.item.value);
  }
  return value.length < 12 ? 'lg:text-lg' : 'lg:text-sm';
});

</script>