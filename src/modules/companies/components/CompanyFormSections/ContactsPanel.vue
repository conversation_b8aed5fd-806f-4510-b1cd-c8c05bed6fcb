<template>
  <FormPanel
    :show="company?.contacts?.length || company?.socials?.length"
    :required="company?.contacts?.length || company?.socials?.length"
  >
    <template #default>
      <div>
        <h2 class="text-primary-500">Contact Methods</h2>
        <p class="mt-1 text-sm">Standard contact methods which would be used for direct communication with a company. These references are not visible without a granted permission. At least one contact is required.</p>
      </div>

      <AccordionContainer
        :opened="contactAccordions.filter((accordion) => accordion.isOpen).map((accordion) => accordion.id)"
        @change="openContact"
      >
        <Accordion
          v-for="({ resource: contact, id }, index) in contactAccordions"
          :id="id"
          :key="id"
        >
          <template #header>
            <div class="flex w-full justify-between">
              <span>{{ 'Contact #' + (index + 1) }}</span>

              <div class="flex items-center space-x-2">
                <VBadge 
                  v-if="form.errors.has(`contacts.${index}`)"
                  color="danger"
                >Requires Attention</VBadge>
                <VBadge
                  v-if="contact.is_primary"
                  color="secondary"
                >Primary</VBadge>
              </div>
            </div>
          </template>
          <template #body>
            <div class="mb-2 grid grid-cols-6 gap-6 p-3">
              <VField
                class="col-span-6 sm:col-span-4 xl:col-span-5"
                required
                :errors="form.errors.get(`contacts.${index}.method_id`)"
                label="Contact Method"
              >
                <ContactMethodCombobox
                  v-model="contact.method_id"
                  contactable="Company"
                />
              </VField>

              <VField class="col-span-6 sm:col-span-2 xl:col-span-1">
                <template #label>
                  <span class="block text-sm font-medium text-primary-500">Primary Contact</span>
                </template>

                <VToggle
                  v-model="contact.is_primary"
                  :disabled="contact.is_primary"
                  class="mt-2"
                  @update:model-value="
                    (value) => {
                      updateContacts({ is_primary: false });
                      updateContact(id, { is_primary: true });
                    }
                  "
                >
                  {{ contact.is_primary ? 'Primary' : 'Secondary' }}
                </VToggle>
              </VField>

              <VField
                class="col-span-6"
                label="Contact Value"
                required
                :errors="form.errors.get(`contacts.${index}.value`)"
              >
                <VInput
                  v-model="contact.value"
                  placeholder="Enter Contact Value"
                />
              </VField>

              <VField
                class="col-span-6"
                label="Contact Note"
                help="Additional notes about a contact. Example; telephone line extension or receptionist name."
                :errors="form.errors.get(`contacts.${index}.name`)"
              >
                <VTextarea
                  v-model="contact.note"
                  placeholder="Enter Contact Value"
                />
              </VField>

              <div
                v-if="!contact.is_primary"
                class="col-span-6"
              >
                <VButton
                  intent="danger"
                  @click="removeContact(id)"
                >
                  <template #start>
                    <BiTelephoneX />
                  </template>
                  Remove Contact
                </VButton>
              </div>
            </div>
          </template>
        </Accordion>
      </AccordionContainer>

      <div class="sm:flex sm:justify-between">
        <VButton
          class="w-full sm:w-auto"
          intent="primary"
          @click="
            createContact(
              {
                is_primary: false,
              },
              true
            )
          "
        >
          <BiTelephonePlus class="mr-2 inline" />Add Contact
        </VButton>

        <ConfirmModal @confirm="clearContacts(1, { is_primary: true })">
          <template #title>Confirm Action</template>

          <template #body>
            <p>You're about to clear all contacts. Please confirm your action.</p>
          </template>

          <VButton
            class="mt-2 w-full sm:ml-3 sm:mt-0 sm:w-auto"
            intent="danger"
          >
            <template #start>
              <BiTelephoneX />
            </template>
            Clear All Contacts
          </VButton>
        </ConfirmModal>
      </div>

      <hr class="mb-6 border-platinum-300 dark:border-midnight-700" />

      <div>
        <h2 class="text-primary-500">Social Media Connections</h2>
        <p class="mt-1 text-sm">Optionally you may also provide social media references for a company. These will be displayed against a company profile.</p>
      </div>

      <AccordionContainer
        :opened="socialAccordions.filter((accordion) => accordion.isOpen).map((accordion) => accordion.id)"
        @change="openSocial"
      >
        <Accordion
          v-for="({ resource: social, id }, index) in socialAccordions"
          :id="id"
          :key="id"
        >
          <template #header>
            <div class="flex w-full justify-between">
              <span>{{ social.name ? social.name : 'Social #' + (index + 1) }}</span>

              <div class="flex items-center space-x-2">
                <VBadge
                  v-if="form.errors.has(`socials.${index}`)"
                  color="danger"
                >Requires Attention</VBadge>
              </div>
            </div>
          </template>

          <template #body>
            <div class="mb-2 grid grid-cols-6 gap-6 p-3">
              <VField
                class="col-span-6"
                required
                :errors="form.errors.get(`socials.${index}.method_id`)"
                label="Social Media Platform"
              >
                <ContactMethodCombobox
                  v-model="social.method_id"
                  contactable="Company"
                  social
                />
              </VField>

              <VField
                class="col-span-6"
                label="Social Handle Value"
                required
                :errors="form.errors.get(`socials.${index}.value`)"
              >
                <VInput
                  v-model="social.value"
                  placeholder="Enter Handle Value"
                />
              </VField>

              <div class="col-span-6">
                <VButton
                  intent="danger"
                  size="sm"
                  @click="removeSocial(id)"
                >
                  <template #start>
                    <BiNodeMinus />
                  </template>
                  Remove Social Reference
                </VButton>
              </div>
            </div>
          </template>
        </Accordion>
      </AccordionContainer>

      <div class="sm:flex sm:justify-between">
        <VButton
          class="w-full sm:w-auto"
          intent="primary"
          @click="createSocial({}, true)"
        >
          <template #start>
            <BiNodePlus />
          </template>
          Add Social
        </VButton>

        <ConfirmModal @confirm="clearSocials(0)">
          <template #title>Confirm Action</template>
          <template #body>
            <p>You're about to clear all social media references. Please confirm your action.</p>
          </template>
          <VButton
            class="mt-2 w-full sm:ml-3 sm:mt-0 sm:w-auto"
            intent="danger"
          >
            <template #start>
              <BiNodeMinus />
            </template>
            Clear All Socials
          </VButton>
        </ConfirmModal>
      </div>
    </template>
  </FormPanel>
</template>

<script setup>
import { onMounted } from 'vue';

import Accordion from '@/components/ui/accordions/Accordion/Accordion.vue';
import AccordionContainer from '@/components/ui/accordions/Accordion/AccordionContainer.vue';

import ContactMethodCombobox from '@/components/ui/form/composables/ContactMethodCombobox.vue';
import FormPanel from '@/components/ui/forms/tabbed/FormPanel.vue';
import ConfirmModal from '@/components/ui/modals/ConfirmModal.vue';
import { useResourceAccordion } from '@/hooks/form';

import BiNodeMinus from '~icons/bi/node-minus';
import BiNodePlus from '~icons/bi/node-plus';
import BiTelephonePlus from '~icons/bi/telephone-plus';
import BiTelephoneX from '~icons/bi/telephone-x';
import { useVModel } from '@/hooks/model';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
  company: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(['update:modelValue']);

const form = useVModel(props, 'modelValue', emit, {
  passive: true,
  deep: true,
});

const {
  accordions: contactAccordions,
  create: createContact,
  remove: removeContact,
  clear: clearContacts,
  updateResource: updateContact,
  updateResources: updateContacts,
  open: openContact,
} = useResourceAccordion(
  {
    method_id: '',
    value: '',
    note: '',
    is_primary: false,
  },
  (accordions) => (form.value.contacts = accordions.map((accordion) => accordion.resource))
);

const {
  accordions: socialAccordions,
  create: createSocial,
  remove: removeSocial,
  clear: clearSocials,
  open: openSocial,
} = useResourceAccordion(
  {
    method_id: '',
    value: '',
  },
  (accordions) => (form.value.socials = accordions.map((accordion) => accordion.resource))
);

onMounted(() => {
  if (!props.company) {
    createContact(
      {
        is_primary: true,
      },
      true
    );
    return;
  }

  props.company.contacts.forEach((contact) => createContact(contact));
  props.company.socials.forEach((social) => createSocial(social));
});

form.value.succeeded(() => {
  if (props.company) return;

  clearSocials(0);

  clearContacts(0);
  createContact(
    {
      is_primary: true,
    },
    true
  );
});
</script>
