<template>
  <FormPanel :show="!!company?.roles?.length" :required="!!company?.roles?.length">
    <template #default>
      <div class="grid grid-cols-6 gap-6">
        <VField
          class="col-span-6"
          :errors="form.errors.get('roles')"
          label="Company Roles"
          required
        >
          <RolesCombobox v-model="form.roles" roleable="Company" />
        </VField>
      </div>
    </template>
  </FormPanel>
</template>

<script setup>
import RolesCombobox from '@/components/ui/form/composables/RolesCombobox.vue';
import FormPanel from '@/components/ui/forms/tabbed/FormPanel.vue';
import { useVModel } from '@/hooks/model';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
  company: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(['update:modelValue']);

const form = useVModel(props, 'modelValue', emit, {
  passive: true,
  deep: true,
});
</script>
