<template>
  <FormPanel>
    <template #default>
      <div class="grid grid-cols-3 gap-6 lg:grid-cols-6">
        <VField
          label="Company Name"
          class="col-span-3 lg:col-span-6"
          
          :errors="form.errors.get('name')"
        >
          <VInput v-model="form.general.name" placeholder="Enter Company Name" />
        </VField>

        <VField
          class="col-span-3"
          required
          label="Owning User"
          :errors="form.errors.get('owner_id')"
        >
          <UserCombobox v-model="form.general.owner_id" />
        </VField>

        <VField
          class="col-span-3"
          required
          :errors="form.errors.get('status_id')"
          label="Status"
        >
          <StatusCombobox
            v-model="form.general.status_id"
            statusable="Company"
            default-option="Active"
          />
        </VField>
      </div>
    </template>

    <template #footer>
      <p>
        <BIconArrowUp class="mr-2 -mt-0.5 inline lg:hidden" />
        <BIconArrowLeft class="mr-2 -mt-0.5 hidden lg:inline" />
        Configure further company values using the optional checkboxes in the menu or continue and add the user immediately
      </p>
    </template>
  </FormPanel>
</template>

<script setup>
import BIconArrowLeft from '~icons/bi/arrow-left';
import BIconArrowUp from '~icons/bi/arrow-up';

import StatusCombobox from '@/components/ui/form/composables/StatusCombobox.vue';
import UserCombobox from '@/components/ui/form/composables/UserCombobox.vue';
import FormPanel from '@/components/ui/forms/tabbed/FormPanel.vue';
import { useVModel } from '@/hooks/model';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['update:modelValue']);

const form = useVModel(props, 'modelValue', emit, {
  passive: true,
  deep: true,
});
</script>
