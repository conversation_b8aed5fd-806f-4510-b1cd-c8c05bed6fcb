<template>
  <FormPanel :show="form.information?.legal_name" :required="form.information?.legal_name">
    <template #default>
      <div class="grid grid-cols-3 gap-6 md:grid-cols-6">
        <VField class="col-span-6" label="Company Legal Name" :errors="form.errors.get('information.legal_name')"
          required>
          <VInput v-model="form.information.legal_name" placeholder="Enter Company Legal Name" />
        </VField>

        <VField class="col-span-6 md:col-span-3" label="Country Based"
          :errors="form.errors.get('information.country_id')" required>
          <CountryCombobox v-model="form.information.country_id" />
        </VField>

        <!-- <VField class="col-span-6 md:col-span-3" label="Company Type"
          :errors="form.errors.get('information.structure_type_id')" required>
          <CompanyStructureTypeCombobox v-model="form.information.structure_type_id" />
        </VField> -->

        <VField class="col-span-6" label="Company Description" :errors="form.errors.get('information.description')">
          <VTextarea v-model="form.information.description" placeholder="Enter Company Description" />
        </VField>

        <VField class="col-span-6 md:col-span-3" label="Founded Year"
          :errors="form.errors.get('information.founded_year')">
          <VInput v-model="form.information.founded_year" type="number" min="1000" :max="new Date().getFullYear()"
            placeholder="Enter Founded Year" />
        </VField>

        <VField class="col-span-6 md:col-span-3" label="Timezone" :errors="form.errors.get('information.timezone_id')">
          <TimezoneCombobox v-model="form.information.timezone_id" placeholder="Select Local Timezone"
            value-prop="locked_id" />
        </VField>

        <VField class="col-span-6" label="Tagline / Slogan" :errors="form.errors.get('information.tagline_slogan')">
          <VTextarea v-model="form.information.tagline_slogan" placeholder="Enter Company Tagline or Slogan" />
        </VField>

        <VField class="col-span-6" label="Website URL" :errors="form.errors.get('information.website_url')">
          <VInput v-model="form.information.website_url" placeholder="Enter Website URL" />
        </VField>

        <VField class="col-span-6" label="VAT Number" :errors="form.errors.get('information.vat_number')">
          <VInput v-model="form.information.vat_number" placeholder="Enter VAT Number" />
        </VField>

        <VField class="col-span-6" label="Company Logo" :errors="form.errors.get('information.logo')">
          <VFile
            name="logo"
            :model-value="form.information.logo"
            @change="(value) => form.information.logo = value"
          />
        </VField>

        <VField
          class="col-span-6"
          label="Company Banner"
          :errors="form.errors.get('information.banner')"
        >
          <VFile
            name="banner"
            v-model="form.information.banner"
          />
        </VField>

        
      </div>
    </template>
  </FormPanel>
</template>

<script setup>
import CompanyStructureTypeCombobox from '@/components/ui/form/composables/CompanyStructureTypeCombobox.vue';
import CountryCombobox from '@/components/ui/form/composables/CountryCombobox.vue';
import TimezoneCombobox from '@/components/ui/form/composables/TimezoneCombobox.vue';
import FormPanel from '@/components/ui/forms/tabbed/FormPanel.vue';
import VFile from '@/components/ui/form/VFile.vue';
import { useVModel } from '@/hooks/model';
const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
  company: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(['update:modelValue']);

const form = useVModel(props, 'modelValue', emit, {
  passive: true,
  deep: true,
});

</script>
