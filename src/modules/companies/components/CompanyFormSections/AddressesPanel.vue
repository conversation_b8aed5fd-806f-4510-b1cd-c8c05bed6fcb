<template>
  <FormPanel :show="!!company?.addresses?.length" :required="!!company?.addresses?.length">
    <template #default>
      <AccordionContainer
        :opened="accordions.filter((accordion) => accordion.isOpen).map((accordion) => accordion.id)"
        @change="openAddress"
      >
        <Accordion v-for="({ resource: address, id }, index) in accordions" :id="id" :key="id">
          <template #header>
            <div class="flex w-full justify-between">
              <span>{{ address.name ? address.name : 'Address #' + (index + 1) }}</span>

              <div class="flex items-center space-x-2">
                <VBadge
                  v-if="form.errors.has(`addresses.${index}`)"
                  color="danger"
                >Requires Attention</VBadge>
                <VBadge
                  v-if="address.is_primary"
                  color="secondary"
                >Primary</VBadge>
              </div>
            </div>
          </template>
          <template #body>
            <div class="mb-2 grid grid-cols-6 gap-6 p-3">
              <VField
                class="col-span-6 sm:col-span-4 xl:col-span-5"
                label="Identifiable Address Name"
                required
                :errors="form.errors.get(`addresses.${index}.name`)"
              >
                <VInput v-model="address.name" placeholder="Name" />
              </VField>

              <VField
                class="col-span-6 sm:col-span-2 xl:col-span-1"
                label="Primary Address"
                required
              >
                <VToggle
                  v-model="address.is_primary"
                  :disabled="address.is_primary"
                  class="mt-2"
                  @update:model-value="
                    (value) => {
                      updateAddresses({ is_primary: false });
                      updateAddress(id, { is_primary: true });
                    }
                  "
                >
                  {{ address.is_primary ? 'Primary' : 'Secondary' }}
                </VToggle>
              </VField>

              <VField
                class="col-span-6"
                label="Street Address / Address Line 1"
                required
                :errors="form.errors.get(`addresses.${index}.address_line_1`)"
              >
                <VInput v-model="address.address_line_1" placeholder="Enter Address Line 1" />
              </VField>

              <VField
                class="col-span-6"
                label="Address Line 2"
                :errors="form.errors.get(`addresses.${index}.address_line_2`)"
              >
                <VInput v-model="address.address_line_2" placeholder="Enter Address Line 2" />
              </VField>

              <VField
                class="col-span-6"
                label="Address Line 3"
                :errors="form.errors.get(`addresses.${index}.address_line_3`)"
              >
                <VInput v-model="address.address_line_3" placeholder="Enter Address Line 3" />
              </VField>

              <VField
                class="col-span-6 lg:col-span-3"
                label="Country"
                :errors="form.errors.get(`addresses.${index}.country_id`)"
                required
              >
                <CountryCombobox v-model="address.country_id" />
              </VField>

              <VField
                class="col-span-6 sm:col-span-6 lg:col-span-3"
                label="City"
                :errors="form.errors.get(`addresses.${index}.city`)"
                required
              >
                <VInput id="city" v-model="address.city" type="text" placeholder="Enter City" />
              </VField>

              <VField
                class="col-span-6 sm:col-span-3"
                label="State / Province / Region"
                :errors="form.errors.get(`addresses.${index}.state_county_region`)"
              >
                <VInput v-model="address.state_county_region" placeholder="Enter State, Province or Region" />
              </VField>

              <VField
                class="col-span-6 sm:col-span-3"
                label="Postcode / Zip"
                required
                :errors="form.errors.get(`addresses.${index}.postcode_zipcode`)"
              >
                <VInput v-model="address.postcode_zipcode" placeholder="Enter Postcode or Zip" />
              </VField>
              <div class="col-span-6" v-if="!address.is_primary">
                <VButton
                  intent="danger"
                  size="sm"
                  @click="() => removeAddress(id)"
                >
                  <template #start>
                    <BiHouseX />
                  </template>
                  Remove Address
                </VButton>
              </div>
            </div>
          </template>
        </Accordion>
      </AccordionContainer>
      <div class="sm:flex sm:justify-between">
        <VButton
          class="w-full sm:w-auto"
          size="sm"
          intent="primary"
          @click="
            () =>
              createAddress(
                {
                  is_primary: false,
                },
                true
              )
          "
        >
          <template #start>
            <BiHouseAdd />
          </template>
          Add Address
        </VButton>

        <ConfirmModal @confirm="clearAddresses(1, { is_primary: true })">
          <template #title>Confirm Action</template>
          <template #body>
            <p>You're about to clear all addresses. Please confirm!</p>
          </template>
          <VButton
            class="mt-2 w-full sm:ml-3 sm:mt-0 sm:w-auto"
            intent="danger"
            size="sm"
          >
            <template #start>
              <BiHouseX />
            </template>
            Clear All Addresses
          </VButton>
        </ConfirmModal>
      </div>
    </template>
  </FormPanel>
</template>

<script setup>
import { useQuery } from '@tanstack/vue-query';
import { onMounted } from 'vue';

import Accordion from '@/components/ui/accordions/Accordion/Accordion.vue';
import AccordionContainer from '@/components/ui/accordions/Accordion/AccordionContainer.vue';
import CountryCombobox from '@/components/ui/form/composables/CountryCombobox.vue';
import FormPanel from '@/components/ui/forms/tabbed/FormPanel.vue';
import ConfirmModal from '@/components/ui/modals/ConfirmModal.vue';
import { useResourceAccordion } from '@/hooks/form';
import { useVModel } from '@/hooks/model';
import BiHouseAdd from '~icons/bi/house-add';
import BiHouseX from '~icons/bi/house-x';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
  company: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(['update:modelValue']);

const form = useVModel(props, 'modelValue', emit, {
  passive: true,
  deep: true,
});

const {
  accordions,
  create: createAddress,
  remove: removeAddress,
  clear: clearAddresses,
  updateResource: updateAddress,
  updateResources: updateAddresses,
  open: openAddress,
} = useResourceAccordion(
  {
    name: '',
    address_line_1: '',
    address_line_2: '',
    address_line_3: '',
    country_id: '',
    city: '',
    state_county_region: '',
    postcode_zipcode: '',
    is_primary: false,
  },
  (accordions) => (form.value.addresses = accordions.map((accordion) => accordion.resource))
);

onMounted(() => {
  if (!props.company) {
    createAddress(
      {
        is_primary: true,
      },
      true
    );
    return;
  }

  props.company.addresses.forEach((address) => createAddress(address));
});

form.value.succeeded(() => {
  if (props.company) return;

  clearAddresses(0);
  createAddress(
    {
      is_primary: true,
    },
    true
  );
});
</script>
