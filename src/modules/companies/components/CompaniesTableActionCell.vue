<template>
  <div class="flex items-center justify-end space-x-1.5">
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <VButton intent="table">
          <BIconThreeDots class="h-5 w-5" />
        </VButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem as-child>
          <RouterLink
            :to="{ name: 'companies.profile', params: { id: row.locked_id } }"
            class="w-full"
          >
            <BIconBuildingsFill />Profile
          </RouterLink>
        </DropdownMenuItem>
        <DropdownMenuItem as-child>
          <RouterLink
            :to="{ name: 'companies.view', params: { id: row.locked_id } }"
            class="w-full"
          >
            <BIconSearch />View
          </RouterLink>
        </DropdownMenuItem>

        <DropdownMenuItem as-child>
          <RouterLink
            :to="{ name: 'companies.activity', params: { id: row.locked_id } }"
            class="w-full"
          >
            <BIconActivity />Activity
          </RouterLink>
        </DropdownMenuItem>

        <DropdownMenuGroup>
          <DropdownMenuLabel>Modify</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem as-child>
            <RouterLink
              :to="{
                name: 'companies.update',
                params: { id: row.locked_id },
              }"
              class="w-full"
            >
              <BIconPencilSquare />Edit
            </RouterLink>
          </DropdownMenuItem>
          <DropdownMenuItem @click="openCompanyDeleteConfirmationDialog">
            <BIconTrash />Delete
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>

    <VConfirmModal
      v-model:open="isCompanyDeleteConfirmationDialog"
      @confirmed="deleteCompanyMutation.mutate({
        companyId: props.row.locked_id
      })"
      title="Confirm Company Deletion"
      :description="`<p>You're about to delete the company: <b>${props.row.name}</b>.</p><p>Please confirm you wish to proceed.</p>`"
      :icon="BIconTrashFill"
    />
  </div>
</template>

<script setup lang="ts">
import BIconSearch from '~icons/bi/search';
import BIconThreeDots from '~icons/bi/three-dots';
import BIconPencilSquare from '~icons/bi/pencil-square';
import BIconTrash from '~icons/bi/trash';
import BIconBuildingsFill from '~icons/bi/buildings-fill';
import BIconTrashFill from '~icons/bi/trash-fill';
import BIconActivity from '~icons/bi/activity';
import { computed } from 'vue';

import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/shadcn/dropdown-menu';
import { useDisclosure } from '@/hooks';
import type { CellContext } from '@tanstack/vue-table';
import type { Company } from '@/types/api';
import { useDeleteCompany } from '@/modules/companies/api/delete-company';


interface CompanyRow {
  locked_id: string;
  name: string;
  [key: string]: any;
}

const props = defineProps<{
  row: CompanyRow;
}>();

// Computed property to get the company's full name with fallback.
const companyName = computed(() => {
  return props.row?.name ? props.row?.name : 'Undefined';
});

const deleteCompanyMutation = useDeleteCompany({
  mutationConfig: {
    onSuccess: () => {
      Reshape.toast(
        {
          title: 'Company Deleted',
          message: `The requested company <b>${companyName.value}</b> was successfully deleted.`,
        },
        'success'
      );
    },
  },
});

const {
  isOpen: isCompanyDeleteConfirmationDialog,
  open: openCompanyDeleteConfirmationDialog,
} = useDisclosure(false);

</script>