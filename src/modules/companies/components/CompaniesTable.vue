<template>
  <Table
    v-bind="$attrs"
    :columns="columns"
    :default-sort="[{
      id: 'created_at',
      desc: true,
    }]"
    paginated
    sortable
    searchable
    controlled
  />
</template>

<script setup>
import { h, ref } from 'vue';

import Table from '@/components/ui/tables/Table.vue';
import { humanizeTime } from '@/util/dateUtils';

import ActionCell from './CompaniesTableActionCell.vue';
import CompanyStatusBadge from '@/modules/companies/components/CompanyStatusBadge.vue';
import CountryCell from './CompaniesTableCountryCell.vue';
import EntityLink from '@/components/ui/links/EntityLink.vue';

const columns = ref([
  {
    accessorKey: 'name',
    header: 'Name',
    cell: (info) => {
      return h(EntityLink, {
        value: info.getValue(),
        entityType: 'Company',
        entityId: info.row.original.locked_id,
        valueClass: 'max-w-32 sm:max-w-48 md:max-w-52 lg:max-w-64 xl:max-w-72 truncate',
        showAvatar: true,
        avatarSize: 'sm',
      })
    }
  },
  {
    accessorFn: (row) => row?.information?.industry?.name,
    id: 'information.industry.name',
    header: 'Industry',
    cell: (info) => info.getValue(),
  },
  {
    accessorFn: (row) => row?.information?.country?.name,
    id: 'information.country.name',
    header: 'Country Based',
    cell: (info) => h(CountryCell, { row: info.row.original }),
  },
  {
    accessorKey: 'created_at',
    header: 'Created Date',
    cell: (info) => humanizeTime(info.getValue()),
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: (info) => h(CompanyStatusBadge, { status: info.row.original.status.name }),
  },
  {
    id: 'actions',
    cell: (info) => h(ActionCell, { row: info.row.original }),
    enableSorting: false,
    enableResizing: false,
    size: 50,
  },
]);
</script>
