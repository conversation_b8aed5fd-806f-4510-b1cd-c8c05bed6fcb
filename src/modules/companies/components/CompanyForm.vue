<template>
  <form id="company-form" @submit.prevent="submit">
    <FormStructure v-model:sections="sections">
      <CompanyFormGeneralPanel v-model="form" name="general" label="Basic Information" title="Basic Information"
        description="The bare minimum required information for a company to be created. Everything else is optional and can be
          completed later." required :icon="icons.required" show />

      <CompanyFormInformationPanel v-model="form" name="information" label="More Information" title="More Information"
        description="Details in relation to the new company. We recommend completing as many fields as possible."
        :icon="icons.details" :required="!!company?.information" :show="!!company?.information" />

      <CompanyFormAddressesPanel v-model="form" :company="company" name="addresses" label="Addresses" title="Addresses"
        description="Any addresses associated with this company." :icon="icons.addresses" />

      <CompanyFormContactsPanel v-model="form" :company="company" name="contacts" label="Contacts" title="Contacts"
        description="Setup company contact methods and other connections, including external social media references."
        :icon="icons.contacts" />

      <CompanyFormRolesPanel v-model="form" :company="company" name="roles" label="Permission Roles"
        title="Permission Roles"
        description="Assign default roles that apply to this company. Keep in mind that user level roles can override these configurations."
        :icon="icons.permissions" />

      <FormPanel name="preferences" label="Company Preferences" :icon="icons.preferences" disabled>
        <template #header>
          <h3 class="text-lg font-medium leading-6 text-gray-900">Company Preferences</h3>

          <p class="mt-1 text-sm text-gray-500">Configurable preferences in relation to this company.</p>
        </template>

        <template #body>
          <div class="grid grid-cols-3 gap-6">
            <p>Company preferences.</p>
          </div>
        </template>
      </FormPanel>

      <template #actions v-if="$slots.actions">
        <slot name="actions" />
      </template>
    </FormStructure>
  </form>
</template>

<script setup>
import { markRaw, ref } from 'vue';

import FormPanel from '@/components/ui/forms/tabbed/FormPanel.vue';
import FormStructure from '@/components/ui/forms/tabbed/FormStructure.vue';
import { useForm } from '@/hooks/form';
import CompanyFormAddressesPanel from '@/modules/companies/components/CompanyFormSections/AddressesPanel.vue';
import CompanyFormContactsPanel from '@/modules/companies/components/CompanyFormSections/ContactsPanel.vue';
import CompanyFormGeneralPanel from '@/modules/companies/components/CompanyFormSections/GeneralPanel.vue';
import CompanyFormInformationPanel from '@/modules/companies/components/CompanyFormSections/InformationPanel.vue';
import CompanyFormRolesPanel from '@/modules/companies/components/CompanyFormSections/RolesPanel.vue';
import BIconBuildingFillExclamation from '~icons/bi/building-fill';
import BIconChatQuoteFill from '~icons/bi/chat-quote-fill';
import BIconPaletteFill from '~icons/bi/palette-fill';
import BIconPatchQuestionFill from '~icons/bi/patch-question-fill';
import BIconPinMapFill from '~icons/bi/pin-map-fill';
import BIconUnlockFill from '~icons/bi/unlock-fill';
import _ from 'lodash';

const props = defineProps({
  company: {
    type: Object,
    default: null,
  },
});

const emits = defineEmits(['submit']);

const sections = ref([]);
const logo = ref(null);
const banner = ref(null);

const icons = {
  required: markRaw(BIconBuildingFillExclamation),
  details: markRaw(BIconPatchQuestionFill),
  addresses: markRaw(BIconPinMapFill),
  contacts: markRaw(BIconChatQuoteFill),
  permissions: markRaw(BIconUnlockFill),
  preferences: markRaw(BIconPaletteFill),
};

const form = useForm({
  general: {
    name: props.company?.name ?? '',
    status_id: props.company?.status?.locked_id ?? '',
    owner_id: props.company?.owner?.locked_id ?? '',
  },
  information: {
    legal_name: props.company?.information?.legal_name ?? '',
    description: props.company?.information?.description ?? '',
    tagline_slogan: props.company?.information?.tagline_slogan ?? '',
    country_id: props.company?.information?.country?.locked_id ?? '',
    timezone_id: props.company?.information?.timezone?.locked_id ?? '',
    founded_year: props.company?.information?.founded_year ?? '',
    structure_type_id: props.company?.information?.structure_type_id ?? '',
    vat_number: props.company?.information?.vat_number ?? '',
    website_url: props.company?.information?.website_url ?? '',
    banner: props.company?.information?.media?.banner?.conversions?.original ?? '',
  },
  addresses: [],
  contacts: [],
  socials: [],
  roles: props.company?.roles?.map((role) => role.locked_id),
});

const isUrl = string => {
  try { return Boolean(new URL(string)); }
  catch (e) { return false; }
}

const submit = () =>
  emits(
    'submit',
    form.value.transform((data) => {
      const enabled = sections.value.filter((section) => section.show).map((section) => section.name);

      let payload = {};

      if (enabled.includes('general')) {
        payload = {
          ...payload,
          ...data.general,
        };
      }

      if (enabled.includes('information')) {
        payload = {
          ...payload,
          information: data.information,
        };
      }

if (enabled.includes('addresses')) {
  payload = {
    ...payload,
    addresses: data.addresses,
  };
}

if (enabled.includes('contacts')) {
  payload = {
    ...payload,
    contacts: data.contacts,
    socials: data.socials,
  };
}

if (enabled.includes('roles')) {
  payload = {
    ...payload,
    roles: data.roles,
  };
}

if (logo.value?.files && payload.information) {
  payload.information.logo = logo.value.files[0] ?? null;
}

if (banner.value?.files && payload.information) {
  payload.information.banner = banner.value.files[0] ?? null;
}

return payload;
    })
  );
</script>
