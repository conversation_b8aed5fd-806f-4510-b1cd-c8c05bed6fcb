<template>
  <VInformationPanel :loading="isCompanyLoading">
    <VDefaultField label="Name" :value="company.name" />
    <VDefaultField label="Company Structure Type" :value="company.information?.structure_type?.name" />
    <VDefaultField label="Legal Name" :value="company.information?.legal_name" />
    <VDefaultField label="Owning User" :value="owningUser" />
    <VCountryField
      label="Country Based"
      :value="company.information?.country.name"
      :iso2="company.information?.country.iso_alpha_2"
    />
    <VDefaultField label="Website URL" :value="company.information?.website_url" />
    <VTimeField label="Created At" :value="company.created_at" />
    <VTimeField label="Updated At" :value="company.updated_at" />
  </VInformationPanel>
</template>

<script setup>
import { useQuery } from '@tanstack/vue-query';
import { computed } from 'vue';
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

const { data: company, isLoading: isCompanyLoading } = useQuery({
  queryKey: ['company-information-panel', props.id],
  queryFn: () =>
    Reshape.http()
      .get(`/api/companies/${props.id}`)
      .then(({ data }) => data),
});

const owningUser = computed(() => {
  const names = [company.value?.owner?.first_name, company.value?.owner?.middle_names, company.value?.owner?.last_name];
  return names.filter(Boolean).join(' ');
});
</script>
