<script>
export default {
  props: ['row'],
};
</script>

<template>
  <div v-if="row?.information?.country?.name" class="flex items-center space-x-2">
    <img
      v-if="row.information.country.iso_alpha_2"
      class="h-5 w-5"
      :src="`https://flagicons.lipis.dev/flags/4x3/${row.information.country.iso_alpha_2.toLowerCase()}.svg`"
    />
    <span>{{ row?.information?.country.name }}</span>
  </div>
  <span v-else class="text-beta-50">Undefined</span>
</template>
