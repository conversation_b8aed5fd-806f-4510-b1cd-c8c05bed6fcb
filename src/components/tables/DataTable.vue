<template>
  <div class="card overflow-hidden">
    <data-table
      class=""
      :value="value"
      :total-records="totalRecords"
      :lazy="lazy"
      :paginator="paginator"
      paginator-template="CurrentPageReport FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
      :resizable-columns="resizableColumns"
      :rows-per-page-options="rowsPerPageOptions"
      :rows="rows"
      :loading="loading"
      :show-gridlines="showGridlines"
      :striped-rows="stripedRows"
      current-page-report-template="{first}-{last} of {totalRecords} Results"
      @page="onPage"
      @sort="onSort"
    >
      <template v-if="title || searchable" #header>
        <div class="align-items-center flex justify-between">
          <h2 v-if="title">{{ title }}</h2>
          <div v-if="tableActions.length" class="flex items-center space-x-2">
            <div v-for="(action, index) in tableActions" :key="index">
              <RouterLink
                v-if="action.type == 'router-link'"
                :to="{ name: action.name }"
                class="ml-3 flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
              >
                <b-icon-person-plus-fill class="mr-2" />{{ action.label }}
              </RouterLink>
            </div>
            <div v-if="searchable" class="input-icon">
              <input
                type="text"
                class="w-full rounded-md border-gray-300 focus:border-gray-300 focus:ring-2 focus:ring-gray-400"
                placeholder="Search"
                @input="onSearch"
              />
              <span class="absolute right-7 mt-3">
                <b-icon-search class="small" />
              </span>
            </div>
          </div>
        </div>
      </template>
      <template v-if="$slots.empty" #empty>
        <slot name="empty" />
      </template>
      <template v-else #empty>No entries found.</template>
      <slot></slot>
    </data-table>
  </div>
</template>

<script setup>
import { toRefs, emit } from 'vue';
import DataTable from 'primevue/datatable';
import BIconPersonPlusFill from '~icons/bi/person-plus-fill';
import BIconSearch from '~icons/bi/search';
import { RouterLink } from 'vue-router';

const props = defineProps({
  title: String,
  tableActions: Array,
  value: {
    type: Array,
    required: true,
  },
  totalRecords: Number,
  searchable: {
    type: Boolean,
    default: true,
  },
  lazy: {
    type: Boolean,
    default: true,
  },
  paginator: {
    type: Boolean,
    default: true,
  },
  resizableColumns: {
    type: Boolean,
    default: true,
  },
  rowsPerPageOptions: {
    type: Array,
    default: () => [10, 20, 50],
  },
  rows: Number,
  loading: Boolean,
  showGridlines: {
    type: Boolean,
    default: false,
  },
  stripedRows: {
    type: Boolean,
    default: false,
  },
});

const {
  title,
  tableActions,
  value,
  totalRecords,
  searchable,
  lazy,
  paginator,
  resizableColumns,
  rowsPerPageOptions,
  rows,
  loading,
  showGridlines,
  stripedRows,
} = toRefs(props);

const onPage = (event) => {
  emit('page', event);
};

const onSort = (event) => {
  emit('sort', event);
};

const onSearch = (event) => {
  emit('search', event.target.value);
};
</script>
