<template>
  <div class="flex min-h-full items-center justify-center">
    <div class="container mx-auto p-3">
      <div
        class="mx-auto grid grid-cols-7 items-center gap-3 rounded-lg bg-platinum-300 p-6 shadow dark:bg-midnight-500"
      >
        <div class="col-span-3 hidden flex-shrink-0 p-12 lg:block xl:p-10">
          <div class="mx-auto h-auto w-full text-primary-500">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              data-name="Layer 1"
              width="850"
              height="605"
              viewBox="0 0 850 605"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              class="w-full opacity-80 transition-opacity dark:opacity-100"
            >
              <path
                d="M1020.31758,709.7164a1.00274,1.00274,0,0,1-1.286.58847L224.27089,414.56956a1,1,0,0,1,.69749-1.87443l794.76073,295.73531A1.00274,1.00274,0,0,1,1020.31758,709.7164Z"
                transform="translate(-175 -147.5)"
                fill="#f2f2f2"
              />
              <path
                d="M596.95457,147.5H407.575a56.09681,56.09681,0,0,0-56.0333,56.0333V220.651a2.60094,2.60094,0,0,0-3.2909,2.53968v13.16358a2.60094,2.60094,0,0,0,3.2909,2.53968v19.93154a2.60068,2.60068,0,0,0-3.2909,2.5396v32.909a2.60071,2.60071,0,0,0,3.2909,2.5396v12.03346a2.60068,2.60068,0,0,0-3.2909,2.5396v32.909a2.60068,2.60068,0,0,0,3.2909,2.5396V694.359a56.09681,56.09681,0,0,0,56.0333,56.0333H596.95457a56.09681,56.09681,0,0,0,56.0333-56.0333V203.5333A56.09681,56.09681,0,0,0,596.95457,147.5Zm44.18607,546.859a44.23621,44.23621,0,0,1-44.18607,44.18607H407.575A44.23621,44.23621,0,0,1,363.389,694.359V203.5333A44.23622,44.23622,0,0,1,407.575,159.34723H596.95457a44.23622,44.23622,0,0,1,44.18607,44.18607Z"
                transform="translate(-175 -147.5)"
                fill="currentColor"
                class="text-primary-400 transition-colors"
              />
              <path
                d="M596.95457,745.12686H407.575A50.82555,50.82555,0,0,1,356.80716,694.359V203.5333A50.82555,50.82555,0,0,1,407.575,152.76543H596.95457a50.82555,50.82555,0,0,1,50.76786,50.76787V694.359A50.82555,50.82555,0,0,1,596.95457,745.12686Z"
                transform="translate(-175 -147.5)"
                fill="#e6e6e6"
              />
              <path
                d="M601.63445,737.22871H402.8953A34.2798,34.2798,0,0,1,368.65438,702.988V194.90434a34.27967,34.27967,0,0,1,34.24076-34.24075H428.813a5.66578,5.66578,0,0,1,5.65928,5.65944A16.7374,16.7374,0,0,0,451.191,183.04169h99.51475A16.73744,16.73744,0,0,0,567.42455,166.323a5.66579,5.66579,0,0,1,5.65928-5.65944h28.55046a34.27979,34.27979,0,0,1,34.24091,34.24075V702.988A34.27966,34.27966,0,0,1,601.63445,737.22871Z"
                transform="translate(-175 -147.5)"
                fill="currentColor"
                class="text-platinum-50 transition-colors dark:text-midnight-600"
              />
              <path
                d="M510.82113,172.18173H487.12667a2.63272,2.63272,0,0,1,0-5.26544h23.69446a2.63272,2.63272,0,0,1,0,5.26544Z"
                transform="translate(-175 -147.5)"
                fill="currentColor"
                class="text-platinum-100 transition-colors dark:text-midnight-500"
              />
              <circle
                cx="352.27561"
                cy="22.70719"
                r="1.31636"
                fill="currentColor"
                class="text-platinum-100 transition-colors dark:text-midnight-500"
              />
              <rect x="186.99023" y="116" width="280.28735" height="3" fill="#e6e6e6" />
              <rect x="186.99023" y="176" width="280.28735" height="3" fill="#e6e6e6" />
              <rect x="186.99023" y="300" width="280.28735" height="3" fill="#e6e6e6" />
              <rect x="186.99023" y="424" width="280.28735" height="3" fill="#e6e6e6" />
              <rect x="186.99023" y="521" width="280.28735" height="3" fill="#e6e6e6" />
              <rect x="387.5415" y="117.5" width="3" height="184" fill="#e6e6e6" />
              <rect x="247.22656" y="301.44629" width="3" height="221.05371" fill="#e6e6e6" />
              <rect x="248.72656" y="362.08252" width="212.14868" height="3" fill="#e6e6e6" />
              <rect x="387.86426" y="522.5" width="3" height="67.22852" fill="#e6e6e6" />
              <rect x="193.1543" y="239.22852" width="195.38721" height="3" fill="#e6e6e6" />
              <rect
                x="428.18217"
                y="154.90786"
                width="3.00015"
                height="114.53144"
                transform="translate(-223.59965 34.82579) rotate(-22.7088)"
                fill="#e6e6e6"
              />
              <rect x="367.13208" y="35.5415" width="3" height="81.9585" fill="#e6e6e6" />
              <path
                d="M622.49023,310.5c0,26.88036-41.55938,89.516-53.70465,107.24669a3.99186,3.99186,0,0,1-6.59069,0C550.04962,400.016,508.49023,337.38036,508.49023,310.5a57,57,0,0,1,114,0Z"
                transform="translate(-175 -147.5)"
                fill="currentColor"
              />
              <circle
                cx="390.49023"
                cy="159"
                r="29"
                fill="currentColor"
                class="text-platinum-100 transition-colors dark:text-midnight-500"
              />
              <rect
                x="387.32778"
                y="558.75004"
                width="245.38204"
                height="2.99991"
                transform="translate(-394.25359 618.08148) rotate(-63.41202)"
                fill="#e6e6e6"
              />
              <path
                d="M721.75888,498.13009a10.05573,10.05573,0,0,0,14.317-5.72533l35.72951-.61765-10.48946-15.32335-32.3212,2.93577a10.11027,10.11027,0,0,0-7.23584,18.73056Z"
                transform="translate(-175 -147.5)"
                fill="#ffb8b8"
              />
              <path
                d="M743.20789,497.68406a4.10149,4.10149,0,0,1-4.09741-4.28125l.72168-16.38379a4.08166,4.08166,0,0,1,3.6289-3.89258l41.39136-4.74707-1.38721,27.30176-40.05468,1.99707Q743.30909,497.68407,743.20789,497.68406Z"
                transform="translate(-175 -147.5)"
                fill="currentColor"
              />
              <polygon
                points="618.618 574.755 606.964 578.561 586.737 535.421 603.937 529.804 618.618 574.755"
                fill="#ffb8b8"
              />
              <path
                d="M800.28016,732.58073,762.703,744.85353l-.15525-.47528a15.38607,15.38607,0,0,1,9.84857-19.40324l.00092-.0003L795.348,717.479Z"
                transform="translate(-175 -147.5)"
                fill="#2f2e41"
              />
              <polygon
                points="652.944 592.796 640.684 592.795 634.852 545.507 652.946 545.508 652.944 592.796"
                fill="#ffb8b8"
              />
              <path
                d="M831.07054,752.18006,791.54,752.17859v-.5a15.38605,15.38605,0,0,1,15.38648-15.38623h.001l24.1438.001Z"
                transform="translate(-175 -147.5)"
                fill="#2f2e41"
              />
              <path
                d="M809.32337,728.7866a4.10265,4.10265,0,0,1-4.06812-3.61231l-15.80688-132.1455-23.1377,37.14258,27.148,69.37695a4.12536,4.12536,0,0,1-2.01562,5.17773l-23.02979,11.26953a4.10008,4.10008,0,0,1-5.69433-2.39453l-25.522-77.1084a32.57821,32.57821,0,0,1-.72485-17.96875l16.50244-67.72168.19336-.10546,37.199-20.376.2129.07812,43.80786,15.98926-4.58472,86.81738,3.33105,90.1084a4.07728,4.07728,0,0,1-3.84375,4.24414L809.58,728.77879C809.494,728.78367,809.40833,728.7866,809.32337,728.7866Z"
                transform="translate(-175 -147.5)"
                fill="#2f2e41"
              />
              <path
                d="M785.19178,576.53562c-6.54517,0-12.05909-1.86425-15.50928-6.60351l-.11133-.15235,2.23242-23.42578-19.855,11.02149a2.30009,2.30009,0,0,1-3.2688-2.82129l24.76172-65.75879L789.79139,435.228a21.536,21.536,0,0,1,17.35815-17.19629h0c14.56494-2.6753,23.23975,2.23925,33.25659,13.145a23.58463,23.58463,0,0,1,5.82593,19.89648l-9.47632,54.16065,2.78442,48.56054a4.09489,4.09489,0,0,1-1.7771,4.46875C830.01819,563.19285,806.27015,576.53367,785.19178,576.53562Z"
                transform="translate(-175 -147.5)"
                fill="currentColor"
              />
              <circle cx="643.32793" cy="237.85985" r="24.56103" fill="#ffb8b8" />
              <path
                d="M824.72548,560.43755a10.05576,10.05576,0,0,0,4.072-14.87193l21.06419-28.86657-18.54884-.87984-17.15918,27.54708a10.11027,10.11027,0,0,0,10.57182,17.07126Z"
                transform="translate(-175 -147.5)"
                fill="#ffb8b8"
              />
              <path
                d="M836.13343,544.14819a4.10488,4.10488,0,0,1-2.925-.696l-13.94509-9.7243a4.08269,4.08269,0,0,1-1.22867-5.37064l24.82591-44.1711-24.96921-30.05591A14.78319,14.78319,0,1,1,842.12467,437.396l27.57023,48.66117a2.30984,2.30984,0,0,1-.00142,2.26995l-30.57547,53.788a4.109,4.109,0,0,1-2.66069,1.97307C836.34993,544.1134,836.24172,544.13277,836.13343,544.14819Z"
                transform="translate(-175 -147.5)"
                fill="currentColor"
              />
              <path
                d="M808.512,382.22447c-2.43091-1.81448-5.79587-1.53136-8.67632-2.48248a10.30029,10.30029,0,0,1-3.64453-17.45173c3.11064-2.78756,7.53685-3.42018,11.6825-3.93019,5.67954-.6987,11.41422-1.3984,17.10955-.84287s11.42848,2.47934,15.59139,6.40561c4.58993,4.329,6.85067,10.77342,6.86528,17.08275a38.08427,38.08427,0,0,1-5.06209,18.03352,14.13447,14.13447,0,0,1-4.29211,5.29421c-1.9159,1.26452-4.6156,1.59544-6.46075.22976-1.59468-1.18029-2.17835-3.248-2.89876-5.09659s-1.94976-3.8176-3.91311-4.10279c-2.52853-.36728-4.45771,2.20376-6.87032,3.045-3.37444,1.17662-7.12264-1.42575-8.4991-4.72373s-1.014-7.02932-.62784-10.58209"
                transform="translate(-175 -147.5)"
                fill="#2f2e41"
              />
              <path
                d="M1025,751.5a1.00276,1.00276,0,0,1-1,1H176a1,1,0,0,1,0-2h848A1.00276,1.00276,0,0,1,1025,751.5Z"
                transform="translate(-175 -147.5)"
                fill="#ccc"
              />
              <circle cx="390.49023" cy="301" r="13" fill="currentColor" />
              <path
                d="M748.50971,306.36115c-5.42933,7.23584-29.26782,15.70233-36.11843,18.02208a1.34342,1.34342,0,0,1-1.77413-1.3312c.31192-7.226,1.77591-32.48091,7.20524-39.71676a19.18269,19.18269,0,1,1,30.68732,23.02588Z"
                transform="translate(-175 -147.5)"
                fill="currentColor"
                class="text-primary-400 transition-colors dark:text-primary-700"
              />
              <circle
                cx="558.97398"
                cy="146.27146"
                r="9.75962"
                fill="currentColor"
                class="text-platinum-300 transition-colors dark:text-midnight-500"
              />
              <circle
                cx="530.29262"
                cy="184.49602"
                r="4.375"
                fill="currentColor"
                class="text-primary-400 transition-colors dark:text-primary-700"
              />
              <path
                d="M272.677,504.84041c7.98924,12.20605,7.73388,53.00021,7.48867,64.66124a2.16642,2.16642,0,0,1-2.99275,1.95885c-10.78483-4.44152-48.27269-20.53156-56.26193-32.7376a30.93438,30.93438,0,1,1,51.766-33.88249Z"
                transform="translate(-175 -147.5)"
                fill="currentColor"
                class="text-primary-300 transition-colors dark:text-primary-700"
              />
              <circle
                cx="70.60514"
                cy="372.46531"
                r="15.73854"
                fill="currentColor"
                class="text-platinum-300 transition-colors dark:text-midnight-500"
              />
              <circle
                cx="112.80964"
                cy="436.94577"
                r="7.05521"
                fill="currentColor"
                class="text-primary-300 transition-colors dark:text-primary-700"
              />
            </svg>
          </div>
        </div>
        <div class="col-span-7 mx-14 py-3 lg:col-span-4">
          <div class="text-center">
            <h1 class="mt-3 text-3xl font-extrabold tracking-tight text-primary-500 sm:text-4xl">
              Kudos, you found an empty page.
            </h1>
            <p class="mt-3 text-lg text-platinum-950 dark:text-midnight-100">
              Unfortunately the page you are looking for could not be found.
            </p>
          </div>
          <div class="mt-6 rounded-lg bg-platinum-200 p-3 dark:bg-midnight-600">
            <p class="px-1 text-lg text-platinum-950 dark:text-midnight-100">Let's get you back on track:</p>
            <ul
              role="list"
              class="mt-3 divide-y divide-platinum-200 border-t border-platinum-300 dark:divide-midnight-800 dark:border-midnight-800"
            >
              <li
                v-for="(link, linkIdx) in filteredLinks"
                :key="linkIdx"
                class="relative py-3"
                :class="linkIdx == links.length - 1 ? 'pb-0' : ''"
              >
                <RouterLink
                  :to="link.route ? { name: link.route } : link.url"
                  class="group flex items-start space-x-4 rounded-lg p-3 focus-within:ring-2 focus-within:ring-primary-500 hover:cursor-pointer hover:bg-platinum-300 dark:hover:bg-midnight-400"
                >
                  <div class="flex-shrink-0">
                    <span
                      class="flex h-12 w-12 items-center justify-center rounded-lg bg-primary-400 dark:bg-primary-600"
                    >
                      <component
                        :is="link.icon"
                        class="h-6 w-6 text-platinum-200 dark:text-midnight-800"
                        aria-hidden="true"
                      />
                    </span>
                  </div>
                  <div class="min-w-0 flex-1">
                    <h3 class="text-base font-bold text-primary-500">
                      <span
                        class="rounded-sm focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2"
                      >
                        <span class="absolute inset-0" aria-hidden="true" />
                        {{ link.title }}
                      </span>
                    </h3>
                    <p class="text-base text-platinum-950 dark:text-midnight-100">{{ link.description }}</p>
                  </div>
                  <div class="flex-shrink-0 self-center">
                    <BIconArrowRight
                      class="w-4 -translate-x-3 text-primary-600 opacity-0 transition group-hover:translate-x-0 group-hover:opacity-100 dark:text-primary-400"
                      aria-hidden="true"
                    />
                  </div>
                </RouterLink>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, computed } from 'vue';
import { useAuth } from '@/store';

import BIconChatRightQuoteFill from '~icons/bi/chat-right-quote-fill';
import BIconPersonCircle from '~icons/bi/person-circle';
import BIconHeadset from '~icons/bi/headset';
import BIconArrowRight from '~icons/bi/arrow-right';
import BIconHouseFill from '~icons/bi/house-fill';
import BIconSpeedometer from '~icons/bi/speedometer';

const links = [
  {
    title: 'Dashboard',
    description: 'Head back to the dashboard.',
    icon: BIconSpeedometer,
    route: 'index',
    requiresAuth: true,
    requiresGuest: false,
  },
  {
    title: 'Home',
    description: 'Head back to the home page.',
    icon: BIconHouseFill,
    route: 'index',
    requiresAuth: false,
    requiresGuest: true,
  },
  {
    title: 'FAQ',
    description: 'Answers for common questions we get asked.',
    icon: BIconChatRightQuoteFill,
    route: 'support.faq',
    requiresAuth: false,
    requiresGuest: false,
  },
  {
    title: 'My Profile',
    description: 'Everything you related. A space just for you.',
    icon: BIconPersonCircle,
    route: 'profile.view',
    requiresAuth: true,
    requiresGuest: false,
  },
  {
    title: 'Contact Us',
    description: "Need a hand with something? We've got you covered!",
    icon: BIconHeadset,
    route: 'support.contact',
    requiresAuth: false,
    requiresGuest: false,
  },
];

// Computed property to filter links based on auth status.
const filteredLinks = computed(() => {
  const auth = useAuth();
  return links.filter((link) => {
    if (link.requiresAuth && !auth.user) return false;
    if (link.requiresGuest && auth.user) return false;
    return true;
  });
});

const store = useAuth();

onMounted(async () => {
  await store.getUser();
});
</script>