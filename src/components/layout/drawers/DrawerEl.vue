<template>
  <div>
    <template v-if="!link.children">
      <DrawerLink :link="link" />
    </template>
    <template v-else>
      <DrawerDropdown :open="isChildrenActive" :link="link" :label="link.label" :icon="link.icon">
        <template #content>
          <DrawerEl v-for="(link, index) in link.children.filter((child) => !child.ability || auth.can(child.ability))" :key="index" :link="link" />
        </template>
      </DrawerDropdown>
    </template>
  </div>
</template>

<script setup>
import DrawerLink from '@/components/layout/drawers/DrawerLink.vue';
import DrawerDropdown from '@/components/ui/drawer/DrawerDropdown.vue';
import { useRoute, useRouter } from 'vue-router';
import { computed } from 'vue';
import { useAuth } from '@/store';

const auth = useAuth();
const props = defineProps({
  link: {
    type: Object,
    required: true,
  },
});

const route = useRoute();
const router = useRouter();
const isChildActive = (value, prop) => props.link?.children?.map((child) => child[prop]).includes(value);

const isChildrenActive = computed(
  () =>
    route.matched.map((item) => item.name).some((name) => isChildActive(name, 'name')) ||
    route.matched.map((item) => item.path).some((path) => isChildActive(path, 'path'))
);

</script>
