<template>
  <Notification>
    <NotificationContainer>
      <NotificationImage
        :src="notification.data.user.image"
        :fallback="notification.data.user.full_name"
        :id="notification.data.user.locked_id"
      />
      <NotificationContent :is-read="isRead" @update:is-read="markAsReadSingle">
        <template #header>
          <NotificationTitle>New Company User</NotificationTitle>
          <NotificationTimestamp :datetime="notification.created_at"/>
        </template>
        <template #description>
          <NotificationDescription>
            Awesome!
            <VEntityLink
              entity-type="user"
              :entity-id="notification.data.user.locked_id"
              :value="notification.data.user.full_name"
              :show-icon="false"
              :show-link-icon="false"
            /> has joined the team at 
            <VEntityLink
              entity-type="company"
              :entity-id="notification.data.company.locked_id"
              :value="notification.data.company.name"
              :show-icon="false"
              :show-link-icon="false"
            />. Consider reaching out to them on their profile!
          </NotificationDescription>
        </template>
      </NotificationContent>
    </NotificationContainer>
    <NotificationActions>
      <VButton
        intent="notification"
        as="router-link"
        :to="{ name: 'users.profile', params: { id: notification.data.user.locked_id } }"
        centered
      >
        <span class="flex items-center space-x-2">
          <BIconPersonCircle />
          <span>View Profile</span>
        </span>
      </VButton>
      <VButton
        intent="notification"
        as="router-link"
        :to="{ name: 'companies.profile', params: { id: notification.data.company.locked_id } }"
        centered
      >
        <span class="flex items-center space-x-2">
          <BIconBuilding />
          <span>View Company</span>
        </span>
      </VButton>
    </NotificationActions>
  </Notification>
</template>

<script setup lang="ts">
import BIconPersonCircle from '~icons/bi/person-circle';
import BIconBuilding from '~icons/bi/building';
import {
  Notification,
  NotificationContent,
  NotificationTitle,
  NotificationDescription,
  NotificationContainer,
  NotificationActions,
  NotificationImage,
  NotificationTimestamp,
} from '@/components/ui/notifications';
import type { Notification as NotificationResponse } from '@/types/api';

export type NewCompanyUserNotification = {
  user: {
    locked_id: string;
    full_name: string;
    image: string;
  },
  company: {
    locked_id: string;
    name: string;
    image: string;
  }
  created_at: string;
  read_at: string | null;
}

const props = defineProps<{
  notification: {
    data: NewCompanyUserNotification;
    created_at: string;
    read_at: string | null;
  };
  isRead?: boolean;
}>();

const emit = defineEmits(['update:is-read']);

function markAsReadSingle(read = true) {
  emit('update:is-read', read);
}

</script>