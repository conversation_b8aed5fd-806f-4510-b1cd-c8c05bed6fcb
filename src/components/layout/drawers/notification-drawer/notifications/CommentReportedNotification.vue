<template>
  <Notification>
    <NotificationContainer>
      <NotificationImage
        :src="notification.data.reporter?.image"
        :fallback="notification.data.reporter?.full_name || 'Anonymous'"
        :id="notification.data.reporter?.locked_id"
      />
      <NotificationContent :is-read="isRead" @update:is-read="markAsReadSingle">
        <template #header>
          <NotificationTitle>Comment Reported</NotificationTitle>
          <NotificationTimestamp :datetime="notification.created_at"/>
        </template>
        <template #description>
          <NotificationDescription>
            <VEntityLink
              v-if="notification.data.reporter"
              entity-type="user"
              :entity-id="notification.data.reporter.locked_id"
              :value="notification.data.reporter.full_name"
              :show-icon="false"
              :show-link-icon="false"
            />
            <span v-else>An anonymous user</span>
            has reported a comment for {{ notification.data.report.type || 'general concerns' }}.
            Review required for content moderation.
          </NotificationDescription>
        </template>
      </NotificationContent>
    </NotificationContainer>
    <NotificationActions>
      <VButton
        v-if="notification.data.reporter"
        intent="notification"
        as="router-link"
        :to="{ name: 'users.profile', params: { id: notification.data.reporter.locked_id } }"
        centered
      >
        <span class="flex items-center space-x-2">
          <BIconPersonCircle />
          <span>View Reporter</span>
        </span>
      </VButton>
      <VButton
        v-if="notification.data.comment"
        intent="notification"
        as="router-link"
        :to="{ name: 'comments.view', params: { id: notification.data.comment.locked_id } }"
        centered
      >
        <span class="flex items-center space-x-2">
          <BIconChatText />
          <span>View Comment</span>
        </span>
      </VButton>
    </NotificationActions>
  </Notification>
</template>

<script setup lang="ts">
import BIconPersonCircle from '~icons/bi/person-circle';
import BIconChatText from '~icons/bi/chat-text';
import {
  Notification,
  NotificationContent,
  NotificationTitle,
  NotificationDescription,
  NotificationContainer,
  NotificationActions,
  NotificationImage,
  NotificationTimestamp,
} from '@/components/ui/notifications';
import type { Notification as NotificationResponse } from '@/types/api';

export type ReportNotification = {
  reporter?: {
    locked_id: string;
    full_name: string;
    email: string;
    image: string;
  };
  comment?: {
    locked_id: string;
    body: string;
    creator: {
      locked_id: string;
      full_name: string;
      email: string;
    };
  };
  report: {
    type?: string;
    reason?: string;
    datetime: string;
  };
}

const props = defineProps<{
  notification: {
    data: ReportNotification;
    created_at: string;
    read_at: string | null;
  };
  isRead?: boolean;
}>();

const emit = defineEmits(['update:is-read']);

function markAsReadSingle(read = true) {
  emit('update:is-read', read);
}
</script>
