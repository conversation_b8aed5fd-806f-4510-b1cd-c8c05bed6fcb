<template>
  <Notification>
    <NotificationContainer>
      <NotificationContent
        :is-read="isRead"
        @update:is-read="markAsReadSingle"
      >
        <template #header>
          <NotificationTitle>Password Updated</NotificationTitle>
          <NotificationTimestamp :datetime="notification.created_at"/>
        </template>
        <template #description>
          <NotificationDescription>Your password was updated on {{ formattedDate(notification.created_at) }}
            {{ notification.data.ipAddress ? ` from the IP Address: ${notification.data.ipAddress}` : '' }}.
          </NotificationDescription>
        </template>
      </NotificationContent>
    </NotificationContainer>
  </Notification>
</template>

<script setup lang="ts">
import BIconPersonCircle from '~icons/bi/person-circle';
import {
  Notification,
  NotificationContent,
  NotificationTitle,
  NotificationDescription,
  NotificationContainer,
  NotificationActions,
  NotificationTimestamp
} from '@/components/ui/notifications';
import type { Notification as NotificationResponse } from '@/types/api';
import { formattedDate } from '@/util/dateUtils';

export type PasswordChangeNotification = {
  user: {
    locked_id: string;
    full_name: string;
    image: string;
  },
  ipAddress: string,
  created_at: string,
  read_at: string | null,
}

const props = defineProps<{
  notification: NotificationResponse<PasswordChangeNotification>
  isRead?: boolean;
}>();

const emit = defineEmits(['update:is-read']);

function markAsReadSingle(read = true) {
  emit('update:is-read', read);
}

</script>