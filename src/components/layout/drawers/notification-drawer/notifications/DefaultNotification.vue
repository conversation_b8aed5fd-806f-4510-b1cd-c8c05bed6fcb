<template>
  <Notification>
    <NotificationContainer>
      <NotificationContent :is-read="isRead" @update:is-read="markAsReadSingle">
        <template #header>
          <NotificationTitle>{{ notificationTitle }}</NotificationTitle>
          <NotificationTimestamp :datetime="notification.created_at"/>
        </template>
        <template #description>
          <NotificationDescription>
            <div class="space-y-1">
              <p class="text-sm">{{ notificationDescription }}</p>
              <p class="text-xs opacity-80 border-t border-platinum-300 dark:border-midnight-300 inline-block w-auto pt-1">
                <b class="text-primary-500">Note:</b>
                This is a fallback notification.
              </p>
            </div>
          </NotificationDescription>
        </template>
      </NotificationContent>
    </NotificationContainer>
  </Notification>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  Notification,
  NotificationContent,
  NotificationTitle,
  NotificationDescription,
  NotificationContainer,
  NotificationTimestamp,
} from '@/components/ui/notifications';

const props = defineProps<{
  notification: {
    type: string;
    data: Record<string, any>;
    created_at: string;
    read_at: string | null;
  };
  isRead?: boolean;
}>();

const emit = defineEmits(['update:is-read']);

function markAsReadSingle(read = true) {
  emit('update:is-read', read);
}

// Convert notification type to readable title
const notificationTitle = computed(() => {
  return props.notification.type
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim();
});

// Simple description
const notificationDescription = computed(() => {
  return `You have received a ${notificationTitle.value.toLowerCase().replace('notification', '')} notification.`;
});
</script>
