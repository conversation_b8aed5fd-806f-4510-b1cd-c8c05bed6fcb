<template>
  <Notification>
    <NotificationContainer>
      <NotificationContent :is-read="isRead" @update:is-read="markAsReadSingle">
        <template #header>
          <NotificationTitle>Contact Enquiry Confirmed</NotificationTitle>
          <NotificationTimestamp :datetime="notification.created_at"/>
        </template>
        <template #description>
          <NotificationDescription v-if="typeRequiresReply">
            Your {{ notification.data.type_value }} enquiry has been received. You can expect to hear back from us soon!
          </NotificationDescription>
          <NotificationDescription v-else>
            Your {{ notification.data.type_value }} message has been received. Thanks for reaching out to us!
          </NotificationDescription>
        </template>
      </NotificationContent>
    </NotificationContainer>
  </Notification>
</template>

<script setup lang="ts">
import {
  Notification,
  NotificationContent,
  NotificationTitle,
  NotificationDescription,
  NotificationContainer,
  NotificationTimestamp,
} from '@/components/ui/notifications';
import type { Notification as NotificationResponse } from '@/types/api';
import { computed } from 'vue';

export type ContactUsReceiptNotification = {
  type_value: string;
}

const props = defineProps<{
  notification: {
    data: ContactUsReceiptNotification;
    created_at: string;
    read_at: string | null;
  };
  isRead?: boolean;
}>();

const emit = defineEmits(['update:is-read']);

const typeRequiresReply = computed(() => {
  // Check if the type includes 'feedback'.
  return !props.notification?.data?.type_value?.toLowerCase().includes('feedback');
});

function markAsReadSingle(read = true) {
  emit('update:is-read', read);
}

</script>