<template>
  <component
    :is="notificationComponent"
    :notification="notification"
    :class="[isRead ? 'opacity-75 hover:opacity-100' : 'opacity-100']"
    :is-read="isRead"
    :notification-id="notification.id"
    @update:is-read="markAsReadSingle"
  />
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import { useNotifications } from '@/store/notifications';

// Notification components.
import {
  NewCompanyUserNotification,
  PasswordChangeNotification,
  ContactUsReceiptNotification,
  CommentReportedNotification,
  DefaultNotification,
} from './notifications'

const props = defineProps({
  notification: {
    type: Object,
    required: true,
  },
});

const isRead = ref(!!props.notification.read_at);

watch(() => props.notification.read_at, (newVal) => {
  isRead.value = !!newVal;
});

const notificationsStore = useNotifications();

function markAsReadSingle(read = true) {
  notificationsStore.markAsRead(props.notification.id, read);
}

const notificationComponent = computed(() => {
  switch (props.notification.type) {
    case 'NewCompanyUserNotification':
      return NewCompanyUserNotification;
    case 'PasswordChangeNotification':
      return PasswordChangeNotification;
    case 'ContactUsReceiptNotification':
      return ContactUsReceiptNotification;
    case 'CommentReportedNotification':
      return CommentReportedNotification;
    default:
      return DefaultNotification;
  }
});
</script>