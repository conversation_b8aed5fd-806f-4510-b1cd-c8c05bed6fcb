<template>
  <div>
    <span @click="toggle">
      <slot />
    </span>

    <VDrawer
      :open="isOpen"
      position="right"
      @close="close"
      size="xl"
    >
      <template #title>
        <div class="flex h-16 items-center text-sm font-bold uppercase tracking-widest text-primary-500">
          Notifications
        </div>
      </template>

      <template #content>
        <!-- Done as reverse to prevent autofocus on notification content via headless ui. -->
        <div class="flex flex-col-reverse h-full shadow-inner shadow-platinum-300 dark:shadow-midnight-800">
          <VButton
            intent="navBottom"
            class="w-full bg-primary-500 hover:bg-primary-400 dark:bg-primary-600 dark:hover:bg-primary-700"
            as="router-link"
            :to="{ name: 'notifications.index' }"
          >View All Notifications</VButton>
          <div class="flex-1 overflow-hidden">
            <NotificationsWrapper :notifications="sortedNotifications" class="flex-1" />
          </div>
        </div>
      </template>
    </VDrawer>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, computed } from 'vue';
import { useDisclosure } from '@/hooks/disclosure';
import { useAuth } from '@/store';
import { useNotifications } from '@/store/notifications';
import NotificationsWrapper from '@/components/ui/notifications/NotificationsWrapper.vue';

// Use a single declaration for notificationsStore
const notificationsStore = useNotifications();

const sortedNotifications = computed(() => {
  return [...notificationsStore.notifications].sort((a, b) => {
    // Unread first, then by created_at desc
    if (!!a.read_at === !!b.read_at) {
      return new Date(b.created_at) - new Date(a.created_at);
    }
    return a.read_at ? 1 : -1;
  });
});

const { isOpen, toggle, close } = useDisclosure(false);
const auth = useAuth();
// Removed duplicate notificationsStore and unused unreadNotifications/readNotifications.

// Initialize WebSocket connection and fetch initial notifications
onMounted(() => {
  notificationsStore.initializeWebSocket(auth.user.locked_id);
  notificationsStore.fetchNotifications();
});

// Cleanup when component is unmounted
onUnmounted(() => {
  notificationsStore.resetState();
});
</script>
