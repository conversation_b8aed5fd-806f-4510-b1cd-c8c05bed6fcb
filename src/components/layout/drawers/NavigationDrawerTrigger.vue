<template>
  <VTooltip
    :content="open ? 'Close Navigation' : 'Open Navigation'"
    trigger-classes="h-full w-14"
  >
    <button class="group relative transition hover:bg-primary-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 z-[99999999]">
      <div class="relative flex h-16 w-14 transform items-center justify-center overflow-hidden rounded-full transition-all">
        <div class="flex h-[18px] w-[18px] origin-center transform flex-col justify-between transition-all duration-300">
          <div 
            :class="{ '!w-[24px] -translate-y-[0.5px] translate-x-[0.5px] rotate-[45deg]': open }"
            class="h-[2px] w-full origin-left transform rounded bg-primary-500 transition-transform duration-300 group-hover:bg-white dark:group-hover:bg-midnight-600"
          ></div>
          <div
            :class="{ '-translate-x-5 opacity-0': open }"
            class="h-[2px] w-1/2 transform rounded bg-primary-500 transition-all duration-100 group-hover:bg-white hover:bg-primary-500 dark:group-hover:bg-midnight-600"
          ></div>
          <div
            :class="{ '-translate-x-5 opacity-0': open }"
            class="h-[2px] w-1/2 transform rounded bg-primary-500 transition-all duration-100 group-hover:bg-white hover:bg-primary-500 dark:group-hover:bg-midnight-600"
          ></div>
          <div
            :class="{ '!w-[24px] translate-x-[0.5px] translate-y-[0.5px] -rotate-[45deg]': open }"
            class="h-[2px] w-full origin-left transform rounded bg-primary-500 transition-transform duration-300 group-hover:bg-white hover:bg-primary-500 dark:group-hover:bg-midnight-600"
          ></div>
        </div>
      </div>
    </button>
  </VTooltip>
</template>

<script setup>

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
});

</script>