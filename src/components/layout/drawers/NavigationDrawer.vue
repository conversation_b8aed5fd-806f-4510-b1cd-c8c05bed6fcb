<template>
  <div>
    <span @click="layout.actions.toggleNavigation()">
      <slot :open="layout.getters.isNavigationOpen()" />
    </span>

    <VDrawer :open="layout.getters.isNavigationOpen()" @close="layout.actions.setNavigationOpen(false)" size="sm">
      <template #title>
        <div class="ml-2 text-sm font-bold uppercase tracking-widest text-primary-500">Navigation</div>
        <div class="absolute -right-14 top-0"  @click="layout.actions.toggleNavigation()">
          <NavigationDrawerTrigger :open="layout.getters.isNavigationOpen()" />
        </div>
      </template>

      <template #content>
        <ul class="mt-0 flex h-full list-none flex-col space-y-0">
          <template v-if="auth.user">
            <DrawerEl
              v-for="(link, index) in links.filter((link) => link.auth && (!link.ability || auth.can(link.ability, 'any')))"
              :key="index"
              :link="link"
            />
          </template>

          <template v-else>
            <DrawerEl v-for="(link, index) in links.filter((link) => link.guest)" :key="index" :link="link" />
          </template>

          <DrawerEl v-for="(link, index) in links.filter((link) => link.common)" :key="index" :link="link" />
        </ul>
      </template>
    </VDrawer>
  </div>
</template>

<script setup>
import DrawerEl from '@/components/layout/drawers/DrawerEl.vue';
import { useAuth } from '@/store';
import layout from '@/store/layout';
import BIconBuildingFill from '~icons/bi/building-fill';
import BIconHouseFill from '~icons/bi/house-fill';
import BIconJournalBookmarkFill from '~icons/bi/journal-bookmark-fill';
import BIconPersonVCardFill from '~icons/bi/person-vcard-fill';
import BIconActivity from '~icons/bi/activity';
import BIconPersonWorkspace from '~icons/bi/person-workspace';
import BIconPeopleFill from '~icons/bi/people-fill';
import BIconSpeedometer from '~icons/bi/speedometer';
import BIconGearFill from '~icons/bi/gear-fill';
import BIconShieldLockFill from '~icons/bi/shield-lock-fill';
import BIconAwardFill from '~icons/bi/award-fill';
import BIconGlobeAmericas from '~icons/bi/globe-americas';
import BIconMegaphoneFill from '~icons/bi/megaphone-fill';
import BIconCurrencyExchange from '~icons/bi/currency-exchange';
import BIconBriefcaseFill from '~icons/bi/briefcase-fill';
import BIconBellFill from '~icons/bi/bell-fill';
import BIconBookmarksFill from '~icons/bi/bookmarks-fill';
import BIconChatQuoteFill from '~icons/bi/chat-quote-fill';
import BIconPaletteFill from '~icons/bi/palette-fill';
import BIconHeadphones from '~icons/bi/headphones';
import NavigationDrawerTrigger from '@/components/layout/drawers/NavigationDrawerTrigger.vue';
import BIconBookmarkHeartFill from '~icons/bi/bookmark-heart-fill';

const auth = useAuth();

const links = [
  {
    label: 'Dashboard',
    name: 'index',
    url: null,
    icon: BIconSpeedometer,
    auth: true,
    guest: false,
    common: false,
    ability: 'dashboard.view',
  },
  {
    label: 'Users',
    name: 'users.index',
    url: null,
    icon: BIconPeopleFill,
    auth: true,
    guest: false,
    common: false,
    ability: 'users.index',
  },
  {
    label: 'Companies',
    name: 'companies.index',
    url: null,
    icon: BIconBuildingFill,
    auth: true,
    guest: false,
    common: false,
    ability: ['companies.index.any', 'companies.index', 'companies.index.self'],
  },
  {
    label: 'Customers',
    name: 'customers.index',
    url: null,
    icon: BIconPersonWorkspace,
    auth: true,
    guest: false,
    common: false,
    ability: ['customers.index.any', 'customers.index', 'customers.index.self'],
  },
  {
    label: 'Activity',
    name: 'activity.index',
    url: null,
    icon: BIconActivity,
    auth: true,
    guest: false,
    common: false,
    ability: ['activity.index.any', 'activity.index', 'activity.index.self'],
  },
  {
    label: 'Contacts',
    name: 'contacts.index',
    url: null,
    icon: BIconPersonVCardFill,
    auth: true,
    guest: false,
    common: false,
    ability: ['contacts.index.any', 'contacts.index', 'contacts.index.self'],
  },
  {
    label: 'Addresses',
    name: 'addresses.index',
    url: null,
    icon: BIconJournalBookmarkFill,
    auth: true,
    guest: false,
    common: false,
    ability: ['addresses.index.any', 'addresses.index', 'addresses.index.self'],
  },
  {
    label: 'Subscriptions',
    name: 'subscriptions.index',
    url: null,
    icon: BIconBookmarkHeartFill,
    auth: true,
    guest: false,
    common: false,
    ability: 'subscriptions.index.any',
  },
  {
    label: 'Comments',
    name: 'comments.index',
    url: null,
    icon: BIconChatQuoteFill,
    auth: true,
    guest: false,
    common: false,
    ability: 'comments.index.any',
  },
  {
    label: 'Settings',
    name: 'settings.index',
    url: null,
    icon: BIconGearFill,
    auth: true,
    guest: false,
    common: false,
    ability: 'settings.view',
    children: [
      {
        label: 'Announcements',
        name: 'settings.announcements.index',
        url: null,
        icon: BIconMegaphoneFill,
        auth: true,
        guest: false,
        common: false,
        ability: 'announcements.index.any',
      },
      {
        label: 'Roles',
        name: 'settings.roles.index',
        url: null,
        icon: BIconAwardFill,
        auth: true,
        guest: false,
        common: false,
        ability: 'roles.index.any',
      },
      {
        label: 'Abilities',
        name: 'settings.abilities.index',
        url: null,
        icon: BIconShieldLockFill,
        auth: true,
        guest: false,
        common: false,
        ability: 'abilities.index',
      },
      {
        label: 'Countries',
        name: 'settings.countries.index',
        url: null,
        icon: BIconGlobeAmericas,
        auth: true,
        guest: false,
        common: false,
        ability: 'countries.index',
      },
      {
        label: 'Currencies',
        name: 'settings.currencies.index',
        url: null,
        icon: BIconCurrencyExchange,
        auth: true,
        guest: false,
        common: false,
        ability: 'currencies.index',
      },
      {
        label: 'Industries',
        name: 'settings.industries.index',
        url: null,
        icon: BIconBriefcaseFill,
        auth: true,
        guest: false,
        common: false,
        ability: 'industries.index',
      },
      {
        label: 'Notifications',
        name: 'settings.notifications.index',
        url: null,
        icon: BIconBellFill,
        auth: true,
        guest: false,
        common: false,
        ability: 'notifications.index.any',
      },
      {
        label: 'Subscription Tiers',
        name: 'settings.tiers.index',
        url: null,
        icon: BIconBookmarksFill,
        auth: true,
        guest: false,
        common: false,
        ability: 'subscriptions.tiers.index.any',
      },
      {
        label: 'Themes',
        name: 'settings.themes.index',
        url: null,
        icon: BIconPaletteFill,
        auth: true,
        guest: false,
        common: false,
        ability: 'themes.index.any',
      },
    ],
  },
  {
    label: 'Support',
    name: 'support.index',
    url: null,
    icon: BIconHeadphones,
    guest: false,
    auth: false,
    common: true,
  },
  {
    label: 'Home',
    name: 'index',
    url: null,
    icon: BIconHouseFill,
    guest: true,
    auth: false,
    common: false,
  },
  // {
  //   label: 'About',
  //   name: 'forefront.about',
  //   url: null,
  //   icon: BIconPatchQuestionFill,
  //   guest: true,
  //   auth: false,
  //   common: false,
  // },
  // {
  //   label: 'Services',
  //   name: 'forefront.services',
  //   url: null,
  //   icon: BIconShop,
  //   guest: true,
  //   auth: false,
  //   common: false,
  // },
];

</script>
