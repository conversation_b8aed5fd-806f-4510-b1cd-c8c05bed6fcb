<template>
  <div class="h-full">
  <TransitionFade>
    <div v-if="auth?.user && auth?.user?.profile">
      <span @click="layout.actions.toggleUserNavigation()">
        <slot />
      </span>

      <VDrawer
        position="right"
        :open="layout.getters.isUserNavigationOpen()"
        @close="layout.actions.setUserNavigationOpen(false)"
        size="sm"
      >
        <template #title>
          <div class="ml-2 text-sm font-bold uppercase tracking-widest text-primary-500">Manage</div>
        </template>

        <template #content>
          <div class="flex h-[calc(100vh-64px)] list-none flex-col">
            <UserDrawerAvailabilityDisclosure v-if="auth.user.availability" />
            <UserDrawerThemeDisclosure />

            <ul class="flex flex-col justify-between h-full">
              <div>
                <DrawerLink v-for="(link, index) in links" :key="index" :link="link">
                  {{ link.label }}
                </DrawerLink>
              </div>

              <VButton
                class="bg-red-900/80 hover:bg-red-900 dark:bg-red-900/60 dark:hover:bg-red-900"
                intent="navBottom"
                @click="isConfirmingLogout = true"
              >Logout</VButton>
            </ul>
          </div>
        </template>
      </VDrawer>
    </div>

    <!-- User has not yet created a profile, provide a logout button. -->
    <div
      v-else
      class="h-full"
    >
      <VTooltip
        content="Log Out"
        trigger-classes="h-full"
      >
        <VButton
          v-if="auth.user && !auth.user?.profile"
          intent="nav"
          class="relative transition hover:bg-primary-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 h-full"
          @click="isConfirmingLogout = true"
        >
          <BIconBoxArrowLeft class="w-5 h-5 mt-0.5" />
          <span class="sr-only">Logout</span>
        </VButton>
      </VTooltip>
    </div>
  </TransitionFade>

  <VConfirmModal
    v-model:open="isConfirmingLogout"
    @confirmed="logoutAndRedirect"
    title="Confirm Logout"
    description="Are you sure you want to logout?"
    :icon="BIconDoorOpenFill"
  />
  </div>
</template>

<script setup>
import DrawerLink from '@/components/layout/drawers/DrawerLink.vue';
import UserDrawerAvailabilityDisclosure from '@/components/layout/drawers/UserDrawerAvailabilityDisclosure.vue';
import UserDrawerThemeDisclosure from '@/components/layout/drawers/UserDrawerThemeDisclosure.vue';
import BIconDoorOpenFill from '~icons/bi/door-open-fill';
import BIconPersonCircle from '~icons/bi/person-circle';
import BIconBookmarkStarFill from '~icons/bi/bookmark-star-fill';
import BIconBoxArrowLeft from '~icons/bi/box-arrow-left';
import { useAuth } from '@/store';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import layout from '@/store/layout';
const auth = useAuth();
const { logout } = useAuth();
const router = useRouter();

const isConfirmingLogout = ref(false);

const logoutAndRedirect = async () => {
  try {
    // Close UI elements (await if returns a promise).
    await layout.actions.closeAllDrawers();

    // Perform logout with toast
    const loggedOut = await logout(true);

    // Redirect to index page if logout was successful.
    if (loggedOut) {
      // Use nextTick to ensure the logout state is fully processed.
      await router.push({ path: '/', replace: true });
    }

  } catch (error) {
    console.error("Logout failed:", error);
    // Show error toast if logout fails.
    Reshape.toast({
      title: 'Logout Failed',
      message: 'There was an error logging you out. Please try again.'
    }, 'danger');
  }
};

const links = [
  {
    label: 'My Profile',
    name: 'profile.view',
    url: null,
    icon: BIconPersonCircle,
    classes: 'border-t hover:border-t  hover:border-beta-500',
  },
  {
    label: 'My Subscription',
    name: 'profile.subscription.index',
    url: null,
    icon: BIconBookmarkStarFill,
    classes: 'border-t hover:border-t  hover:border-beta-500',
  },
];
</script>
