<template>
  <DrawerDropdown>
    <template #label="{ open }">
      <div v-if="open" class="group flex items-center">
        <BIconChatRightQuoteFill
          class="mr-3 text-primary-500 group-hover:text-white dark:text-midnight-50 dark:group-hover:text-midnight-50"
        />
        <span class="font-semibold group-hover:text-white">I am currently...</span>
      </div>

      <div v-else class="flex items-center group-hover:text-white">
        <span class="-mt-1 mr-2 inline w-2.5">
          <figure
            class="default-icon-size h-3.5 w-3.5 rounded-full"
            :style="{ backgroundColor: auth.user.availability.color }"
          ></figure>
        </span>
        <span class="ml-3 font-semibold">Showing as {{ auth.user.availability.name }}</span>
      </div>
    </template>
    <template #content>
      <div
        v-for="availability in availabilites?.data"
        :key="availability.locked_id"
        class="group/availabilites cursor-pointer px-6 py-1.5 text-primary-700 hover:bg-platinum-300 dark:text-midnight-50 dark:hover:bg-midnight-800"
        @click="updateAvailability(availability.locked_id)"
      >
        <BIconChevronRight
          class="-mt-1 mr-2 inline w-2.5 text-platinum-400 group-hover/availabilites:text-primary-500"
        />

        <span
          class="mr-2 inline-block h-3 w-3 rounded-full ring-white"
          :style="{ backgroundColor: availability.color }"
        ></span>

        <span>{{ availability.name }}</span>
      </div>
    </template>
  </DrawerDropdown>
</template>

<script setup>
import { useMutation, useQuery } from '@tanstack/vue-query';

import { useAuth } from '@/store';
import BIconChatRightQuoteFill from '~icons/bi/chat-right-quote-fill';
import BIconChevronRight from '~icons/bi/chevron-right';
import DrawerDropdown from '@/components/ui/drawer/DrawerDropdown.vue';

const { data: availabilites } = useQuery({
  queryKey: ['availabilities'],
  queryFn: () =>
    Reshape.http()
      .get('/api/users/availabilities')
      .then((response) => response),
});

const { mutate } = useMutation({
  mutationFn: (payload) => Reshape.http().put('/api/user/availability', payload),
  onSuccess: () => auth.getUser(),
});

const updateAvailability = (id) => {
  mutate({
    availability_id: id,
  });
};

const auth = useAuth();
</script>
