<template>
  <DrawerDropdown>
    <template #label="{ open }">
      <div class="flex items-center group-hover:text-white">
        <BIconPaletteFill class="mr-3 text-primary-500 group-hover:text-white dark:group-hover:text-midnight-50" />
        <span class="font-semibold">Select Theme</span>
      </div>
    </template>
    <template #content>
      <div class="grid grid-cols-8 gap-3 p-3 px-6">
        <VTooltip
          v-for="theme in themes?.data"
          :key="theme.locked_id"
          :content="theme.name"
          class="group/theme flex cursor-pointer justify-center h-5 w-5"
        >
          <button
            @click="updateTheme(theme.locked_id)"
            class="h-5 w-5 overflow-hidden rounded-full ring-2 ring-platinum-300 dark:ring-midnight-400"
          >
            <div class="-rotate-45 rounded-full opacity-75 transition-opacity group-hover/theme:opacity-100">
              <span class="flex h-2.5 w-5" :style="{ backgroundColor: `hsl(${theme.colors['500']})` }"></span>
              <span class="flex h-2.5 w-5 bg-platinum-100 dark:bg-midnight-900"></span>
            </div>
          </button>
        </VTooltip>
        <div v-if="auth.can('themes.create')">
          <VTooltip content="Add Theme">
            <router-link
              :to="{ name: 'themes.create' }"
              class="h-5 w-5 transition overflow-hidden rounded-full block p-0.5 ring-2 ring-platinum-300 dark:ring-midnight-400 hover:text-primary-400 bg-primary-500 opacity-50 hover:opacity-100"
            >
              <BIconPlus class="h-4 w-4 text-white" />
            </router-link>
          </VTooltip>
        </div>
      </div>
    </template>
  </DrawerDropdown>
</template>

<script setup>
import { useQuery, useMutation } from '@tanstack/vue-query';
import { useAuth } from '@/store';
import BIconPaletteFill from '~icons/bi/palette-fill';
import DrawerDropdown from '@/components/ui/drawer/DrawerDropdown.vue';
import BIconPlus from '~icons/bi/plus';

const { data: themes } = useQuery({
  queryKey: ['themes'],
  queryFn: () =>
    Reshape.http()
      .get('/api/themes')
      .then((response) => response),
});

const { mutate } = useMutation({
  mutationFn: (payload) => Reshape.http().put('/api/current-theme', payload),
  onSuccess: () => auth.getUser(),
});

const updateTheme = (id) => {
  mutate({
    theme_id: id,
  });
};

const auth = useAuth();
</script>
