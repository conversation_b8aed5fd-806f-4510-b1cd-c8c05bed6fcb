<template>
  <li>
    <RouterLink :to="(link.name ? { name: link.name } : link.url) ?? '/'" v-slot="{ isExactActive }">
      <div
        :class="[
          isExactActive
            ? 'border-primary-400 bg-primary-600 text-white'
            : 'border-transparent text-primary-500 hover:bg-primary-500 hover:text-white dark:text-midnight-50 dark:hover:bg-midnight-800',
          'group flex items-center border-r-4 py-3 pl-6 pr-3 transition',
        ]"
      >
        <component
          :is="link.icon"
          :class="[
            isExactActive ? 'text-white' : 'text-primary-500 group-hover:text-white',
            'mr-3 h-5 w-5 flex-shrink-0 transition',
          ]"
          aria-hidden="true"
        />
        <span class="font-semibold">{{ link.label }}</span>
      </div>
    </RouterLink>
  </li>
</template>

<script setup>
defineProps({
  link: {
    type: Object,
    required: true,
  },
});
</script>
