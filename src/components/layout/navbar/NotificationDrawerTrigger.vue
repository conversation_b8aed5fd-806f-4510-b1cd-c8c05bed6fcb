<template>
  <VTooltip
    content="Open Notifications"
    trigger-classes="h-full"
  >
    <NavbarItem :class="notificationStore.unreadCount >= 10 ? 'pr-5' : ''">
      <span class="relative inline-block">
        <BIconBellFill
          class="z-10 h-5 w-5 origin-[center_top] animate-[swing_2s_ease-in-out_2_0.25s] text-primary-500 transition group-hover:text-white dark:group-hover:text-midnight-600"
        />
        <TransitionFade>
          <span
            v-if="notificationStore.unreadCount"
            class="absolute right-0 top-0 block -translate-y-1/2 transform rounded-full"
            :class="notificationStore.unreadCount >= 10 ? 'translate-x-[60%]' : 'translate-x-[50%]'"
          >
            <span class="block rounded-full bg-red-400 px-1 pt-0.5 text-[8pt] font-bold text-white dark:bg-red-700">
              {{ notificationStore.unreadCount >= 10 ? '9+' : notificationStore.unreadCount }}
            </span>
          </span>
        </TransitionFade>
      </span>
    </NavbarItem>
  </VTooltip>
</template>

<script setup>
import BIconBellFill from '~icons/bi/bell-fill';
import NavbarItem from '@/components/layout/navbar/NavbarItem.vue';
import { useNotifications } from '@/store/notifications';
import { onMounted, onUnmounted } from 'vue';

const notificationStore = useNotifications();

onMounted(() => {
  // notificationStore.startPolling();
});

onUnmounted(() => {
  // notificationStore.stopPolling();
});
</script>
