<template>
  <nav class="z-[40] bg-platinum-100 shadow shadow-primary-700/30 transition duration-300 dark:bg-midnight-600 dark:shadow-midnight-800">
    <div class="mx-auto">
      <div class="relative flex h-16 justify-between">
        <div class="inset-y-0 left-0 flex flex-1 items-center justify-start">
          <NavigationDrawer>
            <NavigationDrawerTrigger :open="layout.getters.isNavigationOpen()" />
          </NavigationDrawer>
        </div>
        <VTooltip
          :content="auth.user && auth.user?.profile ? 'Go to Dashboard' : 'Home'"
          trigger-classes="h-full"
          triggerAsChild
        >
        <RouterLink :to="{ name: 'index' }" class="group">
          <VButton intent="nav" class="px-3 relative transition hover:bg-primary-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500">
            <div class="flex items-center justify-center">
              <Logo class="block" :logoText="appTitle" />
            </div>
          </VButton>
        </RouterLink>
        </VTooltip>
        <div class="inset-y-0 right-0 flex flex-1 items-center justify-end pr-0 sm:static sm:inset-auto">

          <TransitionFade>
            <NotificationDrawer v-if="auth?.user && auth?.user?.profile">
              <NotificationDrawerTrigger />
            </NotificationDrawer>
          </TransitionFade>

          <NavbarDarkToggle />

          <UserDrawer>
            <NavbarAvatarItem class="relative" />
          </UserDrawer>

          <div v-if="!auth.user" class="flex h-full">
            <VTooltip
              content="Existing User Log in"
              trigger-classes="h-full"
            >
              <RouterLink :to="{ name: 'auth.login' }" class="h-full">
                <VButton intent="nav" class="group">
                  <BIconBoxArrowInRight class="w-4" />
                  <span
                    class="hidden text-primary-500 transition-colors group-hover:text-white dark:group-hover:text-midnight-600 lg:inline ml-2"
                  >Log in</span>
                </VButton>
              </RouterLink>
            </VTooltip>
            <VTooltip
              :content="`Register with ${appTitle}`"
              trigger-classes="h-full"
            >
              <RouterLink :to="{ name: 'auth.register' }">
                <VButton intent="nav" class="group">
                  <BIconClipboardCheck class="w-4" />
                  <span
                    class="hidden text-primary-500 transition-colors group-hover:text-white dark:group-hover:text-midnight-600 lg:inline ml-2"
                  >Get Started</span>
                </VButton>
              </RouterLink>
            </VTooltip>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { useAuth } from '@/store';
import layout from '@/store/layout';

import NavigationDrawer from '@/components/layout/drawers/NavigationDrawer.vue';
import NotificationDrawer from '@/components/layout/drawers/notification-drawer/NotificationDrawer.vue';
import UserDrawer from '@/components/layout/drawers/UserDrawer.vue';
import NavbarAvatarItem from '@/components/layout/navbar/NavbarAvatarItem.vue';
import NotificationDrawerTrigger from '@/components/layout/navbar/NotificationDrawerTrigger.vue';
import NavbarDarkToggle from '@/components/layout/navbar/NavbarDarkToggle.vue';
import Logo from '@/components/ui/logos/Logo.vue';
import NavigationDrawerTrigger from '@/components/layout/drawers/NavigationDrawerTrigger.vue';

import BIconClipboardCheck from '~icons/bi/clipboard-check';
import BIconBoxArrowInRight from '~icons/bi/box-arrow-in-right';

const auth = useAuth();
const appTitle = import.meta.env.VITE_APP_TITLE || 'reShape';

</script>

<style>
  .h-full-minus-navbar {
    height: calc(100vh - 4rem);
  }
</style>