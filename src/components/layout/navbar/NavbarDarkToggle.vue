<template>
  <VTooltip
    v-if="canToggle"
    :content="isDark ? 'Switch to light theme' : 'Switch to dark theme'"
    trigger-classes="h-full"
  >
    <VButton
      @click="toggleDark()"
      intent="nav"
      class="transition focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
    >
      <TransitionFadeLeftToRight class="h-full overflow-visible">
        <BIconMoonFill v-if="!isDark" />
        <BIconSunFill v-else />
      </TransitionFadeLeftToRight>
    </VButton>
  </VTooltip>
</template>

<script setup>
import { computed } from 'vue';
import BIconMoonFill from '~icons/bi/moon-fill';
import BIconSunFill from '~icons/bi/sun-fill';
import layout from '@/store/layout';

const isDark = computed(() => layout.getters.isDarkMode());
const canToggle = computed(() => layout.getters.canToggleDarkMode());

function toggleDark() {
  layout.actions.toggleDarkMode();
}
</script>