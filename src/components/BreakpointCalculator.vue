<template>
  <slot :sm="sm" :md="md" :lg="lg" :xl="xl" :xxl="xxl" />
</template>

<script setup>
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core';

const breakpoints = useBreakpoints(breakpointsTailwind);

const sm = breakpoints.greaterOrEqual('sm');
const md = breakpoints.greaterOrEqual('md');
const lg = breakpoints.greaterOrEqual('lg');
const xl = breakpoints.greaterOrEqual('xl');
const xxl = breakpoints.greaterOrEqual('2xl');
</script>
