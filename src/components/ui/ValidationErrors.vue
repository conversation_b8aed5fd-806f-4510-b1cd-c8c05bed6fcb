<template>
  <VAlert v-if="errors.length > 0" intent="danger">
    <h3 class="text-sm font-medium">Whoops! Something went wrong.</h3>
    <div class="mt-2 text-sm">
      <ul role="list" class="list-disc space-y-1 pl-5">
        <li v-for="error in errors" :key="error.index">{{ error }}</li>
      </ul>
    </div>
  </VAlert>
</template>

<script setup>
const props = defineProps({
  errors: {
    type: Array,
    required: true,
  },
});
</script>
