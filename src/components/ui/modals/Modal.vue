<template>
  <AlertDialog v-model:open="open">
    <AlertDialogTrigger as-child>
      <slot name="default" />
    </AlertDialogTrigger>
    <AlertDialogContent @pointerDownOutside="cancelled" :class="props.disableOverflow ? 'overflow-visible' :'overflow-hidden'">
      <AlertDialogHeader
        :icon="props.icon"
        class="w-full relative"
        :class="!(props.showConfirm || props.showCancel || props.showClose) ? 'mb-3' : ''"
      >
        <AlertDialogTitle v-if="$slots.title"><slot name="title" /></AlertDialogTitle>
        <AlertDialogTitle v-else>{{ props.title }}</AlertDialogTitle>
        <VButton
          v-if="props.headerClose"
          intent="modalHeaderClose"
          size="icon"
          class="absolute top-1 right-0 p-0.5"
          @click="cancelled"
        >
          <BIconX />
        </VButton>
        <AlertDialogDescription
          class="max-h-96 transition-colors scrollbar-thin"
          :class="props.disableOverflow ? 'overflow-visible' :'overflow-y-auto'"
          asChild
        >
          <slot v-if="$slots.description" name="description" />
          <div v-else class="space-y-3" v-html="props.description" />
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter v-if="props.showConfirm || props.showCancel || props.showClose">
        <template v-if="props.showConfirm">
          <AlertDialogAction v-if="props.closeOnConfirm" @click="confirmed">
            <BIconCheckLg />Confirm
          </AlertDialogAction>
          <VButton v-else intent="primary" @click="confirmed">
            <template #start>
              <BIconCheckLg />
            </template>
            Confirm
          </VButton>
        </template>
        <AlertDialogCancel v-if="props.showCancel">
          <BIconArrowReturnLeft />Cancel
        </AlertDialogCancel>
        <AlertDialogCancel v-if="props.showClose">
          <BIconArrowReturnLeft />Close
        </AlertDialogCancel>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>

<script setup lang="ts">
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/shadcn/alert-dialog';
import { AlertDialogProps } from 'reka-ui';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconCheckLg from '~icons/bi/check-lg';
import BIconInfoCircleFill from '~icons/bi/info-circle-fill';
import BIconX from '~icons/bi/x';
const props = withDefaults(
  defineProps<{
    title?: string;
    description?: string;
    icon?: object;
    showConfirm?: boolean;
    showCancel?: boolean;
    showClose?: boolean;
    headerClose?: boolean;
    disableOverflow?: boolean;
    closeOnConfirm?: boolean;
  }>(),
  {
    title: 'Modal Header',
    description: 'Modal Description',
    icon: () => BIconInfoCircleFill,
    showConfirm: true,
    showCancel: true,
    showClose: false,
    headerClose: true,
    disableOverflow: false,
    closeOnConfirm: true,
  }
);

const emit = defineEmits<{
  (event: 'confirmed'): void;
  (event: 'cancelled'): void;
  (event: 'update:open', value: boolean): void;
}>();

const open = defineModel<boolean>('open');

const cancelled = () => {
  emit('cancelled');
  open.value = false;
};

const confirmed = () => {
  emit('confirmed');
  if (props.closeOnConfirm) {
    open.value = false;
  }
};
</script>
