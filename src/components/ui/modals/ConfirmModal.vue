<template>
  <slot name="default" />
  <AlertDialog v-model:open="open">
    <AlertDialogContent @pointerDownOutside="emit('update:open', false)">
      <AlertDialogHeader :icon="props.icon">
        <AlertDialogTitle>{{ props.title }}</AlertDialogTitle>
        <AlertDialogDescription asChild>
          <div class="space-y-2" v-html="props.description" />
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogAction @click="emit('confirmed')">
          <BIconCheckLg />Confirm
        </AlertDialogAction>
        <AlertDialogCancel>
          <BIconArrowReturnLeft />Cancel
        </AlertDialogCancel>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>

<script setup lang="ts">
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/shadcn/alert-dialog';
import { AlertDialogProps } from 'reka-ui';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';
import BIconCheckLg from '~icons/bi/check-lg';
import BIconExclamationCircleFill from '~icons/bi/exclamation-circle-fill';

const props = withDefaults(
  defineProps<{
    title?: string;
    description?: string;
    icon?: object;
  }>(),
  {
    title: 'Confirm Navigation',
    description: 'Any entered data will be lost when navigating away from this page. Are you sure you wish to proceed?',
    icon: () => BIconExclamationCircleFill,
  }
);

const emit = defineEmits<{
  (event: 'confirmed'): void;
}>();

const open = defineModel<boolean>('open');
</script>
