<template>
  <div class="rounded-md shadow-md">
    <div
      v-if="$slots.header || title || description"
      class="relative flex items-center justify-start space-y-1 bg-primary-500 px-4 py-3 sm:p-6"
      :class="{ 'rounded-t-md': $slots.header || title || description }"
    >
      <div
        v-if="icon"
        class="mr-4 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-platinum-300 dark:bg-midnight-300"
      >
        <component :is="icon" class="h-6 w-6 text-primary-500" />
      </div>
      <VSectionTitle v-if="title || description">
        <template #title>{{ title }}</template>
        <template v-if="description" #description>{{ description }}</template>
      </VSectionTitle>
      <div v-else-if="$slots.header">
        <slot name="header"></slot>
      </div>
    </div>

    <div
      class="space-y-6 bg-platinum-50 p-4 dark:bg-midnight-500 sm:p-6"
      :class="{ 'rounded-t-md': !$slots.header && !(title || description), 'rounded-b-md': !$slots.footer }"
    >
      <slot name="body"></slot>
      <slot />
    </div>

    <div v-if="$slots.footer" class="bg-gray-50 px-4 py-6 sm:px-6" :class="{ 'rounded-b-md': $slots.footer }">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { type Component } from 'vue';

interface Props {
  title?: string;
  description?: string;
  icon?: Component;
}

const props = defineProps<Props>();
</script>
