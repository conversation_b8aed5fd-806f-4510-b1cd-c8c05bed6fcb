<template>
  <FormPanel
    :show="entity?.contacts?.length || (showSocials && entity?.socials?.length)"
    :required="entity?.contacts?.length || (showSocials && entity?.socials?.length)"
  >
    <template #default>
      <div>
        <h2 class="text-primary-500 font-semibold">Contact Methods</h2>
        <p class="mt-1 text-sm">Standard contact methods which would be used for direct communication. At least one contact is required.</p>
      </div>

      <AccordionContainer
        :opened="contactAccordions.filter((accordion) => accordion.isOpen).map((accordion) => accordion.id)"
        @change="openContact"
      >
        <Accordion
          v-for="({ resource: contact, id }, index) in contactAccordions"
          :id="id"
          :key="id"
        >
          <template #header>
            <div class="flex w-full justify-between">
              <span>{{ contact.name ? contact.name : 'Contact #' + (index + 1) }}</span>

              <div class="flex items-center space-x-2">
                <VBadge
                  v-if="form.errors.has(`contacts.${index}`)"
                  color="danger"
                >Requires Attention</VBadge>
                <VBadge
                  v-if="contact.is_primary"
                  color="secondary"
                >Primary</VBadge>
              </div>
            </div>
          </template>
          <template #body>
            <div class="mb-2 grid grid-cols-6 gap-6 p-3">
              <VField
                class="col-span-6 sm:col-span-4 xl:col-span-5"
                required
                :errors="form.errors.get(`contacts.${index}.method_id`)"
                label="Contact Method"
              >
                <ContactMethodCombobox v-model="contact.method_id" :contactable="entityType" />
              </VField>

              <VField class="col-span-6 sm:col-span-2 xl:col-span-1">
                <template #label>
                  <span class="block text-sm font-medium text-primary-500">Primary Contact</span>
                </template>

                <VToggle
                  v-model="contact.is_primary"
                  :disabled="contact.is_primary"
                  class="mt-2"
                  @update:model-value="
                    (value) => {
                      updateContacts({ is_primary: false });
                      updateContact(id, { is_primary: true });
                    }
                  "
                >
                  {{ contact.is_primary ? 'Primary' : 'Secondary' }}
                </VToggle>
              </VField>

              <VField
                class="col-span-6"
                label="Contact Value"
                required
                :errors="form.errors.get(`contacts.${index}.value`)"
              >
                <VInput
                  v-model="contact.value"
                  placeholder="Enter Contact Value"
                />
              </VField>

              <VField
                class="col-span-6"
                label="Contact Note"
                help="Additional notes about a contact. Example; telephone extension or receptionist name."
                :errors="form.errors.get(`contacts.${index}.name`)"
              >
                <VTextarea v-model="contact.note" placeholder="Enter Contact Value" />
              </VField>

              <div v-if="!contact.is_primary" class="col-span-6">
                <VButton
                  intent="danger"
                  @click="removeContact(id)"
                  size="sm"
                  class="w-full sm:w-auto"
                >
                  <template #start>
                    <BiTelephoneX />
                  </template>
                  Remove Contact
                </VButton>
              </div>
            </div>
          </template>
        </Accordion>
      </AccordionContainer>

      <div class="sm:flex sm:justify-between">
        <VButton
          class="w-full sm:w-auto"
          @click="createContact({ is_primary: false }, true)"
          intent="primary"
          size="sm"
        >
          <template #start>
            <BiTelephonePlus />
          </template>
          Add Contact
        </VButton>

        <TransitionFade>
          <VButton
            v-if="form?.contacts && form?.contacts?.length > 1"
            class="mt-2 w-full sm:ml-3 sm:mt-0 sm:w-auto"
            intent="danger"
            size="sm"
            @click="confirmClearContactsModalOpen = true"
          >
            <template #start>
              <BiTelephoneX />
            </template>
            Clear All Contacts
          </VButton>
        </TransitionFade>
      </div>

      <hr v-if="showSocials" class="mb-6 border-platinum-300 dark:border-midnight-700" />

      <div v-if="showSocials">
        <h2 class="text-primary-500 font-semibold">Social Media Connections</h2>
        <p class="mt-1 text-sm">Optionally you may also provide social media references where relevant.</p>
      </div>

      <AccordionContainer
        :opened="socialAccordions.filter((accordion) => accordion.isOpen).map((accordion) => accordion.id)"
        @change="openSocial"
      >
        <Accordion
          v-for="({ resource: social, id }, index) in socialAccordions"
          :id="id"
          :key="id"
        >
          <template #header>
            <div class="flex w-full justify-between">
              <span>{{ social.name ? social.name : 'Social #' + (index + 1) }}</span>

              <div class="flex items-center space-x-2">
                <VBadge
                  v-if="form.errors.has(`socials.${index}`)"
                  color="danger"
                >Requires Attention</VBadge>
              </div>
            </div>
          </template>

          <template #body>
            <div class="mb-2 grid grid-cols-6 gap-6 p-3">
              <VField
                class="col-span-6"
                required
                :errors="form.errors.get(`socials.${index}.method_id`)"
                label="Social Media Platform"
              >
                <ContactMethodCombobox
                  v-model="social.method_id"
                  :contactable="entityType"
                  social
                />
              </VField>

              <VField
                class="col-span-6"
                label="Social Handle Value"
                required
                :errors="form.errors.get(`socials.${index}.value`)"
              >
                <VInput
                  v-model="social.value"
                  placeholder="Enter Handle Value"
                />
              </VField>

              <div class="col-span-6">
                <VButton
                  intent="danger"
                  size="sm"
                  class="w-full sm:w-auto"
                  @click="removeSocial(id)"
                >
                  <template #start>
                    <BiNodeMinus />
                  </template>
                  Remove Social
                </VButton>
              </div>
            </div>
          </template>
        </Accordion>
      </AccordionContainer>

      <div class="sm:flex sm:justify-between">
        <VButton
          class="w-full sm:w-auto"
          intent="primary"
          size="sm"
          @click="createSocial({}, true)"
        >
          <template #start>
            <BiNodePlus />
          </template>
          Add Social
        </VButton>

        <TransitionFade>
          <VButton
            v-if="form?.socials && form?.socials?.length > 0"
            class="mt-2 w-full sm:ml-3 sm:mt-0 sm:w-auto"
            intent="danger"
            size="sm"
            @click="confirmClearSocialsModalOpen = true"
          >
            <template #start>
              <BiNodeMinus />
            </template>
            Clear All Socials
          </VButton>
        </TransitionFade>
      </div>

      <VConfirmModal
        @confirmed="clearContacts(1, { is_primary: true })"
        v-model:open="confirmClearContactsModalOpen"
        title="Confirm Clear Contacts"
        description="You're about to clear all contacts. Please confirm your action."
      />

      <VConfirmModal
        @confirmed="clearSocials(0)"
        v-model:open="confirmClearSocialsModalOpen"
        title="Confirm Clear Socials"
        description="You're about to clear all social references. Please confirm your action."
      />
    </template>
  </FormPanel>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import Accordion from '@/components/ui/accordions/Accordion/Accordion.vue';
import AccordionContainer from '@/components/ui/accordions/Accordion/AccordionContainer.vue';
import ContactMethodCombobox from '@/components/ui/form/composables/ContactMethodCombobox.vue';
import FormPanel from '@/components/ui/forms/tabbed/FormPanel.vue';
import ConfirmModal from '@/components/ui/modals/ConfirmModal.vue';
import { useResourceAccordion } from '@/hooks/form';
import { useVModel } from '@/hooks/model';
import BiNodeMinus from '~icons/bi/node-minus';
import BiNodePlus from '~icons/bi/node-plus';
import BiTelephonePlus from '~icons/bi/telephone-plus';
import BiTelephoneX from '~icons/bi/telephone-x';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
  /**
   * The entity object containing existing contacts and socials.
   */
  entity: {
    type: Object,
    default: null,
  },
  /**
   * The type of entity this contact panel is for (e.g., 'User', 'Company').
   */
  entityType: {
    type: String,
    required: true,
  },
  /**
   * Whether to show social contact elements.
   */
  showSocials: {
    type: Boolean,
    default: true,
  }
});

const confirmClearContactsModalOpen = ref(false);
const confirmClearSocialsModalOpen = ref(false);

const emit = defineEmits(['update:modelValue']);

// Two-way binding for form data using Vue's v-model.
const form = useVModel(props, 'modelValue', emit, {
  passive: true,
  deep: true,
});

// Manages the accordion UI and data for standard contact methods.
// The callback updates the form's contacts array whenever accordions change.
const {
  accordions: contactAccordions,     // List of contact accordions with their open state
  create: createContact,             // Adds new contact with optional initial data
  remove: removeContact,             // Removes contact by ID (except primary)
  clear: clearContacts,              // Clears all contacts except minimum required
  updateResource: updateContact,     // Updates single contact by ID
  updateResources: updateContacts,   // Batch updates all contacts
  open: openContact,                 // Handles accordion open/close state
} = useResourceAccordion(
  {
    method_id: '',
    value: '',
    note: '',
    is_primary: false,
  },
  (accordions) => (form.value.contacts = accordions.map((accordion) => accordion.resource))
);

// Similar accordion management for social media references.
// Simpler data structure: just method_id and value.
const {
  accordions: socialAccordions,
  create: createSocial,
  remove: removeSocial,
  clear: clearSocials,
  open: openSocial,
} = useResourceAccordion(
  {
    method_id: '',
    value: '',
  },
  (accordions) => (form.value.socials = accordions.map((accordion) => accordion.resource))
);

// Initialize the form:
// For new entities: creates a single primary contact
// For existing entities: loads their contacts and socials
onMounted(() => {
  if (!props.entity) {
    createContact(
      {
        is_primary: true,
      },
      true
    );
    return;
  }

  props.entity.contacts?.forEach((contact) => createContact(contact));
  if (props.showSocials) {
    props.entity.socials?.forEach((social) => createSocial(social));
  }
});

// Form success handler:
// Only runs for new entities.
// Resets the form to initial state with one primary contact.
form.value.succeeded(() => {
  if (props.entity) return;

  if (props.showSocials) {
    clearSocials(0);
  }

  clearContacts(0);
  createContact(
    {
      is_primary: true,
    },
    true
  );
});
</script>
