<template>
  <div :key="id" v-if="showing">
    <!-- Offset for Anchor -->
    <div :id="name" class="invisible relative -top-3"></div>

    <div class="section relative rounded-md shadow-md">
      <div
        class="relative flex items-center justify-start space-y-1 bg-primary-500 px-4 py-3 sm:p-6 rounded-t-md"
      >
        <div
          v-if="icon"
          class="mr-4 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-platinum-300 dark:bg-midnight-300"
        >
          <component
            :is="icon"
            class="h-6 w-6 text-primary-500"
          />
        </div>
        <div>
          <h3
            v-if="title"
            class="text-lg font-semibold leading-6 text-platinum-50 dark:text-midnight-700"
          >{{ title }}</h3>
          <p
            v-if="description"
            class="mt-1 text-sm text-primary-200 dark:text-midnight-500"
          >{{ description }}</p>
        </div>
      </div>

      <div
        class="space-y-6 bg-platinum-100 p-4 dark:bg-midnight-500 sm:p-6 text-platinum-950 dark:text-midnight-50"
        :class="{ 'rounded-t-md': !$slots.header, 'rounded-b-md': !$slots.footer }"
      >
        <slot></slot>
      </div>

      <div
        v-if="$slots.footer"
        class="bg-platinum-300 dark:bg-midnight-600 px-4 py-6 sm:px-6 text-platinum-900 dark:text-midnight-100 text-sm"
        :class="{ 'rounded-b-md': $slots.footer }"
      >
        <slot name="footer"></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { nanoid } from 'nanoid';
import { computed, inject, watch } from 'vue';

const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  label: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    default: null,
  },
  show: {
    type: [Boolean, Number],
    default: false,
  },
  required: {
    type: [Boolean, Number],
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  last: {
    type: Boolean,
    default: false,
  },
  icon: {
    type: Object,
    default: null,
  },
  showInNav: {
    type: Boolean,
    default: true,
  },
});

const id = nanoid();

const addSection = inject('addSection');
const sections = inject('sections');
const updateSection = inject('updateSection');

addSection({
  id,
  name: props.name,
  label: props.label,
  show: props.show,
  required: props.required,
  disabled: props.disabled,
  icon: props.icon,
  showInNav: props.showInNav,
});

const showing = computed(() => {
  let section = sections.find((section) => id === section.id);

  if (!section) return;

  return section.show;
});

watch(
  () => props.required,
  (value) => {
    updateSection(id, {
      required: value,
    });
  }
);

watch(
  () => props.show,
  (value) => {
    updateSection(id, {
      show: value,
    });
  }
);

watch(
  () => props.showInNav,
  (value) => {
    updateSection(id, {
      showInNav: value,
    });
  }
);
</script>
