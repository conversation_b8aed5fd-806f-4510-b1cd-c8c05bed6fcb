<template>
  <div class="relative lg:grid lg:grid-cols-12 lg:gap-x-3">
    <aside class="pb-3 lg:col-span-3 lg:px-0 lg:py-0">
      <nav
        class="sticky space-y-3"
        :class="{'top-3': !offset}"
        :style="offset ? { top: offset + 'px' } : {}"
      >
        <aside class="lg:col-span-3 lg:px-0 lg:py-0">
          <nav class="space-y-3">
            <template v-for="section in sections" :key="section.index">
              <a
                v-if="section.showInNav"
                :href="'#' + section.name"
                :class="
                  section.disabled
                    ? 'cursor-not-allowed bg-platinum-500/40 opacity-50 dark:bg-midnight-200/20'
                    : section.show
                    ? ''
                    : 'opacity-60 hover:bg-primary-200 hover:opacity-100'
                "
                class="group flex cursor-pointer items-center justify-between rounded-md bg-platinum-50 px-3 py-2 text-sm font-medium transition-colors dark:bg-midnight-300 shadow outline-none focus:ring-4 focus:ring-primary-200 dark:focus:ring-primary-500"
                :disabled="section.disabled"
              >
                <span class="flex items-center truncate text-clip">
                  <component :is="section.icon" class="mr-3 h-5 w-5 flex-shrink-0 text-primary-500" aria-hidden="true" />
                  <span class="truncate font-semibold text-primary-500">{{ section.label }}</span>
                </span>
  
                <VCheckbox
                  v-if="!section.required && !section.disabled"
                  class="flex-none pl-1"
                  :checked="section.show"
                  @update:checked="updateSection(section.id, { show: !section.show })"
                />
              </a>
            </template>
          </nav>
        </aside>
      </nav>
    </aside>

    <div class="space-y-3 lg:col-span-9">
      <TransitionGroup
        enter-active-class="transition ease-out duration-250"
        enter-from-class="transform opacity-0 transform translate-x-12"
        enter-to-class="transform opacity-100"
        leave-active-class="transition ease-in"
        leave-from-class="transform opacity-100"
        leave-to-class="transform opacity-0 transform translate-x-12"
        tag="div"
        class="space-y-3"
      >
        <slot />
      </TransitionGroup>

      <slot name="actions" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, provide } from 'vue';
import layoutStore from '@/store/layout';
const props = defineProps({
  sectionsModifiers: {
    default: () => ({}),
  },
});

const emit = defineEmits(['update:sections']);

const offset = computed(() => {
  let value = 0;

  if (layoutStore.getters.isFixedHeader()) {
    value = layoutStore.getters.fixedHeaderHeight() + 12; // Plus 0.75rem.
  }
  return value;
});

const sections = ref([]);

function addSection(section) {
  sections.value.push(section);
}

function updateSection(id, data) {
  const index = sections.value.findIndex((section) => id === section.id);
  if (index !== -1) {
    sections.value[index] = {
      ...sections.value[index],
      ...data,
    };
  }
}

watch(
  sections,
  (value) => {
    if (props.sectionsModifiers.enabled) {
      emit(
        'update:sections',
        value.filter((section) => section.show).map((section) => section.name)
      );
    }
    emit('update:sections', value);
  },
  { deep: true }
);

provide('addSection', addSection);
provide('sections', sections.value);
provide('updateSection', updateSection);



</script>

<style>
.list-enter-active,
.list-leave-active {
  transition: all 0.25s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
