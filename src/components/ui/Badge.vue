<template>
  <span
    :class="colorClasses"
    class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium shadow select-none transition-shadow tracking-wide"
  >
    <slot />
  </span>
</template>

<script>
export default {
  name: 'Badge',
  props: {
    color: {
      type: String,
      default: 'primary',
    },
  },
  computed: {
    colorClasses() {
      return {
        primary: 'bg-primary-300 text-primary-800 dark:bg-primary-500 dark:text-primary-900',
        secondary: 'bg-platinum-50 dark:bg-midnight-500 text-primary-500 dark:text-primary-400',
        success: 'bg-success-100/75 text-success-600 dark:bg-success-500/75 dark:text-success-900',
        danger: 'bg-danger-100 text-danger-600 dark:bg-danger-500/75 dark:text-danger-800',
        warning: 'bg-warning-100 text-warning-600 dark:bg-warning-500/75 dark:text-warning-950',
        info: 'bg-info-100 text-info-600 dark:bg-info-500/75 dark:text-info-950',
      }[this.color];
    },
  },
};
</script>
