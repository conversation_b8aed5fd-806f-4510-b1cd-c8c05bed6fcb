<template>
  <DropdownMenuItem v-bind="forwardedProps">
      <div
        v-if="$slots.icon"
        class="[&>*]:mr-2 [&>*]:h-4 [&>*]:w-4 [&>*]:text-gray-600 [&>*]:group-hover:text-current [&>*]:group-focus:text-current"
      >
        <slot name="icon" />
      </div>

      <slot />
  </DropdownMenuItem>
</template>

<script setup>

import { useForwardProps } from 'reka-ui';
import { DropdownMenuItem } from '@/components/ui/shadcn/dropdown-menu';
import { computed } from 'vue';
const props = defineProps({
  as: {
    type: String,
    default: 'button',
  },
});

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props;

  return delegated;
});

const forwardedProps = useForwardProps(delegatedProps);
</script>
