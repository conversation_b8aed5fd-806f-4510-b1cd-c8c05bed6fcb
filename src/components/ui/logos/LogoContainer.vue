<template>
  <component
    :is="element"
    :to="element == 'a' || element == 'router-link' ? url : false"
    class="logo-container flex select-none items-center px-3 py-1"
    :class="type == 'nav' ? 'header-hover ' + containerClasses : containerClasses"
  >
    <logo :display="display" :logo-classes="logoClasses" />
  </component>
</template>

<script>
import Logo from '@/components/ui/logos/Logo.vue';

export default {
  name: 'LogoContainer',
  components: {
    Logo,
  },
  props: {
    element: {
      // The element of the logo container
      type: String,
      default: 'div',
    },
    type: {
      // The type of container this needs to be (such as nav or inline)
      type: String,
      default: 'inline',
    },
    hover: {
      // Whether or not we expect a "hover effect" when cursor on top of element
      type: Boolean,
      default: false,
    },
    url: {
      // Location to direct the user in the case of a anchor or router-link
      type: String,
      default: '/',
    },
    display: {
      // Whether or not we want to display certain parts.
      type: Object,
      default: {
        text: true,
        logo: true,
      },
    },
    logoClasses: {
      // Classes provided to the logo component.
      type: String,
      default: null,
    },
    containerClasses: {
      // Classes to be used for the container, if any.
      type: String,
      default: null,
    },
  },
  data() {
    return {};
  },
  computed: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
@import '@/assets/scss/variables/colors';
.logo-container {
  text-decoration: none;
  text-align: center;
  line-height: 2.75rem;
  color: $ascent5;
  -webkit-transition: all 250ms cubic-bezier(0.645, 0.045, 0.355, 1);
  -moz-transition: all 250ms cubic-bezier(0.645, 0.045, 0.355, 1);
  -o-transition: all 250ms cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: all 250ms cubic-bezier(0.645, 0.045, 0.355, 1);
}

.header-hover {
  -webkit-transition: all 250ms cubic-bezier(0.645, 0.045, 0.355, 1);
  -moz-transition: all 250ms cubic-bezier(0.645, 0.045, 0.355, 1);
  -o-transition: all 250ms cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: all 250ms cubic-bezier(0.645, 0.045, 0.355, 1);
}

.header-hover:hover {
  background-color: $ascent5;
  color: $ascent1;
}
</style>
