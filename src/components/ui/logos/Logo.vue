<template>
  <div class="flex" :class="wrapperClasses">
    <div
      class="hidden select-none font-major text-2xl uppercase sm:block transition-colors font-light"
      :class="customClasses ? customClasses : 'text-primary-500 group-hover:text-platinum-50 dark:group-hover:text-midnight-600'"
    >
      <span>{{ logoText }}</span>
    </div>
    <div
      class="select-none font-major text-3xl uppercase sm:hidden transition-colors font-light"
      :class="customClasses ? customClasses : 'text-primary-500 group-hover:text-platinum-50 dark:group-hover:text-midnight-600'"
    >
      <span>R</span>
    </div>
  </div>
</template>

<script setup>

defineProps({
  customClasses: {
    type: String,
    default: '',
  },
  wrapperClasses: {
    type: String,
    default: '',
  },
  logoText: {
    type: String,
    default: 'reShape',
  },
});
</script>
