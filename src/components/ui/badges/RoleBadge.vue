<template>
  <span class="inline-flex items-center rounded-full px-2.5 py-1 text-xs font-semibold" :class="badgeMode[0]">
    <span class="mr-1.5 h-2.5 w-2.5 rounded-full ring-1" :class="badgeMode[1]" :style="{ backgroundColor: badgeColor }"></span>
    <slot />
  </span>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  color: {
    type: String,
    default: '#A3A3A3',
  },
  mode: {
    type: String,
    default: 'dark',
  },
});

const badgeColor = computed(() => props.color || '#A3A3A3');

const badgeMode = computed(() => {
  return props.mode === 'light'
    ? ['bg-primary-300 text-primary-700', 'ring-primary-700/75']
    : ['bg-primary-600 text-primary-100', 'ring-primary-700/50'];
});
</script>
