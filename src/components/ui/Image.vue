<template>
  <div ref="imageRef" :class="cn('relative h-full', containerClass)">
    <VSkeleton
      v-if="showSkeleton && !imageLoaded && !imageError"
      :class="cn('absolute inset-0 w-full h-full', skeletonClass)"
    />
    
    <!-- Generated SVG Background (fallback). -->
    <TransitionFade>
      <div
        v-if="(!showSkeleton || imageError) && svgGenerated && !imageLoaded && showFallback"
        :class="cn('absolute inset-0 bg-cover bg-center', imageClass)"
        :style="{ backgroundImage: `url('data:image/svg+xml;utf8,${svgContent}')` }"
      />
    </TransitionFade>
    
    <!-- Actual img -->
    <TransitionFade :duration="2000">
      <img
        v-if="imageLoaded && !imageError"
        :src="src"
        :alt="alt"
        :class="cn('absolute inset-0 w-full h-full object-cover object-center', imageClass)"
        @load="onImageLoad"
        @error="onImageError"
      />
    </TransitionFade>
    
    <!-- Hidden img for preloading when intersecting. -->
    <img 
      v-if="props.lazyLoad && isIntersecting && !imageLoaded && !imageError"
      :src="src" 
      @load="onImageLoad" 
      @error="onImageError"
      class="hidden" 
      :alt="alt"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useIntersectionObserver, useDark } from '@vueuse/core';
import { generateSvg } from '@/util/imageUtils';
import { cn } from '@/lib/utils';
import VSkeleton from './Skeleton.vue';
import TransitionFade from '@/components/transitions/TransitionFade.vue';

const props = defineProps({
  src: {
    type: [String, null, undefined, Boolean],
    required: false,
    default: null,
  },
  alt: {
    type: String,
    default: 'Image',
  },
  class: {
    type: [String, Array, Object],
    default: '',
  },
  containerClass: {
    type: [String, Array, Object],
    default: '',
  },
  skeletonClass: {
    type: [String, Array, Object],
    default: '',
  },
  lazyLoad: {
    type: Boolean,
    default: true,
  },
  showSkeleton: {
    type: Boolean,
    default: true,
  },
  showFallback: {
    type: Boolean,
    default: true,
  },
});

// Refs
const imageRef = ref(null);
const imageLoaded = ref(false);
const imageError = ref(false);
const svgContent = ref('');
const svgGenerated = ref(false);
const isDark = useDark();
const computedStyle = ref('');

// Intersection Observer for lazy loading
const { isIntersecting, stop } = useIntersectionObserver(
  imageRef,
  ([{ isIntersecting }]) => {
    if (isIntersecting && props.lazyLoad) {
      loadImage();
    }
  },
  {
    threshold: 0.1,
    rootMargin: '200px',
  }
);

// Computed properties
const imageClass = computed(() => props.class);

// Helper function to check if src is valid
const isValidSrc = (src) => {
  return src && typeof src === 'string' && src.trim() !== '';
};

// Methods
const loadImage = () => {
  // Check if src is invalid first
  if (!isValidSrc(props.src)) {
    imageError.value = true;
    imageLoaded.value = false;
    return;
  }
  
  if (imageLoaded.value || imageError.value) return;
  
  const img = new Image();
  img.src = props.src;
  img.onload = onImageLoad;
  img.onerror = onImageError;
};

const onImageLoad = () => {
  imageLoaded.value = true;
  imageError.value = false;
  stop(); // Stop observing once loaded
};

const onImageError = () => {
  imageError.value = true;
  imageLoaded.value = false;
  console.warn(`Failed to load image: ${props.src}`);
  stop(); // Stop observing on error
};

// Generate fallback SVG (using utility function)
const generateSvgContent = () => {
  svgContent.value = generateSvg(isDark.value, {
    width: 800,
    height: 600,
    variance: 0.75,
    encodeUri: true
  });
  svgGenerated.value = true;
};

// Lifecycle
onMounted(() => {
  // Create a mutation observer to watch for theme changes
  const observer = new MutationObserver(() => {
    computedStyle.value = getComputedStyle(document.body).getPropertyValue('--primary-500');
  });
  observer.observe(document.body, { attributes: true, attributeFilter: ['style', 'class'] });
  
  onUnmounted(() => observer.disconnect());
  
  // Generate SVG on mount (always generate for potential fallback or immediate display)
  generateSvgContent();
  
  // Check if src is invalid on mount
  if (!isValidSrc(props.src)) {
    imageError.value = true;
    return;
  }
  
  // If lazy loading is disabled, load immediately
  if (!props.lazyLoad) {
    loadImage();
  }
});

// Watch for theme changes to regenerate SVG
watch([isDark, computedStyle], () => {
  generateSvgContent(); // Regenerate SVG when theme changes
});

// Watch for src changes
watch(() => props.src, () => {
  imageLoaded.value = false;
  imageError.value = false;
  
  // Check if new src is invalid
  if (!isValidSrc(props.src)) {
    imageError.value = true;
    return;
  }
  
  if (!props.lazyLoad) {
    loadImage();
  }
});
</script>
