<template>
  <div v-if="value" class="icon inline-block" v-html="value" />

  <img v-else-if="url" :src="url" />

  <i v-else :class="className" />
</template>

<script>
export default {
  name: 'Icon',
  props: {
    icon: {
      type: String,
      default: '',
    },
    prefix: {
      type: String,
      required: false,
    },
    url: {
      type: String,
      default: null,
    },
    value: {
      type: String,
      default: null,
    },
    iconClasses: {
      type: String,
      default: '',
    },
  },
  computed: {
    className() {
      const className = {};
      className[`${this.prefix}`] = true;
      className[`${this.prefix}-${this.icon}`] = true;
      return className;
    },
  },
};
</script>
