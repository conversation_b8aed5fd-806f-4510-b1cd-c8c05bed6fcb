<template>
  <div class="flex items-center justify-center">
  <VTooltip :content="tooltipContent">
    <button
      type="button"
      class="inline-flex items-center justify-center p-0.5 text-platinum-500 transition-colors hover:text-primary-500 dark:text-midnight-200 dark:hover:text-primary-500"
      @click="copyToClipboard"
    >
      <TransitionFade>
        <BIconClipboardTimes v-if="cantCopy" class="w-5 text-danger-400" />
        <BIconClipboardCheck v-else-if="copied" class="w-5 text-success-500" />
        <BIconClipboard v-else class="w-5" />
      </TransitionFade>
    </button>
  </VTooltip>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import clipboardy from 'clipboardy';
import BIconClipboard from '~icons/bi/clipboard2';
import BIconClipboardCheck from '~icons/bi/clipboard2-check';
import BIconClipboardTimes from '~icons/bi/clipboard2-x';

const props = defineProps({
  value: {
    type: [String, Number],
    required: true,
  },
});

const copied = ref(false);
const cantCopy = ref(false);

// Computed value to show appropriate tooltip
const tooltipContent = computed(() => {
  if (cantCopy.value) {
    return 'Failed to copy';
  }
  return copied.value ? 'Copied!' : 'Copy';
});

const copyToClipboard = async () => {
  try {
    // Convert the value to a string if it's not already
    const value = String(props.value);
    await clipboardy.write(value);
    copied.value = true;
    setTimeout(() => {
      copied.value = false;
    }, 2000);
  } catch (err) {
    cantCopy.value = true;
    setTimeout(() => {
      cantCopy.value = false;
    }, 2000);
    if (err instanceof TypeError && err.message.includes("(reading 'writeText')")) {
      console.error('Failed to copy text. Note that this feature requires a secure context (https or localhost).\n', err);
    } else {
      console.error('Failed to copy text:', err);
    }
    
  }
};
</script>
