<template>
  <div
    class="rounded-lg bg-transparent [&_dl_div:first-of-type_dt_.accordion-btn]:rounded-t-md [&_dl_div:last-of-type_dd]:rounded-b-md [&_dl_div:last-of-type_dt_.accordion-btn:not(.open)]:rounded-b-md"
  >
    <dl>
      <slot />
    </dl>
  </div>
</template>

<script setup>
import { computed, provide, ref } from 'vue';

const props = defineProps({
  opened: {
    type: [Number, String, Array],
    default: null,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['change']);

const localOpened = ref([]);
const accordionInstances = ref([]);

/**
 * List of opened IDs.
 */
const state = computed({
  set(value) {
    if (props.opened) {
      emits('change', value);
      return;
    }
    localOpened.value = value;
  },
  get() {
    return props.opened ? props.opened : localOpened.value;
  },
});

/**
 * Open an accordion by adding it to the opened list.
 */
const open = (id) => {
  state.value = [...(props.multiple ? state.value : []), id];
};

/**
 * Close an accordion by removing it from the opened list.
 */
const close = (id) => {
  state.value = state.value.filter((_id) => _id !== id);
};

/**
 * Register an accordion instance
 */
const registerAccordion = (id) => {
  if (!accordionInstances.value.includes(id)) {
    accordionInstances.value.push(id);
  }
};

/**
 * Check if an accordion is the last one
 */
const isLastAccordion = (id) => {
  const index = accordionInstances.value.indexOf(id);
  return index === accordionInstances.value.length - 1;
};

provide('state', () => state.value);
provide('open', open);
provide('close', close);
provide('registerAccordion', registerAccordion);
provide('isLastAccordion', isLastAccordion);
</script>
