<template>
  <Disclosure as="div">
    <dt class="text-lg" :class="[(isOpen || !isLast) ? 'border-b' : '', isOpen ? 'border-primary-500' : 'border-transparent']">
      <DisclosureButton
        class="accordion-btn group/header flex w-full cursor-pointer items-center justify-between bg-primary-500 px-3 py-2 text-platinum-50 transition-all hover:bg-primary-500 dark:text-midnight-500"
        :class="isOpen ? 'open' : ''"
        @click="toggle"
      >
        <slot name="header" />
        <span class="ml-2 flex h-7 items-center">
          <div
            class="transition-color flex h-8 w-8 items-center rounded-full p-2 text-white dark:text-midnight-500 duration-200 group-hover/header:bg-primary-600"
            :class="isOpen ? 'rotate-180' : ''"
          >
            <BiChevronDown class="h-4" />
          </div>
        </span>
      </DisclosureButton>
    </dt>

    <VCollapse :when="isOpen" class="duration-250 transition-all" :on-collapsed="() => (collapsed = true)">
      <DisclosurePanel
        v-if="!collapsed"
        as="dd"
        class="w-full border-x bg-platinum-300 px-3 py-2 dark:border-x-0 dark:bg-midnight-400"
        :static="true"
        :class="isOpen ? 'border-b dark:border-b-0' : ''"
      >
        <slot name="body" />
      </DisclosurePanel>
    </VCollapse>
  </Disclosure>
</template>

<script setup>
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue';
import { nanoid } from 'nanoid';
import { computed, inject, onMounted, ref } from 'vue';

import BiChevronDown from '~icons/bi/chevron-down';

const props = defineProps({
  id: {
    type: [String, Number],
    default: null,
  },
});

const uuid = ref(null);

onMounted(() => {
  uuid.value = props.id ? props.id : nanoid();
  registerAccordion(uuid.value);
});

const state = inject('state');
const open = inject('open');
const close = inject('close');
const registerAccordion = inject('registerAccordion');
const isLastAccordion = inject('isLastAccordion');

const isOpen = computed(() => state().includes(uuid.value));
const isLast = computed(() => isLastAccordion(uuid.value));

const collapsed = ref(true);

onMounted(() => {
  if (isOpen.value) collapsed.value = false;
});

const toggle = () => {
  if (isOpen.value) {
    close(uuid.value);
    return;
  }

  collapsed.value = false;
  open(uuid.value);
};
</script>
