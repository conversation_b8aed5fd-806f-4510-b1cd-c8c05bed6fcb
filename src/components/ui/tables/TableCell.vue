<script setup>
const props = defineProps({
  value: {
    type: [String, Number, Boolean],
    required: false,
  },
  truncate: {
    type: Boolean,
    default: true,
  },
});

const removeHtmlCharacters = (value) => {
  if (!value) return value;
  if (typeof value == 'boolean') return value;
  return value?.replace(/<[^>]*>?/g, '');
};

</script>

<template>
  <div :class="{ 'max-w-32 sm:max-w-48 md:max-w-52 lg:max-w-64 xl:max-w-72 truncate': truncate }">
    <slot v-if="$slots.default" />
    <template v-else-if="value !== null && value !== undefined && value !== ''">
      {{ removeHtmlCharacters(value) }}
    </template>
    <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
  </div>
</template>
