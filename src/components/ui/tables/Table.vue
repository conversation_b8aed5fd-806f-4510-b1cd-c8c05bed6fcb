<script setup>
import {
  FlexRender,
  getCoreRowModel,
  useVueTable,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
} from '@tanstack/vue-table';
import { computed, ref, useSlots } from 'vue';
import { rankItem } from '@tanstack/match-sorter-utils';

import BIconChevronUp from '~icons/bi/chevron-up';
import BIconChevronDown from '~icons/bi/chevron-down';
import BIconChevronExpand from '~icons/bi/chevron-expand';
import BIconDatabaseX from '~icons/bi/database-x';

const props = defineProps({
  title: {
    type: String,
    required: false,
  },
  records: {
    type: Array,
    default: [],
  },
  columns: {
    type: Array,
    required: true,
  },
  totalRecords: {
    type: Number,
    required: false,
  },
  perPage: {
    type: Number,
    default: -1,
  },
  page: {
    type: Number,
    default: 0,
  },
  paginated: {
    type: Boolean,
    default: false,
  },
  sortable: {
    type: Boolean,
    default: false,
  },
  searchable: {
    type: Boolean,
    default: false,
  },
  controlled: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  showFooter: {
    type: Boolean,
    default: false,
  },
  headerLoading: {
    type: Boolean,
    default: false,
  },
  defaultSort: {
    type: Array,
    default: [],
  },
  rowModelCore: {
    type: Boolean,
    default: false,
  }
});

const emit = defineEmits(['sort', 'paginate', 'search']);

/**
 * The table's sorting state. Default via defaultSort prop.
 */
const sorting = ref(props.defaultSort);

/**
 * The table's pagination state.
 */
const pagination = ref({
  pageSize: props.perPage,
  pageIndex: props.page,
});

/**
 * The table's global search filter.
 */
const globalFilter = ref('');

/**
 * Get information about the table like total number of records,
 * current subset index of records being displayed etc.
 */
const paginationContext = computed(() => {
  let totalRecords = props.totalRecords ? props.totalRecords : table.getPrePaginationRowModel().rows.length;
  let pageCount = Math.floor(
    (totalRecords + table.getState().pagination.pageSize - 1) / table.getState().pagination.pageSize
  );
  let from =
    (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize -
    (table.getState().pagination.pageSize - 1);
  let to = Math.min(from + table.getState().pagination.pageSize - 1, totalRecords);

  return { totalRecords, pageCount, from, to };
});

/**
 * Determine if the table paginator should be displayed.
 */
const showingPagination = computed(() => {
  return props.paginated;
});

/**
 * Determine if the table's card header should be disabled
 */
const showingHeader = computed(() => {
  return props.searchable || props.title || slots.title;
});

/**
 * Determine if the table's card footer should be displayed.
 */
const showingFooter = computed(() => {
  return !(!props.paginated && !props.showFooter);
});

/**
 * Fuzzy search filter to search through table data.
 */
const fuzzyFilter = (row, columnId, value, addMeta) => {
  const itemRank = rankItem(row.getValue(columnId), value);

  addMeta({
    itemRank,
  });

  return itemRank.passed;
};

const slots = useSlots();
const table = useVueTable({
  get columns() {
    return props.columns;
  },
  get data() {
    return props.records;
  },
  get pageCount() {
    return paginationContext.pageCount;
  },
  state: {
    get sorting() {
      return sorting.value;
    },
    get pagination() {
      return pagination.value;
    },
    get globalFilter() {
      return globalFilter.value;
    },
  },
  onSortingChange: (updater) => {
    sorting.value = typeof updater === 'function' ? updater(sorting.value) : updater;

    if (!props.controlled) return;

    const joined = sorting.value.map((column) => (column.desc ? '-' : '') + column.id).join(',');

    emit('sort', joined);
  },
  onPaginationChange: (updater) => {
    pagination.value = typeof updater === 'function' ? updater(pagination.value) : updater;

    if (!props.controlled) return;

    emit('paginate', pagination.value.pageIndex + 1, pagination.value.pageSize);
  },
  onGlobalFilterChange: (updater) => {
    globalFilter.value = typeof updater === 'function' ? updater(globalFilter.value) : updater;

    if (!props.controlled) return;

    emit('search', globalFilter.value);
  },
  filterFns: {
    fuzzy: fuzzyFilter,
  },
  globalFilterFn: fuzzyFilter,
  manualSorting: props.controlled,
  manualPagination: props.controlled,
  manualFiltering: props.controlled,
  columnResizeMode: 'onChange',
  getCoreRowModel: getCoreRowModel(),
  getSortedRowModel: getSortedRowModel(),
  getFilteredRowModel: getFilteredRowModel(),
  getPaginationRowModel: getPaginationRowModel(),
  debugTable: true,
  debugHeaders: true,
  debugColumns: true,
  enableColumnResizing: true,
  enableSorting: props.sortable,
});
</script>

<template>
  <div class="relative overflow-visible">
    <div class="overflow-hidden shadow sm:rounded-md">
      <div
        v-if="showingHeader"
        class="rounded-t-md bg-platinum-50 px-4 py-6 dark:bg-midnight-600 sm:flex sm:items-center sm:justify-between sm:px-6 space-y-2 sm:space-y-0"
      >
        <div>
          <slot v-if="props.title || $slots.title" name="title">
            <h3 class="text-lg font-semibold leading-6 text-primary-500">{{ title }}</h3>
          </slot>
        </div>
        <div class="sm:flex gap-3">
          <slot name="actions" />
          <VSearchInput
            wrapper-classes="mt-4 sm:mt-0 w-full block sm:w-64"
            v-if="searchable"
            :value="globalFilter"
            clearable
            @input="(event) => table.setGlobalFilter(String(event.target.value))"
            @clear="table.setGlobalFilter()"
          />
        </div>
      </div>
      <div class="relative overflow-auto transition-colors scrollbar-thin">
        <table
          class="min-w-full"
          :style="{
            width: table.getCenterTotalSize(),
          }"
          :class="{ 'rounded-t-md': !showingHeader, 'rounded-b-md': !showingFooter }"
        >
          <thead class="bg-primary-500">
            <tr v-for="headerGroup in table.getHeaderGroups()" :key="headerGroup.id">
              <th
                v-for="header in headerGroup.headers"
                :key="header.id"
                :colSpan="header.colSpan"
                :class="{ 'cursor-pointer select-none hover:text-platinum-50': header.column.getCanSort() }"
                :style="{ width: header.getSize() + 'px' }"
                @click="header.column.getToggleSortingHandler()?.($event)"
                scope="col"
                class="group relative px-6 py-3 text-left text-xs font-bold uppercase tracking-wide text-primary-200 dark:text-midnight-500"
              >
                <div v-if="!header.isPlaceholder" class="flex items-center justify-between">
                  <FlexRender :render="header.column.columnDef.header" :props="header.getContext()" />
                  <div v-if="header.column.getCanSort()" class="group px-2">
                    <template v-if="header.column.getIsSorted()">
                      <BIconChevronUp v-if="header.column.getIsSorted() == 'desc'" class="h-4 w-4 text-platinum-50 dark:text-midnight-950" />
                      <BIconChevronDown v-else class="h-4 w-4 text-platinum-50 dark:text-midnight-950" />
                    </template>
                    <template v-else>
                      <BIconChevronExpand
                        class="h-4 w-4 text-platinum-50 dark:text-midnight-950 opacity-25 transition-opacity group-hover:opacity-100"
                      />
                    </template>
                  </div>
                  <div
                    v-if="header.column.getCanResize()"
                    @mousedown="header.getResizeHandler()?.($event)"
                    @touchstart="header.getResizeHandler()?.($event)"
                    class="absolute right-0 top-0 h-full w-1 cursor-col-resize touch-none select-none opacity-0 transition group-hover:bg-primary-600 group-hover:opacity-100 hover:bg-primary-600"
                    :class="{ '!bg-platinum-50 !opacity-100 dark:!bg-midnight-500': header.column.getIsResizing() }"
                  />
                </div>
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-platinum-300 bg-platinum-50 dark:divide-midnight-400 dark:bg-midnight-600 relative">
            <template v-if="loading">
              <tr>
                <td class="relative" :colspan="table.getAllColumns().length">
                  <table class="w-full">
                    <tbody>
                      <tr
                        v-for="skeletonIndex in 3"
                        :key="`skeleton-${skeletonIndex}`"
                        class="bg-platinum-50 dark:bg-midnight-600 transition-opacity duration-300 ease-in-out animate-in fade-in"
                      >
                        <td class="px-6 py-4 w-full">
                          <div class="flex gap-3 items-center">
                            <VSkeleton class="shrink-0 w-10 h-10 rounded-full" />
                            <div class="flex flex-col space-y-2 w-full">
                              <VSkeleton class="h-3 w-full sm:w-2/3" />
                              <VSkeleton class="w-full h-3" />
                            </div>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <div class="absolute inset-0 flex items-center justify-center">
                    <VSpinner size="xl" hideSpinner />
                  </div>
                </td>
              </tr>
            </template>
            <template v-else>
              <tr
                v-for="row in (rowModelCore ? table.getCoreRowModel().rows : table.getRowModel().rows)"
                :key="row.id"
                class="duration-300 transition-all ease-in-out odd:bg-platinum-50 even:bg-platinum-200 hover:bg-platinum-300 dark:odd:bg-midnight-600 dark:even:bg-midnight-700 dark:hover:bg-midnight-800 animate-in fade-in"
              >
                <td
                  v-for="cell in row.getVisibleCells()"
                  :style="{ width: cell.column.getSize() + 'px' }"
                  :key="cell.id"
                  role="cell"
                  class="whitespace-nowrap px-6 py-4 text-platinum-950 dark:text-midnight-50"
                  :class="cell.column.columnDef.class"
                >
                  <flex-render :render="cell.column.columnDef.cell" :props="cell.getContext()" />
                </td>
              </tr>
              <tr v-if="!(rowModelCore ? table.getCoreRowModel().rows : table.getRowModel().rows).length" class="animate-in fade-in duration-300">
                <td colspan="100%">
                  <slot name="empty">
                    <div class="w-full px-4 py-6 sm:px-6 text-platinum-800 dark:text-midnight-200">
                      <div class="flex items-center gap-2 justify-center">
                        <BIconDatabaseX class="w-6 h-6 text-primary-300 dark:text-primary-600" />
                        <p v-if="globalFilter">No records match the provided criteria.</p>
                        <p v-else>No records to display.</p>
                      </div>
                    </div>
                  </slot>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>

      <div
        v-if="showingFooter"
        class="flex min-h-[67px] items-center justify-between rounded-b-md bg-platinum-50 px-4 py-3 dark:bg-midnight-600 sm:px-6"
      >
        <div class="flex w-full items-center justify-between">
          <div>
            <p v-if="paginationContext.totalRecords" class="text-sm text-platinum-900 dark:text-midnight-50">
              Displaying:
              <span class="text-primary-500">
                <span class="font-bold">{{ paginationContext.from }}</span
                >-<span class="font-bold">{{ paginationContext.to }}</span>
              </span>
              of
              <span class="font-bold text-primary-500">{{ paginationContext.totalRecords }}</span>
            </p>
          </div>

          <div v-if="showingPagination" class="flex items-center space-x-3">
            <VSelect
              class="flex shadow shadow-black/20"
              :value="table.getState().pagination.pageSize"
              @change="(event) => table.setPageSize(Number(event.target.value))"
            >
              <option value="10">10</option>
              <option value="15">15</option>
              <option value="20">20</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option v-if="![10, 15, 20, 25, 50].includes(perPage)" :value="perPage">{{ perPage }}</option>
            </VSelect>

            <VPaginator
              class="sm:hidden"
              simple
              @change="(page) => table.setPageIndex(page - 1)"
              :total="paginationContext.totalRecords"
              :per-page="table.getState().pagination.pageSize"
              :current="Number(table.getState().pagination.pageIndex) + 1"
            />

            <VPaginator
              class="hidden shadow shadow-black/20 sm:flex"
              @change="(page) => table.setPageIndex(page - 1)"
              :total="paginationContext.totalRecords"
              :per-page="table.getState().pagination.pageSize"
              :current="Number(table.getState().pagination.pageIndex) + 1"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
