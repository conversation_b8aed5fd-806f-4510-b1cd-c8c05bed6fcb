<template>
  <div
    class="relative flex items-center rounded text-gray-500 ring-1 ring-inset ring-platinum-200 transition focus-within:caret-primary-400 dark:ring-transparent"
    :class="[
      wrapperClass,
      wrapperClasses({
        size,
        invalid: field.invalid || invalid,
        disabled
      } as WrapperVariantProps),
    ]"
  >
    <span v-if="$slots.start" class="-ml-1 pr-2 flex items-center">
      <slot name="start" />
    </span>
    <input
      :id="field.id"
      v-model="proxy"
      v-bind="$attrs"
      :type="type"
      :disabled="disabled"
      :required="field.required"
      class="w-full min-w-[80px] appearance-none border-0 bg-transparent p-0 text-primary-500 outline-none placeholder:text-platinum-700 autofill:bg-white read-only:bg-transparent focus:outline-none focus:ring-0 disabled:cursor-not-allowed disabled:bg-transparent dark:text-midnight-50 dark:placeholder:text-midnight-200"
      :class="inputClasses({ size })"
      :size="1"
    />
    <BiXCircleFill
      v-if="clearable && proxy"
      class="pointer-events-auto absolute right-3 h-4 w-4 cursor-pointer text-primary-500/50 transition-colors hover:text-primary-500"
      @click="clear"
    />
    <span v-if="$slots.suffix" class="-mr-1 pl-2">
      <slot name="end" />
    </span>
  </div>
</template>

<script lang="ts">
export default {
  inheritAttrs: false,
};
</script>

<script lang="ts" setup>
import { cva, VariantProps } from 'class-variance-authority';
import { useVModel } from '@vueuse/core';
import { inject } from 'vue';
import BiXCircleFill from '~icons/bi/x-circle-fill';

const inputClasses = cva('', {
  variants: {
    size: {
      sm: 'text-sm',
      base: 'text-base',
      lg: 'text-base',
    },
  },
});

const wrapperClasses = cva('', {
  variants: {
    size: {
      sm: 'py-1.5 px-4',
      base: 'py-2 px-4',
      lg: 'py-3 px-4',
    },
    invalid: {
      true: 'ring-danger-400 dark:ring-danger-500',
      false: 'ring-1',
    },
    disabled: {
      true: 'cursor-not-allowed',
      false: 'bg-platinum-300 dark:bg-midnight-300 active:ring-primary-400 active:ring-2 focus-within:ring-primary-400 focus-within:ring-2',
    },
  },
  compoundVariants: [
    {
      invalid: false,
      disabled: true,
      class: 'bg-gray-100 ring-gray-300',
    },
    {
      invalid: false,
      disabled: false,
      class: 'ring-gray-300',
    },
    {
      invalid: false,
      disabled: false,
      class:
        'hover:ring-primary-400 dark:hover:ring-primary-600 focus:ring-primary-400 dark:focus:ring-primary-600 active:ring-primary-400 dark:active:ring-primary-600 focus-within:ring-primary-400 dark:focus-within:ring-primary-600',
    },
  ],
});

type WrapperVariantProps = VariantProps<typeof wrapperClasses>;

interface InputProps {
  modelValue?: string | number;
  size?: WrapperVariantProps['size'];
  invalid?: boolean;
  wrapperClass?: string;
  type?: string;
  disabled?: boolean;
  required?: boolean;
  clearable?: boolean;
}

const props = withDefaults(defineProps<InputProps>(), {
  size: 'base',
  invalid: false,
  type: 'text',
  disabled: false,
  required: false,
  clearable: false,
});

const emit = defineEmits(['update:modelValue']);
const proxy = useVModel(props, 'modelValue', emit);
const field = inject('field', props);

const clear = () => {
  proxy.value = '';
  emit('update:modelValue', '');
};
</script>
