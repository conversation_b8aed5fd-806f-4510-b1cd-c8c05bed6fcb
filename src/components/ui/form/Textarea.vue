<template>
  <div
    class="pr-2 py-2 flex items-center rounded text-gray-500 ring-1 ring-inset ring-platinum-200 transition focus-within:caret-primary-400 dark:ring-transparent"
    :class="[
      wrapperClass,
      wrapperClasses({
        invalid: field.invalid || invalid,
        disabled
      }),
    ]">
    <span v-if="$slots.start" class="-ml-1 pr-2">
      <slot name="start" />
    </span>

    <textarea
      :id="field.id"
      ref="textarea"
      @input="resize"
      v-model="proxy"
      v-bind="$attrs"
      :disabled="disabled"
      :required="field.required"
      :class="[
        props.resize ? '' : 'resize-none',
        props.maxRowsVisible != null ? 'overflow-auto' : 'overflow-hidden',
        'w-full min-w-[80px] appearance-none border-0 bg-transparent px-2 py-0 -my-0.5 text-base text-primary-500 outline-none',
        'placeholder:text-platinum-700 autofill:bg-white read-only:bg-transparent focus:outline-none focus:ring-0 disabled:cursor-not-allowed disabled:bg-transparent',
        'dark:text-midnight-50 dark:placeholder:text-midnight-200 scrollbar-thin',
      ]"
      :rows="rows"
    ></textarea>
  </div>
</template>

<script lang="ts" setup>
import { cva, VariantProps } from 'class-variance-authority';
import { useVModel } from '@vueuse/core';
import { inject, ref, onMounted, watch } from 'vue';

const wrapperClasses = cva('', {
  variants: {
    invalid: {
      true: 'ring-danger-400 dark:ring-danger-500',
      false: 'ring-1',
    },
    disabled: {
      true: 'cursor-not-allowed',
      false: 'bg-platinum-300 dark:bg-midnight-300 active:ring-primary-400 active:ring-2 focus-within:ring-primary-400 focus-within:ring-2',
    },
  },
  compoundVariants: [
    {
      invalid: false,
      disabled: true,
      class: 'bg-gray-100 ring-gray-300',
    },
    {
      invalid: false,
      disabled: false,
      class: 'ring-gray-300',
    },
    {
      invalid: false,
      disabled: false,
      class:
        'hover:ring-primary-400 dark:hover:ring-primary-600 focus:ring-primary-400 dark:focus:ring-primary-600 active:ring-primary-400 dark:active:ring-primary-600 focus-within:ring-primary-400 dark:focus-within:ring-primary-600',
    },
  ],
});

type WrapperVariantProps = VariantProps<typeof wrapperClasses>;

interface InputProps {
  modelValue?: string | number;
  invalid?: boolean;
  wrapperClass?: string;
  disabled?: boolean;
  required?: boolean;
  rows?: number | string;
  maxRowsVisible?: number | null;
  resize?: boolean;
  autoFocus?: boolean;
}

interface FieldProps extends InputProps {
  id?: string;
}

const props = withDefaults(defineProps<InputProps>(), {
  rows: 2,
  invalid: false,
  disabled: false,
  required: false,
  maxRowsVisible: null,
  resize: true,
  autoFocus: false,
});

const emit = defineEmits(['update:modelValue']);
const proxy = useVModel(props, 'modelValue', emit);
const field = inject<FieldProps>('field', props);
const textarea = ref<HTMLTextAreaElement | null>(null);

function resize() {
  if (!textarea.value) return;
  const el = textarea.value;
  el.style.height = 'auto';
  const scrollHeight = el.scrollHeight;
  if (props.maxRowsVisible != null) {
    const lineHeight = parseFloat(getComputedStyle(el).lineHeight);
    const maxHeight = lineHeight * props.maxRowsVisible;
    el.style.height = `${Math.min(scrollHeight, maxHeight)}px`;
    el.style.overflowY = scrollHeight > maxHeight ? 'auto' : 'hidden';
  } else {
    el.style.height = `${scrollHeight}px`;
    el.style.overflowY = 'hidden';
  }
}

const focus = () => {
  if (!textarea.value) return;
  textarea.value.focus();
};

onMounted(() => {
  resize();
  if (props.autoFocus) {
    focus();
  }
});

watch(proxy, () => {
  resize();
});

defineExpose({
  focus,
});
</script>
