<template>
  <input
    ref="input"
    v-model="model"
    max="9"
    maxlength="1"
    min="0"
    pattern="[0-9]"
    :class="inputClasses"
    :disabled="disabled"
    :placeholder="placeholder"
    :type="type"
    class="h-12 w-14 appearance-none rounded border-none bg-white px-3 py-2 text-center text-2xl text-primary-500 outline-none ring-primary-400 transition-all hover:ring-1 focus:outline-none focus:ring-2 focus:ring-primary-400 active:ring-2 dark:bg-midnight-300 dark:text-midnight-50 dark:ring-primary-600 dark:focus:ring-primary-600"
    @blur="onBlur"
    @focus="onFocus"
    @input="onChange"
    @keydown="onKeyDown"
    @paste="onPaste"
  />
</template>

<script>
import { defineComponent, onMounted, ref, watch } from 'vue';

export default defineComponent({
  name: 'SingleOtpInput',
  props: {
    type: {
      type: String,
      validator: (value) => ['number', 'tel', 'text', 'password'].includes(value),
      default: 'text',
    },
    value: {
      type: [String, Number],
    },
    focus: {
      type: Boolean,
    },
    autoFocus: {
      type: Boolean,
    },
    inputClasses: {
      type: [String, Array],
    },
    placeholder: {
      type: String,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['change', 'keydown', 'paste', 'focus', 'blur'],
  setup(props, { emit }) {
    const model = ref(props.value || '');
    const input = ref(null);

    const onChange = () => {
      model.value = model.value.toString();

      if (model.value.length > 1) {
        model.value = model.value.slice(0, 1);
      }

      return emit('change', model.value);
    };

    const isLetter = (code) => code >= 65 && code <= 90;
    const isNumeric = (code) => (code >= 48 && code <= 57) || (code >= 96 && code <= 105);

    const onKeyDown = (event) => {
      if (props.disabled) {
        event.preventDefault();
      }

      const code = event.which ? event.which : event.keyCode;

      if (isNumeric(code) || (props.type === 'text' && isLetter(code)) || [8, 9, 13, 37, 39, 46, 86].includes(code)) {
        emit('keydown', event);
      } else {
        event.preventDefault();
      }
    };

    const onPaste = (event) => emit('paste', event);

    const onFocus = () => {
      input.value.select();
      return emit('focus');
    };

    const onBlur = () => emit('blur');

    watch(
      () => props.value,
      (value, old) => {
        if (value !== old) {
          model.value = value;
        }
      }
    );

    watch(
      () => props.focus,
      (value, old) => {
        if (old !== value && input.value && props.focus) {
          input.value.focus();
          input.value.select();
        }
      }
    );

    onMounted(() => {
      if (input.value && props.focus && props.autoFocus) {
        input.value.focus();
        input.value.select();
      }
    });

    return {
      onChange,
      onKeyDown,
      onPaste,
      onFocus,
      onBlur,
      input,
      model,
    };
  },
});
</script>
