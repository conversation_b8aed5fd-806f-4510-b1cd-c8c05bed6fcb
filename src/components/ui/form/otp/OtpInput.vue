<template>
  <div class="flex flex-row justify-center space-x-3 text-center">
    <!-- Turn off autocomplete when input type is password -->
    <input v-if="type === 'password'" autoComplete="off" name="hidden" style="display: none" type="text" />

    <single-otp-input
      v-for="(value, index) in length"
      :key="index"
      :auto-focus="autoFocus"
      :disabled="disabled"
      :focus="activeInput === index"
      :input-classes="inputClasses"
      :placeholder="placeholder[index]"
      :type="type"
      :value="otp[index]"
      @blur="onBlur"
      @change="onChange"
      @focus="onFocus(index)"
      @keydown="onKeyDown"
      @paste="onPaste"
    />
  </div>
</template>

<script>
import { defineComponent, ref } from 'vue';

import SingleOtpInput from './SingleOtpInput.vue';

const BACKSPACE = 8;
const LEFT_ARROW = 37;
const RIGHT_ARROW = 39;
const DELETE = 46;

export default defineComponent({
  name: 'OtpInput',
  components: {
    SingleOtpInput,
  },
  props: {
    length: {
      default: 4,
    },
    inputClasses: {
      type: [String, Array],
    },
    type: {
      type: String,
      validator: (value) => ['number', 'tel', 'text', 'password'].includes(value),
    },
    autoFocus: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: Array,
      default: [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['complete', 'change'],
  setup(props, { emit }) {
    const activeInput = ref(0);
    const otp = ref([]);
    const previousOtp = ref([]);

    const onFocus = (index) => {
      activeInput.value = index;
    };

    const onBlur = () => {
      activeInput.value = -1;
    };

    const determineCompleted = () => {
      if (otp.value.join('').length === props.length) {
        return emit('complete', otp.value.join(''));
      }
    };

    const focusInput = (input) => {
      activeInput.value = Math.max(Math.min(props.length - 1, input), 0);
    };

    const focusNextInput = () => {
      focusInput(activeInput.value + 1);
    };

    const focusPreviousInput = () => {
      focusInput(activeInput.value - 1);
    };

    const updateFocusedInput = (value) => {
      previousOtp.value = Object.assign([], otp.value);
      otp.value[activeInput.value] = value;

      if (previousOtp.value.join('') !== otp.value.join('')) {
        emit('change', otp.value.join(''));
        determineCompleted();
      }
    };

    const onPaste = (event) => {
      event.preventDefault();

      const content = event.clipboardData
        .getData('text/plain')
        .slice(0, props.length - activeInput.value)
        .split('');

      if (props.type === 'number' && !content.join('').match(/^\d+$/)) return;
      if (props.type === 'text' && !content.join('').match(/^\w+$/)) return;

      const combined = otp.value.slice(0, activeInput.value).concat(content);

      combined.slice(0, props.length).forEach(function (value, index) {
        otp.value[index] = value;
      });

      focusInput(combined.slice(0, props.length).length);
      determineCompleted();
    };

    const onChange = (value) => {
      updateFocusedInput(value);
      focusNextInput();
    };

    const clearInput = () => {
      if (otp.value.length > 0) {
        emit('change', '');
      }
      otp.value = [];
      activeInput.value = 0;
    };

    const onKeyDown = (event) => {
      switch (event.keyCode) {
        case BACKSPACE:
          event.preventDefault();
          updateFocusedInput('');
          focusPreviousInput();
          break;
        case DELETE:
          event.preventDefault();
          updateFocusedInput('');
          break;
        case LEFT_ARROW:
          event.preventDefault();
          focusPreviousInput();
          break;
        case RIGHT_ARROW:
          event.preventDefault();
          focusNextInput();
          break;
        default:
          break;
      }
    };

    return {
      activeInput,
      otp,
      previousOtp,
      clearInput,
      onPaste,
      onKeyDown,
      onBlur,
      focusInput,
      focusNextInput,
      focusPreviousInput,
      onChange,
      onFocus,
      updateFocusedInput,
      determineCompleted,
    };
  },
});
</script>
