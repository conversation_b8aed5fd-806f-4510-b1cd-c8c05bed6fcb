<template>
  <div class="w-full space-y-1 select-none">
    <div class="grid w-full focus:outline-none overflow-hidden relative bg-platinum-300 dark:bg-midnight-600 rounded-lg p-2">
      <div class="relative w-full cursor-pointer">
        <div
          class="w-full rounded-lg duration-300 ease-in-out border-primary-500 outline-dashed outline-2 outline-primary-500/50 hover:outline-primary-500/75 transition-all"
          role="presentation"
        >
          <div class="flex items-center justify-center flex-col py-4 w-full">
            <BIconCloudUploadFill class="w-8 h-8 mb-1 text-platinum-950 dark:text-midnight-100" />
            <p class="mb-1 text-sm text-platinum-950 dark:text-midnight-100">
              <span class="font-semibold">Drag and Drop or Click to Upload</span>
            </p>
            <p class="text-xs text-platinum-950 dark:text-midnight-100">
              Accepted formats: {{ acceptedTypes.replaceAll(',', ', ') }}
            </p>
          </div>
        </div>
        <input
          class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
          :multiple="isMultiple"
          :accept="acceptedTypes"
          @change="handleFiles"
          type="file"
          style="border: 0px; clip: rect(0px, 0px, 0px, 0px); clip-path: inset(50%); height: 1px; margin: 0px -1px -1px 0px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
        />
      </div>
    </div>
    <p class="text-xs text-center">
      Select {{ isMultiple ? 'files' : 'a file' }} to upload. Maximum size: {{ props.maxSize || 5 }}MB
    </p>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BIconCloudUploadFill from '~icons/bi/cloud-upload-fill';

const props = defineProps<{
  multiple?: boolean;
  accept?: string;
  maxSize?: number; // in MB
}>();

const acceptedTypes = computed(() => props.accept || 'image/*,.pdf');
const isMultiple = computed(() => props.multiple ?? false);
const maxFileSize = computed(() => (props.maxSize || 5) * 1024 * 1024);

const handleFiles = async (event: Event) => {
  alert('HANDLING FILES');
};

</script>