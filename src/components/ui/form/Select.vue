<template>
  <select
    ref="select"
    :value="modelValue"
    :class="borderClasses"
    class="rounded-md bg-platinum-200 text-primary-500 shadow-sm dark:bg-midnight-400 dark:text-midnight-50"
    @input="$emit('update:modelValue', $event.target.value)"
  >
    <slot></slot>
  </select>
</template>

<script>
export default {
  props: {
    label: {
      type: String,
      required: false,
    },
    modelValue: {
      type: [String, Number],
      required: false,
    },
    type: {
      type: String,
      default: 'text',
    },
    invalid: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:modelValue'],
  computed: {
    borderClasses() {
      return this.invalid
        ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500'
        : 'border-transparent focus:ring-primary-500 focus:border-primary-500';
    },
  },
  methods: {
    focus() {
      this.$refs.select.focus();
    },
  },
};
</script>
