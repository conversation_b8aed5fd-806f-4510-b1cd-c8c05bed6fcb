<template>
  <multiselect
    ref="dropdown"
    :value="modelValue"
    :close-on-select="true"
    :required="required || field.required"
    :classes="{
      container: [
        'relative mx-auto w-full flex items-center justify-end box-border cursor-pointer rounded text-primary-500 dark:text-midnight-50 leading-snug transition bg-platinum-300 dark:bg-midnight-300 text-base',
        field.invalid
          ? 'ring-1 ring-danger-400 dark:ring-danger-500'
          : 'ring-1 ring-platinum-200 dark:ring-transparent transition hover:ring-primary-400 focus-within:ring-2 ring-gray-300 hover:ring-primary-400 dark:hover:ring-primary-600 focus:ring-primary-400 dark:focus:ring-primary-600 active:ring-primary-400 dark:active:ring-primary-600 focus-within:ring-primary-400 dark:focus-within:ring-primary-600',
      ],
      containerDisabled: 'cursor-default bg-gray-100',
      containerOpen: 'rounded-b-none',
      containerOpenTop: 'rounded-t-none',
      containerActive: 'hover:ring-2 ring-primary-600',
      singleLabel:
        'flex items-center h-full max-w-full absolute left-0 top-0 pointer-events-none bg-transparent leading-snug pl-3.5 pr-16 box-border rtl:left-auto rtl:right-0 rtl:pl-0 rtl:pr-3.5',
      singleLabelText: 'overflow-ellipsis overflow-hidden block whitespace-nowrap max-w-full',
      multipleLabel:
        'flex items-center h-full absolute left-0 top-0 pointer-events-none bg-transparent leading-snug pl-3.5 rtl:left-auto rtl:right-0 rtl:pl-0 rtl:pr-3.5',
      search:
        'w-full absolute inset-0 outline-none focus:ring-0 appearance-none box-border border-0 text-primary-500 dark:text-midnight-50 bg-platinum-300 dark:bg-midnight-300 rounded pl-3.5 rtl:pl-0 rtl:pr-3.5 text-base',
      tags: 'flex-grow flex-shrink flex flex-wrap items-center mt-1 pl-2 rtl:pl-0 rtl:pr-2',
      tag: tagClasses
        ? tagClasses
        : 'bg-primary-500 text-white text-sm font-semibold py-0.5 pl-2 rounded-md mr-1 mb-1 flex items-center whitespace-nowrap rtl:pl-0 rtl:pr-2 rtl:mr-0 rtl:ml-1',
      tagDisabled: 'pr-2 opacity-50 rtl:pl-2',
      tagRemove: 'flex items-center justify-center p-1 mx-0.5 rounded-sm hover:bg-black hover:bg-opacity-10 group',
      tagRemoveIcon:
        'bg-multiselect-remove bg-center bg-no-repeat opacity-30 inline-block w-3 h-3 group-hover:opacity-60',
      tagsSearchWrapper: 'inline-block relative mx-1 mb-1 flex-grow flex-shrink h-full',
      tagsSearch:
        'absolute inset-0 border-0 outline-none focus:ring-0 appearance-none p-0 text-base font-sans box-border w-full bg-transparent',
      tagsSearchCopy: 'invisible whitespace-pre-wrap inline-block h-px',
      placeholder:
        'flex items-center h-full absolute left-0 top-0 pointer-events-none bg-transparent leading-snug pl-3.5 text-platinum-500 dark:text-midnight-200 rtl:left-auto rtl:right-0 rtl:pl-0 rtl:pr-3.5',
      caret:
        'bg-multiselect-caret bg-center bg-no-repeat w-2.5 h-4 py-px box-content mr-3.5 relative z-10 flex-shrink-0 flex-grow-0 transition-transform transform pointer-events-none rtl:mr-0 rtl:ml-3.5',
      caretOpen: 'rotate-180 pointer-events-auto',
      clear:
        'pr-3.5 relative z-10 opacity-40 transition duration-300 flex-shrink-0 flex-grow-0 flex hover:opacity-80 rtl:pr-0 rtl:pl-3.5',
      clearIcon: 'bg-multiselect-remove bg-center bg-no-repeat w-2.5 h-4 py-px box-content inline-block',
      spinner:
        'bg-multiselect-spinner bg-center bg-no-repeat w-4 h-4 z-10 mr-3.5 animate-spin flex-shrink-0 flex-grow-0 rtl:mr-0 rtl:ml-3.5',
      inifite: 'flex items-center justify-center w-full',
      inifiteSpinner:
        'bg-multiselect-spinner bg-center bg-no-repeat w-4 h-4 z-10 animate-spin flex-shrink-0 flex-grow-0 m-3.5',
      dropdown:
        'dropdown max-h-60 absolute -left-px -right-px bottom-0 transform translate-y-full border border-platinum-300 dark:border-midnight-300 -mt-px overflow-y-auto z-50 bg-white dark:bg-midnight-500 flex flex-col rounded-b scrollbar-thin',
      dropdownTop: '-translate-y-full top-px bottom-auto rounded-b-none rounded-t',
      dropdownHidden: 'hidden',
      options: 'flex flex-col p-0 m-0 list-none',
      optionsTop: '',
      group: 'p-0 m-0',
      groupLabel:
        'flex text-sm box-border items-center justify-start text-left py-1 px-3 font-semibold bg-gray-200 cursor-default leading-normal',
      groupLabelPointable: 'cursor-pointer',
      groupLabelPointed: 'bg-gray-300 text-gray-700 pointedOption',
      groupLabelSelected: 'bg-primary-600 text-white',
      groupLabelDisabled: 'bg-gray-100 text-gray-300 cursor-not-allowed',
      groupLabelSelectedPointed: 'bg-primary-600 text-white opacity-90',
      groupLabelSelectedDisabled: 'text-primary-100 bg-primary-600 bg-opacity-50 cursor-not-allowed',
      groupOptions: 'p-0 m-0',
      option:
        'flex items-center justify-start box-border text-left cursor-pointer text-base leading-snug py-2 px-3 group text-primary-500',
      optionPointed:
        'bg-primary-400 !text-platinum-200 dark:bg-primary-600 dark:text-midnight-400 pointed pointedOption',
      optionSelected: '!text-platinum-200 dark:text-midnight-500 bg-primary-500 selectedOption',
      optionDisabled: 'text-gray-300 cursor-not-allowed',
      optionSelectedPointed: '!text-platinum-200 dark:text-midnight-400 bg-primary-600 selectedOption',
      optionSelectedDisabled: 'text-primary-100 bg-platinum-700 bg-opacity-50 selectedOption cursor-not-allowed',
      noOptions: 'py-2 px-3 text-gray-600 bg-platinum-200 dark:bg-primary-500 dark:text-platinum-50 text-left',
      noResults: 'py-2 px-3 text-gray-600 bg-platinum-200 dark:bg-primary-500 dark:text-platinum-50 text-left',
      fakeInput:
        'bg-transparent absolute left-0 right-0 -bottom-px w-full h-px border-0 p-0 appearance-none outline-none text-transparent !ring-0',
      spacer: 'h-9 py-px box-content',
    }"
    :clear-on-select="false"
    @input="(value) => $emit('update:modelValue', value)"
  >
    <template v-for="(_, slot) in $slots" #[slot]="scope">
      <slot :name="slot" v-bind="scope || {}" />
    </template>
  </multiselect>
</template>

<script setup>
import Multiselect from '@vueform/multiselect';
import { inject, onBeforeUnmount, onMounted, ref } from 'vue';

const props = defineProps(['modelValue', 'checkInfiniteScroll', 'required', 'tagClasses']);
const emits = defineEmits(['update:modelValue', 'infinite-scroll']);

const dropdown = ref();

const field = inject('field', props);

const determineIfEndOfScroll = (list) => {
  if (list.clientHeight !== list.scrollHeight && list.scrollTop + list.clientHeight >= list.scrollHeight) {
    emits('infinite-scroll');
  }
};

onMounted(() => {
  if (props.checkInfiniteScroll) {
    const list = dropdown.value.$el.querySelector('.dropdown');
    list.addEventListener('scroll', () => determineIfEndOfScroll(list));
  }
});

onBeforeUnmount(() => {
  if (props.checkInfiniteScroll) {
    const list = dropdown.value.$el.querySelector('.dropdown');
    list.removeEventListener('scroll', () => determineIfEndOfScroll(list));
  }
});
</script>
