<template>
  <VResourceCombobox
    v-model="proxy"
    placeholder="Select Honorific"
    resource="honorific"
    label="name"
    :model-value-resolver="(value) => value ? axios.get(`/api/honorifics/${value}`).then((response) => response.data) : Promise.resolve(null)"
    :options-resolver="
      (params, { signal }) =>
        axios
          .get(`/api/honorifics`, {
            params,
            signal,
          })
          .then((response) => response.data)
    "
  />
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';

const props = defineProps({
  modelValue: {
    type: String,
    default: null,
  },
});

const emit = defineEmits(['update:modelValue']);

const proxy = useVModel(props, 'modelValue', emit);
</script>
