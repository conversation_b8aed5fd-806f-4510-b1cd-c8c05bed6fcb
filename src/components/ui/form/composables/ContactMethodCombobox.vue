<template>
  <VResourceCombobox
    v-model="proxy"
    placeholder="Select Contact Method"
    resource="contact-method"
    label="name"
    :model-value-resolver="(value) => axios.get(`/api/contacts/methods/${value}`).then((response) => response.data)"
    :options-resolver="
      (params, { signal }) =>
        axios
          .get(`/api/contacts/methods`, {
            params: {
              ...params,
              filter: {
                is_social: social,
                contactable_type: contactable,
              },
            },
            signal,
          })
          .then((response) => response.data)
    "
    @raw-value="(value) => emit('update:rawValue', value)"
  />
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  contactable: {
    type: String,
    required: true,
  },
  social: {
    type: Boolean,
    default: false,
    required: false,
  },
});

const emit = defineEmits(['update:modelValue', 'update:rawValue']);

const proxy = useVModel(props, 'modelValue', emit);
</script>
