<template>
  <VResourceCombobox
    v-model="proxy"
    placeholder="Select Type"
    resource="type"
    label="name"
    :model-value-resolver="resolveModelValue"
    :options-resolver="
      (params, { signal }) =>
        axios
          .get(`/api/types`, {
            params: {
              ...params,
              typeable,
            },
            signal,
          })
          .then((response) => response.data)
    "
  />
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  typeable: {
    type: String,
    required: true,
  },
  defaultOption: {
    type: [String, Boolean],
    default: false,
  },
});

const emit = defineEmits(['update:modelValue', 'resolved']);

const resolveModelValue = async (value) => {
  if (value) {
    try {
      const response = await axios.get(`/api/types/${value}`);
      return response.data;
    } catch (error) {
      console.error('Failed to resolve model value:', error);
      return null;
    }
  } else if (props.defaultOption) {
    try {
      const response = await axios.get(`/api/types`, {
        params: {
          typeable: props.typeable,
          filter: {
            name: props.defaultOption,
          },
        },
      });
      return response.data[0] || null;
    } catch (error) {
      console.error('Failed to resolve model value:', error);
      return null;
    }
  }
  return null;
}

const proxy = useVModel(props, 'modelValue', emit);
</script>
