<template>
  <VResourceCombobox
    v-model="proxy"
    placeholder="Select Industry"
    resource="industry"
    label="name"
    :model-value-resolver="resolveModelValue"
    :options-resolver="
      (params, { signal }) =>
        axios
          .get(`/api/companies/industries`, {
            params: {
              ...params,
              sort: 'name',
            },
            signal,
          })
          .then((response) => response.data)
    "
  />
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  defaultOption: {
    type: [String, Boolean],
    default: false,
  },
});

const emit = defineEmits(['update:modelValue', 'resolved']);

const resolveModelValue = async (value) => {
  if (value) {
    try {
      const response = await axios.get(`/api/companies/industries/${value}`);
      return response.data;
    } catch (error) {
      console.error('Failed to resolve model value:', error);
      return null;
    }
  } else if (props.defaultOption) {
    try {
      const response = await axios.get(`/api/companies/industries`, {
        params: {
          filters: {
            name: props.defaultOption,
          },
        },
      });
      return response.data[0] || null;
    } catch (error) {
      console.error('Failed to resolve model value:', error);
      return null;
    }
  }
  return null;
}

const proxy = useVModel(props, 'modelValue', emit);
</script>
