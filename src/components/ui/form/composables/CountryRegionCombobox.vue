<template>
  <VResourceCombobox
    v-model="proxy"
    placeholder="Select Region"
    resource="region"
    label="name"
    :model-value-resolver="(value) => axios.get(`/api/countries/regions/${value}`).then((response) => response.data)"
    :options-resolver="
      (params, { signal }) =>
        axios
          .get(`/api/countries/regions`, {
            params,
            signal,
          })
          .then((response) => response.data)
    "
  />
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue']);

const proxy = useVModel(props, 'modelValue', emit);
</script>
