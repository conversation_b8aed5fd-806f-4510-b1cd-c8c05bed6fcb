<template>
  <VResourceCombobox
    v-model="proxy"
    placeholder="Select Timezone"
    resource="timezone"
    label="name"
    :model-value-resolver="resolveModelValue"
    :options-resolver="
      (params, { signal }) =>
        axios
          .get(`/api/timezones`, {
            params: {
              ...params,
            },
            signal,
          })
          .then((response) => response.data)
    "
  >
    <template #option="{ option, isSelected }">
      <div class="flex w-full justify-between">
        {{ option.name }}
        <span> (UTC {{ getOffset(option.value) }}) </span>
      </div>
    </template>
  </VResourceCombobox>
</template>

<script setup>
import dayjs from '@/lib/dayjs';
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';

const props = defineProps({
  modelValue: {
    type: String,
    default: null,
  },
  defaultOption: {
    type: [String, Boolean],
    default: null,
  },
});

const emit = defineEmits(['update:modelValue']);

const proxy = useVModel(props, 'modelValue', emit, props.defaultValue);

const getOffset = (timezone) => {
  const offset = dayjs.tz(dayjs.utc().format(), timezone).utcOffset();
  const formatted = dayjs.duration(offset / 60, 'h').format('HH:mm');

  if (offset >= 0) {
    return '+' + formatted;
  }

  return formatted;
};

const resolveModelValue = async (value) => {
  if (value) {
    try {
      const response = await axios.get(`/api/timezones/${value}`); // (value) => value ? axios.get(`/api/timezones/${value}`).then((response) => response.data) : Promise.resolve(null)
      return response.data;
    } catch (error) {
      console.error('Failed to resolve model value:', error);
      return null;
    }
  } else if (props.defaultOption) {
    try {
      const response = await axios.get(`/api/timezones`, {
        params: {
          filter: {
            value: props.defaultOption,
          },
        },
      });
      return response.data[0] || null;
    } catch (error) {
      console.error('Failed to resolve model value:', error);
      return null;
    }
  }
  return null;
}

</script>
