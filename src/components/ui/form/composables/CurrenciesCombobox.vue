<template>
  <VResourceCombobox
    v-model="proxy"
    mode="tags"
    placeholder="Select Currencies"
    resource="currencies"
    label="iso"
    :object="true"
    :model-value-resolver="
      (value) =>
        value && value.length
          ? axios
              .get('/api/currencies', {
                params: {
                  filter: {
                    locked_id: value,
                  },
                },
              })
              .then((response) => response.data)
          : Promise.resolve([])
    "
    :options-resolver="
      (params, { signal }) =>
        axios
          .get(`/api/currencies`, {
            params: {
              ...params,
            },
            signal,
          })
          .then((response) => response.data)
    "
  />
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['update:modelValue']);

const proxy = useVModel(props, 'modelValue', emit);
</script>
