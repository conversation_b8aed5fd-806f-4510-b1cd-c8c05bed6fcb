<template>
  <VResourceCombobox
    v-model="proxy"
    mode="tags"
    placeholder="Select Permissions"
    resource="permissions"
    label="name"
    :object="true"
    :model-value-resolver="
      (value) =>
        value.length
          ? axios
              .get('/api/abilities', {
                params: {
                  filter: {
                    locked_id: value,
                  },
                },
              })
              .then((response) => response.data)
          : []
    "
    :options-query-key="exclude"
    :options-resolver="
      (params, { signal }) =>
        axios
          .get(`/api/abilities`, {
            params: {
              ...params,
            },
            signal,
          })
          .then((response) => response.data)
    "
  />
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  exclude: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['update:modelValue']);

const proxy = useVModel(props, 'modelValue', emit);
</script>
