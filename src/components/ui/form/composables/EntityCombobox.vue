<template>
  <VResourceCombobox
    v-model="proxy"
    placeholder="Select Entity"
    resource="entity"
    label="name"
    @raw-value="(value) => emit('resolved', value)"
    :model-value-resolver="(value) => axios.get(`/api/entities/${value}`).then((response) => response.data)"
    value-prop="name"
    :options-resolver="
      (params, { signal }) =>
        axios
          .get(`/api/entities`, {
            params,
            signal,
          })
          .then((response) => response.data.filter(entity => !props.excludedNames.includes(entity.name)))
    "
  />
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  excludedNames: {
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['update:modelValue', 'resolved']);

const proxy = useVModel(props, 'modelValue', emit);
</script>
