<template>
  <VResourceCombobox
    v-model="proxy"
    mode="tags"
    placeholder="Select Roles"
    resource="roles"
    label="name"
    :object="true"
    :model-value-resolver="
      (value) =>
        value && value.length
          ? axios
              .get('/api/roles', {
                params: {
                  filter: {
                    locked_id: value,
                  },
                },
              })
              .then((response) => response.data)
          : Promise.resolve([])
    "
    :options-resolver="
      (params, { signal }) =>
        axios
          .get(`/api/roles`, {
            params: {
              ...params,
              filter: {
                roleable,
              },
            },
            signal,
          })
          .then((response) => response.data)
    "
  >
    <template #tag="{ option, handleTagRemove, disabled }">
      <div class="flex justify-center">
        <RoleBadge mode="dark" :color="option.color" class="mb-1 mr-1 pr-0.5">
          <span>{{ option.name }}</span>
          <span
            class="multiselect-tag-remove ml-1 rounded-full bg-transparent p-0.5 hover:bg-white hover:bg-opacity-25"
            @mousedown.prevent="handleTagRemove(option, $event)"
          >
            <span class="multiselect-tag-remove-icon"></span>
          </span>
        </RoleBadge>
      </div>
    </template>
    <template #option="{ option, isSelected, isPointed }">
      <div class="flex items-center">
        <RoleBadge mode="dark" :color="option.color">{{ option.name }}</RoleBadge>
      </div>
    </template>
  </VResourceCombobox>
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';
import RoleBadge from '../../badges/RoleBadge.vue';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  roleable: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['update:modelValue']);

const proxy = useVModel(props, 'modelValue', emit);
</script>
