<template>
  <VResourceCombobox
    v-model="proxy"
    placeholder="Select Customer"
    resource="customer"
    label="name"
    check-infinite-scroll
    :model-value-resolver="
      (value) =>
        value.length
          ? axios
              .get('/api/customers', {
                params: {
                  filter: {
                    locked_id: value,
                  },
                },
              })
              .then((response) => props.multiple ? response.data : response.data[0])
          : []
    "
    :options-resolver="
      (params, { signal, pageParam = 0 }) =>
        axios
          .get(`/api/customers`, {
            params: {
              ...params,
              page: pageParam,
              sort: 'name',
              statusable,
            },
            signal,
          })
          .then((response) => response)
    "
    :mode="props.multiple ? 'multiple' : 'single'"
  >
    <template #singlelabel="{ value }">
      <div class="multiselect-single-label flex items-center w-full">
        <div class="truncate space-x-1.5 flex items-center w-full">
          <BIconPersonFill v-if="value?.informationable_type === 'CustomerPersonInformation'" class="text-primary-500" />
          <BIconBuildingFill v-else-if="value?.informationable_type === 'CustomerCompanyInformation'" class="text-primary-500" />
          <span>
            <template v-if="value?.information?.name || value?.information?.full_name">
              {{ `${value?.information?.name || value?.information?.full_name}` }}
            </template>
            <template v-else>
              <span class="italic text-platinum-400 dark:text-midnight-50/50">Customer Information Unavailable</span>
            </template>
          </span>
        </div>
      </div>
    </template>

    <template #multiplelabel="{ values }">
      <div class="multiselect-multiple-label flex items-center gap-1 w-full">
        <template v-if="values.length === 1">
          <div class="multiselect-single-label flex items-center">
            
            <VAvatar
              size="xs"
              :name="values[0]?.informationable_type === 'CustomerPersonInformation' ? values[0]?.information?.full_name : values[0]?.information?.name"
              :src="null"
            />

            <div class="ml-2 truncate">
              {{ values[0]?.informationable_type === 'CustomerPersonInformation' ? values[0]?.information?.full_name : values[0]?.information?.name }}
            </div>
          </div>
        </template>

        <template v-else>
          <span>{{ values.length }} customers selected</span>
        </template>
      </div>
    </template>

    <template #option="{ option }">
      <div class="flex items-center w-full">
        <span class="truncate space-x-1.5 flex items-center w-full">
          <BIconPersonFill v-if="option?.informationable_type === 'CustomerPersonInformation'" class="text-platinum-400 dark:text-midnight-50" />
          <BIconBuildingFill v-else-if="option?.informationable_type === 'CustomerCompanyInformation'" class="text-platinum-400 dark:text-midnight-50" />
          <span>
            <template v-if="option?.information?.name || option?.information?.full_name">
              {{ `${option?.information?.name || option?.information?.full_name}` }}
            </template>
            <template v-else>
              <span class="italic text-platinum-400 dark:text-midnight-50/50">Customer Information Unavailable</span>
            </template>
          </span>
        </span>
      </div>
    </template>

  </VResourceCombobox>
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';
import BIconPersonFill from '~icons/bi/person-fill';
import BIconBuildingFill from '~icons/bi/building-fill';

const props = defineProps({
  modelValue: {
    type: [String, Array],
    default: (props) => props.multiple ? [] : null
  },
  multiple: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:modelValue']);

const proxy = useVModel(props, 'modelValue', emit);
</script>