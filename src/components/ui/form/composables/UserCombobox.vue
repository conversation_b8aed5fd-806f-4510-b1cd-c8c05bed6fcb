<template>
  <VResourceCombobox
    v-model="proxy"
    placeholder="Select User"
    resource="user"
    label="email"
    check-infinite-scroll
    :model-value-resolver="
      (value) =>
        value.length
          ? axios
              .get('/api/users', {
                params: {
                  filter: {
                    locked_id: value,
                  },
                },
              })
              .then((response) => props.multiple ? response.data : response.data[0])
          : []
    "
    :options-resolver="
      (params, { signal, pageParam = 0 }) =>
        axios
          .get(`/api/users`, {
            params: {
              ...params,
              page: pageParam,
              sort: 'profile_full_name',
              statusable,
            },
            signal,
          })
          .then((response) => response)
    "
    :mode="props.multiple ? 'multiple' : 'single'"
  >
    <template #singlelabel="{ value }">
      <div class="multiselect-single-label flex items-center">
        <VAvatar
          size="xs"
          :dot="value?.availability?.color"
          :name="`${value?.profile?.full_name}`"
          :src="value?.profile?.media?.profile_photo?.conversions?.small"
        />

        <div class="ml-2 truncate">
          <span>
            <template v-if="value?.profile?.full_name">
              {{ `${value?.profile?.full_name}` }}
            </template>
            <template v-else>
              <span class="italic text-platinum-800 dark:text-midnight-100">Unavailable Profile</span>
            </template>
          </span>
          <span class="ml-1 text-sm text-platinum-950/50 dark:text-midnight-50/50">({{ value.email }})</span>
        </div>
      </div>
    </template>

    <template #multiplelabel="{ values }">
      <div class="multiselect-multiple-label flex items-center gap-1 w-full">
        <template v-if="values.length === 1">
          <div class="multiselect-single-label flex items-center">
            <VAvatar
              size="xs"
              :dot="values[0]?.availability?.color"
              :name="values[0]?.profile?.full_name"
              :src="values[0]?.profile?.media?.profile_photo?.conversions?.small"
            />

            <div class="ml-2 truncate">
              <span>
                <template v-if="values[0]?.profile?.full_name">
                  {{ values[0]?.profile?.full_name }}
                </template>
                <template v-else>
                  <span class="italic text-platinum-800 dark:text-midnight-100">Unavailable Profile</span>
                </template>
              </span>
              <span class="ml-1 text-sm text-platinum-950/50 dark:text-midnight-50/50">({{ values[0].email }})</span>
            </div>
          </div>
        </template>

        <template v-else>
          <span>{{ values.length }} users selected</span>
        </template>
      </div>
    </template>

    <template #option="{ option }">
      <div class="flex items-center">
        <VAvatar
          size="xs"
          :name="`${option?.profile?.full_name}`"
          :src="option?.profile?.media?.profile_photo?.conversions?.small"
        />
        <span class="ml-2 truncate">
          <span>
            <template v-if="option?.profile?.full_name">
              {{ `${option?.profile?.full_name}` }}
            </template>
            <template v-else>
              <span class="italic text-gray-700">Unavailable Profile</span>
            </template>
          </span>
          <span class="text-sm text-platinum-400 dark:text-midnight-50/50"> ({{ option.email }}) </span>
        </span>
      </div>
    </template>
  </VResourceCombobox>
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';

const props = defineProps({
  multiple: {
    type: Boolean,
    default: false
  },
  statusable: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: [String, Array],
    default: (props) => props.multiple ? [] : null
  }
});

const emit = defineEmits(['update:modelValue']);

const proxy = useVModel(props, 'modelValue', emit);
</script>
