<template>
  <VResourceCombobox
    v-model="proxy"
    placeholder="Select Country"
    resource="country"
    label="name"
    check-infinite-scroll
    :model-value-resolver="(value) => {
      if (value.trim() !== '') {
        return axios.get(`/api/countries/${value}`).then((response) => response.data);
      }
      return Promise.resolve(null);
    }"
    :options-resolver="
      (params, { signal, pageParam = 0 }) =>
        axios
          .get(`/api/countries`, {
            params: {
              ...params,
              page: pageParam,
              sort: 'name',
              statusable,
            },
            signal,
          })
          .then((response) => response)
    "
  >
    <template #singlelabel="{ value }">
      <div class="multiselect-single-label flex items-center space-x-1.5">
        <img
          v-if="value.iso_alpha_2"
          class="h-5 w-5"
          :src="`https://flagicons.lipis.dev/flags/4x3/${value.iso_alpha_2.toLowerCase()}.svg`"
        />
        <span class="truncate">
          {{ value.name }}
        </span>
      </div>
    </template>

    <template #option="{ option }">
      <div class="flex items-center space-x-1.5">
        <img
          v-if="option.iso_alpha_2"
          class="h-5 w-5"
          :src="`https://flagicons.lipis.dev/flags/4x3/${option.iso_alpha_2.toLowerCase()}.svg`"
        />
        <span class="truncate">
          {{ option.name }}
        </span>
      </div>
    </template>
  </VResourceCombobox>
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';

const props = defineProps({
  modelValue: {
    type: String,
    default: null,
  },
});

const emit = defineEmits(['update:modelValue']);

const proxy = useVModel(props, 'modelValue', emit);
</script>
