<template>
  <VResourceCombobox
    ref="comboboxRef"
    v-model="proxy"
    placeholder="Select Status"
    resource="status"
    label="name"
    :model-value-resolver="resolveModelValue"
    :options-resolver="
      (params, { signal }) =>
        axios
          .get(`/api/statuses`, {
            params: {
              ...params,
              statusable,
            },
            signal,
          })
          .then((response) => response.data)
    "
  />
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';
import { ref, watch, nextTick } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  statusable: {
    type: String,
    required: true,
  },
  defaultOption: {
    type: [String, Boolean],
    default: false,
  },
});

const emit = defineEmits(['update:modelValue']);

const resolveModelValue = async (value) => {
  if (value) {
    try {
      const response = await axios.get(`/api/statuses/${value}`);
      return response.data;
    } catch (error) {
      console.error('Failed to resolve model value:', error);
      return null;
    }
  } else if (props.defaultOption) {
    try {
      const response = await axios.get(`/api/statuses`, {
        params: {
          statusable: props.statusable,
          filters: {
            name: props.defaultOption,
          },
        },
      });
      return response.data[0] || null;
    } catch (error) {
      console.error('Failed to resolve model value:', error);
      return null;
    }
  }
  return null;
}

const comboboxRef = ref(null);

watch(() => props.statusable, async (newValue, oldValue) => {
  // If the statusable prop has changed, refetch the combobox options..
  if (comboboxRef.value && newValue !== oldValue) {
    proxy.value = null;
    emit('update:modelValue', null);
    await nextTick();
    comboboxRef.value.refetch();
  }
});

const proxy = useVModel(props, 'modelValue', emit);

</script>
