<template>
  <VResourceCombobox
    v-model="proxy"
    placeholder="Select Structure Type"
    resource="company-structure-type"
    label="name"
    :model-value-resolver="(value) => {
      if (value.trim() !== '') {
        return axios.get(`/api/companies/structure-types/${value}`).then((response) => response.data);
      }
      return Promise.resolve(null);
    }"
    :options-resolver="
      (params, { signal }) =>
        axios
          .get(`/api/companies/structure-types`, {
            params,
            signal,
          })
          .then((response) => response.data)
    "
  />
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue']);

const proxy = useVModel(props, 'modelValue', emit);
</script>
