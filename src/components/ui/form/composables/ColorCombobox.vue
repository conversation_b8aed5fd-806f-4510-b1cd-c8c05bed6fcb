<template>
  <VCombobox
    v-model="value"
    spinner
    autocomplete="off"
    :options="colors"
    :object="false"
    value-prop="value"
    searchable
    track-by="name"
    :placeholder="placeholder"
  >
    <template #singlelabel="{ value }">
      <div class="multiselect-single-label flex items-center">
        <span class="h-4 w-4 rounded-full" v-bind:style="{ backgroundColor: value.value }" />
        <div class="ml-2 truncate">
          <span class="">
            <template v-if="value && value.name">
              {{ value.name }}
            </template>
            <template v-else>
              <span class="italic">No Colour Name</span>
            </template>
          </span>
        </div>
      </div>
    </template>
    <template #option="{ option, isSelected, isPointed }">
      <div class="flex items-center">
        <span class="h-4 w-4 rounded-full" v-bind:style="{ backgroundColor: option.value }" />
        <span class="ml-2 truncate">
            <template v-if="option && option.name">
              {{ option.name }}
            </template>
            <template v-else>
              <span class="italic">No Colour Name</span>
            </template>
        </span>
      </div>
    </template>
  </VCombobox>
</template>

<script setup>
import { useVModel } from '@/hooks/model';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: 'Select Colour',
  },
});

const colors = [
  { value: '#B91C1C', name: 'Ruby Red' },
  { value: '#E11D48', name: 'Rose Red' },
  { value: '#F87171', name: 'Coral' },
  { value: '#EA580C', name: 'Orange' },
  { value: '#FBBF24', name: 'Gold' },
  { value: '#FDE047', name: 'Yellow' },
  { value: '#A3E635', name: 'Lime' },
  { value: '#16A34A', name: 'Green' },
  { value: '#059669', name: 'Emerald' },
  { value: '#14B8A6', name: 'Teal' },
  { value: '#0E7490', name: 'Dark Cyan' },
  { value: '#22D3EE', name: 'Sky Blue' },
  { value: '#1E40AF', name: 'Navy' },
  { value: '#60A5FA', name: 'Pastel Blue' },
  { value: '#4F46E5', name: 'Indigo' },
  { value: '#7C3AED', name: 'Violet' },
  { value: '#9333EA', name: 'Purple' },
  { value: '#C026D3', name: 'Fuchsia' },
  { value: '#DB2777', name: 'Hot Pink' },
  { value: '#525252', name: 'Dark Gray' },
  { value: '#A3A3A3', name: 'Light Gray' },
];

const emit = defineEmits(['update:modelValue']);

const value = useVModel(props, 'modelValue', emit);
</script>
