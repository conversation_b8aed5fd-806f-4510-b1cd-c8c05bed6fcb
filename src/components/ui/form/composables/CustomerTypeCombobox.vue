<template>
  <VCombobox
    v-model="value"
    autocomplete="off"
    :options="customerTypes"
    :placeholder="placeholder"
  />
</template>

<script setup>
import { useVModel } from '@/hooks/model';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: 'Select Customer Type',
  },
});

const customerTypes = [
  {
    label: 'Person',
    value: 'CustomerPersonInformation',
  },
  {
    label: 'Company',
    value: 'CustomerCompanyInformation',
  },
];
const emit = defineEmits(['update:modelValue']);

const value = useVModel(props, 'modelValue', emit);
</script>
