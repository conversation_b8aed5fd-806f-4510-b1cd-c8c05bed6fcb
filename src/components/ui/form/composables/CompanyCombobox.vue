<template>
  <VResourceCombobox
    v-model="proxy"
    placeholder="Select Company"
    resource="company"
    label="name"
    check-infinite-scroll
    :model-value-resolver="
      (value) =>
        value.length
          ? axios
              .get('/api/companies', {
                params: {
                  filter: {
                    locked_id: value,
                  },
                },
              })
              .then((response) => props.multiple ? response.data : response.data[0])
          : []
    "
    :options-resolver="
      (params, { signal, pageParam = 0 }) =>
        axios
          .get(`/api/companies`, {
            params: {
              ...params,
              page: pageParam,
              sort: 'name',
              statusable,
            },
            signal,
          })
          .then((response) => response)
    "
    :mode="props.multiple ? 'multiple' : 'single'"
  >
    <template #multiplelabel="{ values }">
      <div class="multiselect-multiple-label flex items-center gap-1 w-full">
        <template v-if="values.length === 1">
          <div class="multiselect-single-label flex items-center">
            <VAvatar
              size="xs"
              :name="values[0]?.name"
              :src="values[0]?.information?.logo?.conversions?.small"
            />

            <div class="ml-2 truncate">
              {{ values[0]?.name }}
            </div>
          </div>
        </template>

        <template v-else>
          <span>{{ values.length }} companies selected</span>
        </template>
      </div>
    </template>
  </VResourceCombobox>
</template>

<script setup>
import { useVModel } from '@/hooks/model';
import { axios } from '@/lib/axios';
import { ref } from 'vue';
const props = defineProps({
  multiple: {
    type: Boolean,
    default: false
  },
  statusable: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: [String, Array],
    default: (props) => props.multiple ? [] : null
  }
});

const initialValue = ref(props.modelValue ?? null);

const emit = defineEmits(['update:modelValue']);

const proxy = useVModel(props, 'modelValue', emit);

</script>
