<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { cn } from '@/utils';
import { Calendar } from '@/components/ui/shadcn/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/shadcn/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/shadcn/select';
import {
  DateFormatter,
  type DateValue,
  getLocalTimeZone,
  today,
  parseDate,
  parseDateTime,
  parseTime,
  type Time,
} from '@internationalized/date';
import BIconCalendarEvent from '~icons/bi/calendar-event';
import {
  TimeFieldInput,
  TimeFieldRoot,
} from 'reka-ui'
import { result } from 'lodash';

interface QuickSelectItem {
  value: number;
  label: string;
}

interface InputProps {
  modelValue?: string | null;
  invalid?: boolean;
  disabled?: boolean;
  required?: boolean;
  placeholder?: string;
}

const props = withDefaults(defineProps<InputProps>(), {
  modelValue: null,
  invalid: false,
  disabled: false,
  required: false,
  placeholder: 'Select a date & time',
});

const emit = defineEmits<{
  'update:modelValue': [value: string | null]
}>();

const selectedDateTime = ref<DateValue | undefined>();
const selectedDate = ref<DateValue | undefined>();
const selectedTime = ref<Time | undefined>();
const popoverOpen = ref(false);
const quickSelectOpen = ref(false);

const df = new DateFormatter('en-GB', { dateStyle: 'long', timeStyle: 'short' });

const items: QuickSelectItem[] = [
  { value: 0, label: 'Today' },
  { value: 1, label: 'Tomorrow' },
  { value: 2, label: 'In 2 days' },
  { value: 5, label: 'In 5 days' },
  { value: 7, label: 'In a week' },
];

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    newValue = newValue.replace(' ', 'T');
    try {
      selectedDateTime.value = parseDateTime(newValue);
      const split = selectedDateTime.value.toString().split('T');
      selectedDate.value = parseDate(split[0]);
      selectedTime.value = parseTime(split[1].toString().split('.')[0]);
      return parseDateTime(`${selectedDate.value.toString()}T${selectedTime.value.toString()}`);
    } catch (e) {
      console.warn('Invalid date format provided:', newValue);
      selectedDateTime.value = undefined;
      selectedDate.value = undefined;
      selectedTime.value = undefined;
    }
  } else {
    selectedDateTime.value = undefined;
    selectedDate.value = undefined;
    selectedTime.value = undefined;
  }
}, { immediate: true });

// Get the complete datetime string from the provided date and time values
const getFullDateTime = (date, time) => {
  return parseDateTime(`${date.toString()}T${time.toString()}`);
}

// Watch for internal changes to selectedDate
watch(selectedDate, (newValue) => {
  if (newValue) {
    emit('update:modelValue', getFullDateTime(selectedDate.value, selectedTime.value).toString().replace('T', ' '));
  } else {
    emit('update:modelValue', null);
  }
});

// Watch for internal changes to selectedDate
watch(selectedTime, (newValue) => {
  if (newValue) {
    emit('update:modelValue', getFullDateTime(selectedDate.value, selectedTime.value).toString().replace('T', ' '));
  } else {
    emit('update:modelValue', null);
  }
});

const handleQuickSelect = (days: number): void => {
  selectedDate.value = today(getLocalTimeZone()).add({ days });
};

</script>

<template>
  <Popover @update:open="popoverOpen = $event">
    <PopoverTrigger as-child>
      <VButton
        intent="input"
        :disabled="props.disabled"
        :class="cn(
          'w-full justify-start text-left font-normal',
          !selectedDate && 'text-muted-foreground',
          popoverOpen && 'ring-2 ring-primary-500',
          props.invalid && 'border-error-500 focus:border-error-500',
          props.disabled && 'cursor-not-allowed opacity-50'
        )"
      >
        <BIconCalendarEvent class="mr-1 h-4 w-4 -mt-1 inline" />
        {{ selectedDateTime ? df.format(selectedDateTime.toDate(getLocalTimeZone())) : props.placeholder }}
      </VButton>
    </PopoverTrigger>
    <PopoverContent class="flex w-auto flex-col gap-y-0 p-2">
      <Select
        @update:open="quickSelectOpen = $event"
        @update:model-value="(v) => {
          if (!v) return;
          handleQuickSelect(Number(v));
        }"
      >
        <div class="p-3 pb-0">
          <SelectTrigger :selectOpen="quickSelectOpen">
            <SelectValue placeholder="Quick select" />
          </SelectTrigger>
        </div>
        <SelectContent>
          <SelectItem
            v-for="item in items"
            :key="item.value"
            :value="item.value.toString()"
          >
            {{ item.label }}
          </SelectItem>
        </SelectContent>
      </Select>
      <Calendar v-model="selectedDate" />
      
      <div class="flex flex-col gap-2 p-3">
        <span class="text-center !text-midnight-50 text-sm font-medium">
          Time <span class="text-midnight-50/50">(24h)</span>
        </span>
        <TimeFieldRoot
          v-model="selectedTime"
          id="birthday"
          v-slot="{ segments }"
          class="text-lg flex select-none items-center rounded-lg shadow-sm text-center text-green10 border border-transparent p-1 data-[invalid]:border-red-500 justify-center self-center bg-midnight-900"
          :hourCycle="24"
          :defaultValue="parseTime('00:00')"
        >
          <template
            v-for="item in segments"
            :key="item.part"
          >
            <TimeFieldInput
              v-if="item.part === 'literal'"
              :part="item.part"
            >
              {{ item.value }}
            </TimeFieldInput>
            <TimeFieldInput
              v-else
              :part="item.part"
              class="rounded p-1.5 focus:bg-midnight-700 focus:outline-none focus:border-none"
            >
              {{ item.value }}
            </TimeFieldInput>
          </template>
        </TimeFieldRoot>
      </div>

    </PopoverContent>
  </Popover>
</template>