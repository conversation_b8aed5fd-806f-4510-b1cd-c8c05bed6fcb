<template>
  <SwitchGroup as="div" class="flex items-center">
    <Switch
      :disabled="disabled"
      v-model="value"
      :class="[
        value ? 'bg-primary-500' : 'bg-platinum-400 dark:bg-midnight-800',
        'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50',
      ]"
    >
      <span
        aria-hidden="true"
        :class="[
          value ? 'translate-x-5' : 'translate-x-0',
          'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out dark:shadow-white',
        ]"
      />
    </Switch>
    <SwitchLabel v-if="$slots.default" as="label" class="ml-3 cursor-pointer select-none text-sm font-medium">
      <slot></slot>
    </SwitchLabel>
  </SwitchGroup>
</template>

<script>
import { Switch, SwitchGroup, SwitchLabel } from '@headlessui/vue';

export default {
  components: {
    Switch,
    SwitchGroup,
    SwitchLabel,
  },
  props: ['modelValue', 'disabled'],
  emits: ['update:modelValue'],
  computed: {
    value: {
      get() {
        return this.modelValue;
      },
      set(value) {
        this.$emit('update:modelValue', value);
      },
    },
  },
};
</script>
