<script setup lang="ts">
import { ref, watch } from 'vue';
import { cn } from '@/utils';
import { Calendar } from '@/components/ui/shadcn/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/shadcn/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';
import { DateFormatter, type DateValue, getLocalTimeZone, today, parseDate } from '@internationalized/date';
import BIconCalendarEvent from '~icons/bi/calendar-event';

interface QuickSelectItem {
  value: number;
  label: string;
}

interface InputProps {
  modelValue?: string | null;
  invalid?: boolean;
  disabled?: boolean;
  required?: boolean;
  placeholder?: string;
}

const props = withDefaults(defineProps<InputProps>(), {
  modelValue: null,
  invalid: false,
  disabled: false,
  required: false,
  placeholder: 'Select a date',
});

const emit = defineEmits<{
  'update:modelValue': [value: string | null];
}>();

const selectedDate = ref<DateValue | undefined>();
const popoverOpen = ref(false);
const quickSelectOpen = ref(false);

const df = new DateFormatter('en-GB', { dateStyle: 'long' });

const items: QuickSelectItem[] = [
  { value: 0, label: 'Today' },
  { value: 1, label: 'Tomorrow' },
  { value: 2, label: 'In 2 days' },
  { value: 5, label: 'In 5 days' },
  { value: 7, label: 'In a week' },
];

// Watch for external changes to modelValue
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      try {
        selectedDate.value = parseDate(newValue);
      } catch (e) {
        console.warn('Invalid date format provided:', newValue);
        selectedDate.value = undefined;
      }
    } else {
      selectedDate.value = undefined;
    }
  },
  { immediate: true }
);

// Watch for internal changes to selectedDate
watch(selectedDate, (newValue) => {
  if (newValue) {
    emit('update:modelValue', newValue.toString());
  } else {
    emit('update:modelValue', null);
  }
});

const handleQuickSelect = (days: number): void => {
  selectedDate.value = today(getLocalTimeZone()).add({ days });
};
</script>

<template>
  <Popover @update:open="popoverOpen = $event">
    <PopoverTrigger as-child>
      <VButton
        intent="input"
        :disabled="props.disabled"
        :class="
          cn(
            'w-full justify-start text-left font-normal',
            !selectedDate && 'text-muted-foreground',
            popoverOpen && 'ring-2 ring-primary-500',
            props.invalid && 'border-error-500 focus:border-error-500',
            props.disabled && 'cursor-not-allowed opacity-50'
          )
        "
      >
        <BIconCalendarEvent class="-mt-1 mr-1 inline h-4 w-4" />
        {{ selectedDate ? df.format(selectedDate.toDate(getLocalTimeZone())) : props.placeholder }}
      </VButton>
    </PopoverTrigger>
    <PopoverContent class="flex w-auto flex-col gap-y-0 p-2">
      <Select
        @update:open="quickSelectOpen = $event"
        @update:model-value="
          (v) => {
            if (!v) return;
            handleQuickSelect(Number(v));
          }
        "
      >
        <div class="p-3 pb-0">
          <SelectTrigger :selectOpen="quickSelectOpen">
            <SelectValue placeholder="Quick select" />
          </SelectTrigger>
        </div>
        <SelectContent>
          <SelectItem v-for="item in items" :key="item.value" :value="item.value.toString()">
            {{ item.label }}
          </SelectItem>
        </SelectContent>
      </Select>
      <Calendar v-model="selectedDate" />
    </PopoverContent>
  </Popover>
</template>
