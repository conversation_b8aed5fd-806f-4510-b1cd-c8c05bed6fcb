<template>
  <label class="mb-1 block text-sm font-medium text-primary-500" :for="props.for">
    <slot />
    <span v-if="!field.required" class="text-xs text-gray-400"> (optional) </span>
  </label>
</template>

<script setup>
import { inject } from 'vue';

const props = defineProps({
  required: {
    type: Boolean,
    default: false,
  },
});

const field = inject('field', props.required);
</script>
