<template>
    <div class="w-full">
        <FilePond 
            :name="name" 
            :ref="filepondRef"  
            :allow-multiple="multiple"
            :accepted-file-types="accepted" 
            :files="files"
            @init="handleInit"
            @processfile="handleProcess" 
            @removefile="handleRemove"
            :credits="false"
        />
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import vueFilePond, { setOptions } from 'vue-filepond';

import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type/dist/filepond-plugin-file-validate-type.esm.js';
import FilePondPluginImagePreview from 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.esm.js';

import 'filepond/dist/filepond.min.css';
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.min.css';

import { axios, CancelToken } from '@/lib/axios';
import _ from 'lodash';

const props = defineProps({
    name: {
        type: String,
        default: 'file'
    },
    multiple: {
        type: Boolean,
        default: false
    },
    accepted: {
        type: String,
        default: 'image/*'
    },
    modelValue: {
        type: [String, Array],
        default: null
    }
});

const emit = defineEmits(['update:modelValue']);

const FilePond = vueFilePond(FilePondPluginFileValidateType, FilePondPluginImagePreview);
const filepondRef = ref(null);

const urlToFile = async function (path) {
    let url = `${path}`;
    let name = url.split('/').pop();
    const response = await axios.get(url, {
        responseType: 'blob'
    });
    const data = response;
    const metadata = {
        name: name,
        path: path,
        size: data.size,
        type: data.type
    };
    let file = new File([data], path, metadata);

    return {
        source: file,
        options: {
            type: 'local',
            metadata,
        }
    }
}

const files = ref([]);

const handleInit = async () => {
    setOptions({
        credits: '',
        server: {
            revert: (uniqueFileId, load, error) => {
                axios({
                    method: 'DELETE',
                    url: '/api/filepond',
                    data: uniqueFileId
                }).then(res => {
                    load();
                }).catch(err => {
                    error();
                });
            },
            process: (fieldName, file, metadata, load, error, progress, abort) => {
                const formData = new FormData();
                formData.append('file', file, file.name);

                const source = CancelToken.source();

                axios({
                    method: 'post',
                    url: '/api/filepond',
                    data: formData,
                    cancelToken: source.token,
                    onUploadProgress: (e) => {
                        progress(e.lengthComputable, e.loaded, e.total);
                    }
                }).then(response => {
                    load(response);
                })

                return {
                    abort: () => {
                        source.cancel('Operation canceled by the user.');
                    }
                };
            }
        }
    });

    if (!props.modelValue) return;

    if (props.multiple) {
        await Promise.all(Object.values(props.modelValue).map(async (picture) => filepondRef.value.addFile(await urlToFile(picture))))
        return;
    }

    files.value = [
        await urlToFile(_.replace(props.modelValue, 'http://localhost:8000/storage', import.meta.env.VITE_BACKEND_URL + '/api/file/app/public'))
    ]
};

const handleProcess = (error, file) => {
    if (error) {
        throw error;
    }

    const fileId = file.serverId;

    if (props.multiple) {
        const currentFiles = Array.isArray(props.modelValue) ? [...props.modelValue] : [];

        currentFiles.push(fileId);

        emit('update:modelValue', currentFiles);
    } else {
        emit('update:modelValue', fileId);
    }
};


const handleRemove = (error, file) => {
    if (error) {
        throw error;
    }

    if (props.multiple) {
        const currentFiles = Array.isArray(props.modelValue) ? [...props.modelValue] : [];
        const fileId = file.serverId;
        const index = currentFiles.indexOf(fileId);

        if (index > -1) {
            currentFiles.splice(index, 1);

            emit('update:modelValue', currentFiles);
        }
    } else {
        emit('update:modelValue', null);
    }

};


// watch(() => props.modelValue, async () => {
//     console.log(props.modelValue);
//     if (!props.modelValue) return;

//     if (props.multiple) {
//         await Promise.all(Object.values(props.modelValue).map(async (picture) => filepondRef.value.addFile(await urlToFile(picture))))
//         return;
//     }


//     filepondRef.value.addFile(await urlToFile(props.modelValue))
// }, {
//     immediate: true
// });
</script>

<style>
    .filepond--root {
        margin: 0;
    }
</style>