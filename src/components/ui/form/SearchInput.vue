<template>
  <div :class="wrapperClasses" class="relative">
    <div class="relative flex items-center">
      <input
        v-bind="$attrs"
        :value="input"
        @input="setInput"
        type="text"
        class="block w-full rounded-md border-0 bg-platinum-200 py-2 pl-10 pr-8 text-base text-primary-500 placeholder-primary-500 shadow-inner shadow-primary-500/15 transition focus-within:ring-2 focus-within:ring-primary-400 hover:ring-1 hover:ring-primary-400 focus:border-primary-500 focus:bg-primary-200/10 focus:outline-none focus:ring-2 focus:ring-primary-500 active:ring-primary-400 dark:bg-midnight-800 dark:text-midnight-50 dark:placeholder-midnight-50/50 dark:shadow-midnight-700/30 dark:focus-within:ring-primary-700 dark:hover:ring-primary-700 dark:focus:bg-midnight-400 dark:focus:ring-primary-700 dark:active:ring-primary-700"
        placeholder="Search"
      />

      <div class="absolute inset-y-0 left-0 flex items-center pl-3">
        <BiSearch class="h-5 w-5 text-primary-500" />
      </div>

      <BiXCircleFill
        v-if="clearable && value"
        class="pointer-events-auto absolute right-3 h-4 w-4 cursor-pointer text-primary-500/50 transition-colors hover:text-primary-500"
        @click="clear"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

import BiXCircleFill from '~icons/bi/x-circle-fill';
import BiSearch from '~icons/bi/search';

defineOptions({
  inheritAttrs: false,
});

const input = ref(null);

const props = defineProps({
  value: {
    type: String,
    default: '',
  },
  clearable: {
    type: Boolean,
    default: false,
  },
  wrapperClasses: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue', 'clear']);

const clear = () => {
  input.value = '';
  emit('update:modelValue', '');
  emit('clear');
};

const setInput = (event) => {
  input.value = event.target.value;
  emit('update:modelValue', event.target.value);
};
</script>
