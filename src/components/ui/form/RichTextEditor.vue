<template>
  <div
    class="flex flex-col rounded text-gray-500 ring-1 ring-inset ring-platinum-200 transition focus-within:caret-primary-400 dark:ring-transparent scrollbar-thin"
    :class="[
      wrapperClass,
      wrapperClasses({
        invalid: field.invalid || invalid,
        disabled
      }),
    ]"
  >
    <div v-if="editor" class="[&_.tippy-content]:!p-0 [&_.tippy-box]:!bg-transparent">
      <bubble-menu
        v-if="editor"
        :editor="editor"
        :tippy-options="{
          arrow: false,
          duration: 100,
        }"
        class="flex gap-1 rounded-lg p-1 shadow-lg bg-primary-500"
      >
        <button
          v-for="(item, index) in bubbleMenuItems"
          :key="`bubble-${index}`"
          type="button"
          @click.stop.prevent="item.action(editor)"
          :class="[
            'p-1 rounded hover:bg-platinum-100/25 dark:hover:bg-midnight-800/10 text-platinum-300 dark:text-midnight-600/75',
            { 'text-platinum-50 dark:text-midnight-800 bg-platinum-100/25 dark:bg-midnight-900/25': item.isActive(editor) }
          ]"
        >
          <component :is="item.icon" class="w-4 h-4" />
        </button>
      </bubble-menu>

      <floating-menu
        v-if="editor"
        :editor="editor"
        :tippy-options="{
          duration: 100,
          offset: [0, 8]
        }"
        class="flex gap-1 rounded-lg p-1 shadow-lg bg-primary-500"
      >
        <button
          v-for="(item, index) in floatingMenuItems"
          :key="`floating-${index}`"
          type="button"
          @click.stop.prevent="item.action(editor)"
          class="p-1 rounded hover:bg-platinum-100/25 dark:hover:bg-midnight-800/10 text-platinum-300 dark:text-midnight-600/75"
        >
          <component :is="item.icon" class="w-4 h-4" />
        </button>
      </floating-menu>

      <editor-content
        :editor="editor"
        class="w-full min-w-[80px] prose max-w-none appearance-none border-0 bg-transparent px-4 py-2 text-base text-primary-500 outline-none placeholder:text-platinum-700 autofill:bg-white read-only:bg-transparent focus:outline-none focus:ring-0 disabled:cursor-not-allowed disabled:bg-transparent dark:text-midnight-50 dark:placeholder:text-midnight-200 [&_ul]:list-disc [&_ul]:pl-4 [&_ol]:list-decimal [&_ol]:pl-4 [&_p]:mb-4 [&_p]:leading-relaxed [&_p:last-child]:mb-0 [&_ul]:pb-4 [&_ol]:pb-4"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { cva, type VariantProps } from 'class-variance-authority'
import { useEditor, EditorContent, BubbleMenu, FloatingMenu } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import TextAlign from '@tiptap/extension-text-align'
import Color from '@tiptap/extension-color'
import TextStyle from '@tiptap/extension-text-style'
import Underline from '@tiptap/extension-underline'
import Placeholder from '@tiptap/extension-placeholder'
import { inject, watch } from 'vue'
import { Markdown } from 'tiptap-markdown';

// Import your preferred icon components here
// This is a placeholder - replace with your actual icon components
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Heading1,
  Heading2,
  Palette,
} from 'lucide-vue-next'

const wrapperClasses = cva('', {
  variants: {
    invalid: {
      true: 'ring-danger-400 dark:ring-danger-500',
      false: 'ring-1',
    },
    disabled: {
      true: 'cursor-not-allowed',
      false: 'bg-platinum-300 dark:bg-midnight-300 active:ring-primary-400 active:ring-2 focus-within:ring-primary-400 focus-within:ring-2',
    },
  },
  compoundVariants: [
    {
      invalid: false,
      disabled: true,
      class: 'bg-gray-100 ring-gray-300',
    },
    {
      invalid: false,
      disabled: false,
      class: 'ring-gray-300',
    },
    {
      invalid: false,
      disabled: false,
      class:
        'hover:ring-primary-400 dark:hover:ring-primary-600 focus:ring-primary-400 dark:focus:ring-primary-600 active:ring-primary-400 dark:active:ring-primary-600 focus-within:ring-primary-400 dark:focus-within:ring-primary-600',
    },
  ],
})

interface EditorProps {
  modelValue?: string
  invalid?: boolean
  wrapperClass?: string
  disabled?: boolean
  required?: boolean
  placeholder?: string
  markdown?: boolean
  maxRowsVisible?: number | null;
}

Markdown.configure({
  html: true,                  // Allow HTML input/output
  tightLists: true,            // No <p> inside <li> in markdown output
  tightListClass: 'tight',     // Add class to <ul> allowing you to remove <p> margins when tight
  bulletListMarker: '-',       // <li> prefix in markdown output
  linkify: false,              // Create links from "https://..." text
  breaks: true,               // New lines (\n) in markdown input are converted to <br>
  transformPastedText: false,  // Allow to paste markdown text in the editor
  transformCopiedText: false,  // Copied text is transformed to markdown
})

interface FieldProps extends EditorProps {
  id?: string
}

const props = withDefaults(defineProps<EditorProps>(), {
  invalid: false,
  disabled: false,
  required: false,
  placeholder: 'Start typing...',
  markdown: true, // Should be used globally, so can be disabled as required.
  maxRowsVisible: null,
})

const emit = defineEmits(['update:modelValue'])
const field = inject<FieldProps>('field', props)

const editor = useEditor({
  content: props.modelValue,
  editable: !props.disabled,
  extensions: [
    StarterKit,
    TextAlign.configure({
      types: ['heading', 'paragraph'],
    }),
    TextStyle,
    Color,
    Underline,
    Placeholder.configure({
      placeholder: props.placeholder,
      emptyEditorClass: 'before:content-[attr(data-placeholder)] before:float-left before:h-0 before:pointer-events-none before:text-platinum-500 dark:before:text-midnight-200',
    }),
    Markdown,
  ],
  onUpdate: ({ editor }) => {
    emit('update:modelValue', props.markdown ? editor.storage.markdown.getMarkdown() : editor.getHTML())
  },
})

// const markdownOutput = editor.value?.storage.markdown.getMarkdown();

watch(() => props.modelValue, (newValue) => {
  const editorContent = props.markdown ? editor.value?.storage.markdown.getMarkdown() : editor.value?.getHTML()
  if (newValue !== editorContent) {
    editor.value?.commands.setContent(newValue || '')
  }
})

const bubbleMenuItems = [
  {
    icon: Bold,
    action: (editor: any) => editor.chain().focus().toggleBold().run(),
    isActive: (editor: any) => editor.isActive('bold'),
  },
  {
    icon: Italic,
    action: (editor: any) => editor.chain().focus().toggleItalic().run(),
    isActive: (editor: any) => editor.isActive('italic'),
  },
  {
    icon: UnderlineIcon,
    action: (editor: any) => editor.chain().focus().toggleUnderline().run(),
    isActive: (editor: any) => editor.isActive('underline'),
  },
  {
    icon: Strikethrough,
    action: (editor: any) => editor.chain().focus().toggleStrike().run(),
    isActive: (editor: any) => editor.isActive('strike'),
  },
  {
    icon: AlignLeft,
    action: (editor: any) => editor.chain().focus().setTextAlign('left').run(),
    isActive: (editor: any) => editor.isActive({ textAlign: 'left' }),
  },
  {
    icon: AlignCenter,
    action: (editor: any) => editor.chain().focus().setTextAlign('center').run(),
    isActive: (editor: any) => editor.isActive({ textAlign: 'center' }),
  },
  {
    icon: AlignRight,
    action: (editor: any) => editor.chain().focus().setTextAlign('right').run(),
    isActive: (editor: any) => editor.isActive({ textAlign: 'right' }),
  },
]

const floatingMenuItems = [
  {
    icon: Heading1,
    action: (editor: any) => editor.chain().focus().toggleHeading({ level: 1 }).run(),
  },
  {
    icon: Heading2,
    action: (editor: any) => editor.chain().focus().toggleHeading({ level: 2 }).run(),
  },
  {
    icon: List,
    action: (editor: any) => editor.chain().focus().toggleBulletList().run(),
  },
  {
    icon: ListOrdered,
    action: (editor: any) => editor.chain().focus().toggleOrderedList().run(),
  },
]
</script>

<style>
.ProseMirror {
  min-height: 80px;
  outline: none;
}

.ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

</style> 