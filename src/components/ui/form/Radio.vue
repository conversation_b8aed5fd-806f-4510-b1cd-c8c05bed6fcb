<template>
  <label class="flex items-center">
    <input
      v-bind="$attrs"
      v-model="proxyChecked"
      type="radio"
      :value="value"
      class="h-4 w-4 border-gray-300 text-primary-600 focus:ring-primary-500"
    />
    <span v-if="$slots.default" class="ml-3 text-gray-700">
      <slot></slot>
    </span>
  </label>
</template>

<script>
export default {
  inheritAttrs: false,
  props: {
    checked: {
      type: [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>],
      default: false,
    },
    value: {
      type: String,
      default: null,
    },
  },
  emits: ['update:checked'],
  computed: {
    proxyChecked: {
      get() {
        return this.checked;
      },
      set(value) {
        this.$emit('update:checked', value);
      },
    },
  },
};
</script>
