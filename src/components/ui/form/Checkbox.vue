<template>
  <label class="flex items-center">
    <input
      v-bind="$attrs"
      v-model="proxyChecked"
      type="checkbox"
      :value="value"
      :class="size === 'sm' ? 'h-3 w-3' : 'h-4 w-4'"
      class="rounded-full border-none bg-primary-200 text-primary-600 ring-2 ring-primary-300 ring-offset-0 !ring-offset-platinum-50 transition checked:!bg-primary-500 focus:ring-primary-500 dark:bg-midnight-300 dark:ring-primary-500 dark:!ring-offset-midnight-400 dark:checked:!bg-primary-700"
    />
    <span v-if="$slots.default" class="ml-3 select-none text-primary-500">
      <slot></slot>
    </span>
  </label>
</template>

<script>
export default {
  props: {
    checked: {
      type: [Arra<PERSON>, <PERSON><PERSON><PERSON>, Number],
      default: false,
    },
    value: {
      type: String,
      default: null,
    },
    size: {
      type: String,
      default: 'base',
      validator(value) {
        return ['base', 'sm'].includes(value);
      },
    },
  },
  emits: ['update:checked'],
  computed: {
    proxyChecked: {
      get() {
        return this.checked;
      },
      set(value) {
        this.$emit('update:checked', value);
      },
    },
  },
};
</script>
