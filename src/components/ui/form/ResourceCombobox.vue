<template>
  <VCombobox
    :id="field.id"
    v-model="local"
    spinner
    :mode="mode"
    autocomplete="off"
    :filter-results="false"
    :hide-selected="mode === 'multiple' ? false : true"
    :close-on-select="mode === 'multiple' ? false : true"
    :options="flatOptions"
    :placeholder="isResourceValueLoading ? 'Loading...' : placeholder"
    :disabled="isResourceValueLoading"
    :loading="isOptionsLoading && !isOptionsInitialLoading"
    :resolving="isResourceValueLoading"
    :object="true"
    :value-prop="valueProp"
    :no-options-text="isOptionsLoading ? 'Loading...' : 'The list is empty'"
    searchable
    :check-infinite-scroll="checkInfiniteScroll"
    @infinite-scroll="fetchNextPage"
    @open="dirty = true"
    @search-change="search"
  >
    <template v-for="(_, slot) in $slots" #[slot]="scope">
      <slot :name="slot" v-bind="scope || {}" />
    </template>
  </VCombobox>
</template>

<script setup>
import { useInfiniteQuery, useQuery } from '@tanstack/vue-query';
import _ from 'lodash';
import { computed, inject, ref, watch } from 'vue';

import { useFilterParams } from '@/hooks/query';
import { useInfiniteScroll } from '@vueuse/core';

const props = defineProps({
  resource: {
    type: String,
    required: true,
  },
  modelValue: {
    type: [String, Number, Object, Array],
    default: null,
  },
  placeholder: {
    type: String,
    default: 'Select Option',
  },
  id: {
    type: String,
    default: null,
  },
  checkInfiniteScroll: {
    type: Boolean,
    default: false,
  },
  optionsResolver: {
    type: Function,
    required: true,
  },
  optionsQueryKey: {
    type: [Array, String],
    default: () => [],
  },
  modelValueResolver: {
    type: Function,
    required: true,
  },
  valueProp: {
    type: String,
    default: 'locked_id',
  },
  mode: {
    type: String,
    default: 'single',
  },
  eager: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:modelValue', 'rawValue']);

const field = inject('field', props.id);
const dirty = ref(false);
const local = ref(null);

const { params, search } = useFilterParams();

const {
  data: options,
  isFetching: isOptionsLoading,
  isInitialLoading: isOptionsInitialLoading,
  fetchNextPage,
  refetch: refetchOptions
} = props.checkInfiniteScroll
  ? useInfiniteQuery({
      queryKey: [`options:${props.resource}`, params.value, field.value.id, computed(() => props.optionsQueryKey)],
      queryFn: (context) => props.optionsResolver(params.value, context),
      keepPreviousData: true,
      enabled: props.eager || dirty,
      getNextPageParam: (lastPage) => lastPage.meta.current_page === lastPage.meta.last_page ? undefined : lastPage.meta.current_page + 1,
    })
  : useQuery({
      queryKey: [`options:${props.resource}`, params.value, field.value.id, computed(() => props.optionsQueryKey)],
      queryFn: (context) => props.optionsResolver(params.value, context),
      keepPreviousData: true,
      enabled: props.eager || dirty,
    });

const { data: resourceValue, isInitialLoading: isResourceValueLoading, refetch: refetchResourceValue } = useQuery({
  queryKey: [`value:${props.resource}`, props.modelValue, field.value.id],
  queryFn: async (context) => {
    const result = await props.modelValueResolver(props.modelValue, context);
    return result;
  },
});

watch(resourceValue, (value) => {
  local.value = value;
})

const flatOptions = computed(() =>
  props.checkInfiniteScroll ? options.value?.pages?.map((page) => page?.data).flat() : options.value
);

watch(
  () => props.modelValue,
  (value) => {
    if (!value) {
      local.value = {};
      return;
    }

    if (props.mode === 'single' && value === local.value?.[props.valueProp]) {
      return;
    }

    if (
      props.mode === 'tags' &&
      _.isEqual(
        value,
        local.value?.map((item) => item?.[props.valueProp])
      )
    ) {
      return;
    }

    refetchResourceValue();
  },
  {
    immediate: true,
    deep: true,
  }
);

watch(local, (value) => {
  emit('rawValue', value);

  if (props.mode === 'single') {
    emit('update:modelValue', value?.[props.valueProp]);
    return;
  }

  if (props.mode === 'tags' || props.mode === 'multiple') {
    emit(
      'update:modelValue',
      value?.map((item) => item?.[props.valueProp])
    );
    return;
  }

  emit('update:modelValue', value);
});

defineExpose({
  refetch: () => {
    refetchOptions();
    refetchResourceValue();
  }
});
</script>
