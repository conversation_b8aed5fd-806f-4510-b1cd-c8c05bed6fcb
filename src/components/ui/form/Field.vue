<template>
  <TransitionFade>
    <div v-if="!loading">
      <template v-if="$slots.label">
        <slot name="label" />
        <slot />
      </template>

      <template v-else>
        <div class="relative mb-1 flex items-center space-x-1.5" :class="labelClass">
          <label :for="id" class="block text-sm font-medium text-primary-500">{{ label }}</label>
          <VTooltip v-if="help" :content="help" class="flex items-center">
            <BiInfoCircle class="flex h-3.5 w-3.5 text-primary-500" />
          </VTooltip>
          <span v-if="!required" class="text-xs text-platinum-600">(optional)</span>
        </div>

        <div>
          <slot></slot>
        </div>
      </template>

      <TransitionFade>
      <div
        v-for="error in errors"
        :key="error.index"
        role="alert"
        :aria-label="error"
        class="mt-1 text-sm text-danger-400 dark:text-danger-500"
        :class="feedbackClass"
      >
        {{ error }}
      </div>
      </TransitionFade>
    </div>
    <div v-else>
      <Placeholder tagClasses="h-5 mb-1 w-1/4" />
      <Placeholder />
    </div>
  </TransitionFade>
</template>

<script setup>
import { v4 as uuid } from 'uuid';
import { computed, provide } from 'vue';
import BiInfoCircle from '~icons/bi/info-circle';
import Placeholder from '@/components/ui/placeholders/PlaceholderCustom.vue';

const props = defineProps({
  id: {
    type: String,
    default: () => `field-${uuid()}`,
  },
  label: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  errors: {
    type: [String, Array],
    default: () => [],
  },
  help: {
    type: String,
    default: '',
  },
  loading: {
    type: Boolean,
    default: false,
  },
  feedbackClass: {
    type: String,
    default: '',
  },
  labelClass: {
    type: String,
    default: '',
  },
});

const errors = computed(() => Array.of(props.errors).flat());

provide(
  'field',
  computed(() => {
    return {
      ...props,
      invalid: !!errors.value.length,
    };
  })
);
</script>
