<template>
  <color-input
    ref="input"
    class="absolute left-0 [&>.box.active]:!bg-primary-500 [&>.box.active]:bg-none [&>.box]:hover:!ring-primary-400 dark:[&>.box]:hover:!ring-primary-600 [&>.box.active]:!ring-2 [&>.box.active]:!ring-primary-500 [&>.box]:!ring-1 [&>.box]:hover:!ring-2 [&>.box]:!ring-platinum-400 [&>.box]:dark:!ring-midnight-200"
    :class="[
      {
        'no-color-picker-pattern': disableAlpha,
        '[&>.box]:!rounded-full': rounded,
        '[&>.box]:!h-4 [&>.box]:!w-4': size === 'sm',
        '[&>.box]:!h-6 [&>.box]:!w-6': size === 'base',
        '[&>.box]:!h-8 [&>.box]:!w-8': size === 'lg',
        '[&>.box]:!h-10 [&>.box]:!w-10': size === 'xl',
        '[&>.box]:!h-14 [&>.box]:!w-14': size === '2xl',
      },
      [
        // Adjustments to the container/wrapper.
        '[&>.picker-popup]:rounded-md [&>.picker-popup>.saturation-area]:!rounded-t-md [&>.picker-popup>.saturation-area>.slider-canvas]:!rounded-t  [&>.picker-popup]:!bg-platinum-300 dark:[&>.picker-popup]:!bg-midnight-700',
        // Inputs:
        '[&>.picker-popup>.text-inputs-area>.text-inputs-wrapper>.text-input-container>.text-input]:!min-w-[60px]',
        '[&>.picker-popup>.text-inputs-area>.text-inputs-wrapper>.text-input-container>#text-input-hex]:!min-w-[110px]',
        '[&>.picker-popup>.text-inputs-area>.text-inputs-wrapper]:!flex-nowrap',
        '[&>.picker-popup>.text-inputs-area>.text-inputs-wrapper>.text-input-container>.text-input]:!bg-white',
        '[&>.picker-popup>.text-inputs-area>.text-inputs-wrapper>.text-input-container>.text-input]:dark:!bg-midnight-300',
        '[&>.picker-popup>.text-inputs-area>.text-inputs-wrapper>.text-input-container>.text-input]:!text-gray-500',
        '[&>.picker-popup>.text-inputs-area>.text-inputs-wrapper>.text-input-container>.text-input]:dark:!text-midnight-50',
        '[&>.picker-popup>.text-inputs-area>.text-inputs-wrapper>.text-input-container>.text-input]:!ring-1',
        '[&>.picker-popup>.text-inputs-area>.text-inputs-wrapper>.text-input-container>.text-input]:!ring-transparent',
        '[&>.picker-popup>.text-inputs-area>.text-inputs-wrapper>.text-input-container>.text-input]:rounded-md',
        '[&>.picker-popup>.text-inputs-area>.text-inputs-wrapper>.text-input-container>.text-input]:border-none',
        '[&>.picker-popup>.text-inputs-area>.text-inputs-wrapper>.text-input-container>_label]:!text-primary-500',
        '[&>.picker-popup>.text-inputs-area>.text-inputs-wrapper>.text-input-container>_label]:!capitalize',
        '[&>.picker-popup>.text-inputs-area>.text-inputs-wrapper>.text-input-container>_label]:!font-bold',

      ]
    ]"
    v-model="value"
    :disable-alpha="disableAlpha"
    :format="format"
    :position="responsivePosition"
  />
</template>

<script setup>
import ColorInput from 'vue-color-input';
import { useVModel } from '@/hooks/model';
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core';
import { ref, computed } from 'vue';

const breakpoints = useBreakpoints(breakpointsTailwind);

const props = defineProps({
  modelValue: {
    type: String,
    default: null,
  },
  disableAlpha: {
    type: Boolean,
    default: false,
  },
  format: {
    type: String,
    default: 'hex string',
  },
  rounded: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String,
    default: 'base',
  },
  position: {
    type: String,
    default: 'bottom right',
  },
});

const emit = defineEmits(['update:modelValue']);

const value = useVModel(props, 'modelValue', emit);

const input = ref(null);

defineExpose({
  focus: () => input.value.focus(),
});

const responsivePosition = computed(() => {
  return breakpoints.greaterOrEqual('md').value ? props.position : 'bottom';
});

</script>

<style>
.color-input .box .transparent {
  background-color: transparent !important;
}

.color-input .box.active .inner {
  transform: unset !important;
}

.no-color-picker-pattern {
  --transparent-pattern: unset !important;
}
</style>
