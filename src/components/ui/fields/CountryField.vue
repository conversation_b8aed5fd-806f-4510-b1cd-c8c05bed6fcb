<template>
  <div class="flex items-center space-x-1.5">
    <span
      :class="[
        'fi',
        `fi-${iso.toLowerCase()}`,
        format === 'square' ? 'fi-squared' : '',
        'h-5 w-5 block'
      ]"
    ></span>
    <span v-if="name" class="truncate">
      {{ name }}
    </span>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import 'flag-icons/css/flag-icons.min.css'

defineProps({
  iso: {
    type: String,
    required: true,
    validator: (val) => val.length === 2,
  },
  name: {
    type: String,
    required: false,
  },
  format: {
    type: String,
    default: 'default',
    validator: (val) => ['default', 'square'].includes(val),
  },
})
</script>