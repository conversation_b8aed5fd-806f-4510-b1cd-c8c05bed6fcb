<template>
  <div class="flex flex-col">
    <div class="flex items-center space-x-2" v-if="ip">
      <span class="font-medium">IP:</span>
      <span>{{ ip }}</span>
    </div>
    <div class="flex items-center space-x-2" v-if="data.city">
      <span class="font-medium">City:</span>
      <span>{{ data.city }}</span>
    </div>
    <div class="flex items-center space-x-2" v-if="data.country_name">
      <span class="font-medium">Country:</span>
      <CountryField :iso="data.country_code" :name="data.country_name" />
    </div>
    <div class="flex items-center space-x-2" v-if="data.zip_code">
      <span class="font-medium">Post / Zip:</span>
      <span>{{ data.zip_code }}</span>
    </div>
  </div>
</template>

<script setup>
import CountryField from '@/components/ui/fields/CountryField.vue'

defineProps({
  data: {
    type: Object,
    required: true
  },
  ip: {
    type: String,
    required: true
  }
})
</script>
