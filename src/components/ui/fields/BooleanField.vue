<template>
  <template v-if="typeof value === 'boolean'">
    <span class="flex items-center gap-1">
      <BIconCheckCircle v-if="value" class="w-4 overflow-visible text-success-300 dark:text-success-500/75" />
      <BIconXCircle v-else class="w-4 overflow-visible text-danger-300 dark:text-danger-500" />
      {{ value ? trueLabel : falseLabel }}
    </span>
  </template>
  <span v-else class="italic text-gray-500">Undefined</span>
</template>

<script setup>
import BIconCheckCircle from '~icons/bi/check-circle-fill';
import BIconXCircle from '~icons/bi/x-circle-fill';

defineProps({
  value: {
    type: Boolean,
    required: false,
  },
  trueLabel: {
    type: String,
    required: false,
    default: 'Yes',
  },
  falseLabel: { 
    type: String,
    required: false,
    default: 'No',
  },
});
</script>