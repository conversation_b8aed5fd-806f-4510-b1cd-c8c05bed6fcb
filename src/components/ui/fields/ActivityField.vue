<template>
  <Table
    class="mt-1 sm:mt-0 !rounded !overflow-hidden [&_td]:align-top"
    :columns="tableColumns"
    :records="tableRecords"
    :sortable="false"
    :paginated="false"
    :searchable="false"
    :rowModelCore="true"
  />
</template>

<script setup lang="ts">
import { computed, h } from 'vue';
import Table from '@/components/ui/tables/Table.vue';
import TableCell from '@/components/ui/tables/TableCell.vue';
import { formattedDate } from '@/util/dateUtils';

interface Props {
  value: {
    attributes: Record<string, any>;
    old?: Record<string, any>;
  };
}
const props = defineProps<Props>();

const attrs = computed(() => props.value?.attributes || {});
const oldAttrs = computed(() => props.value?.old || {});
const isUpdate = computed(() => !!props.value?.old);

// Readable key labels.
function humanizeKey(key: string): string {
  return key
    .replace(/_/g, ' ')
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    .replace(/\b(id|Id)\b/g, 'ID')
    .replace(/\b([a-z])/g, (m) => m.toUpperCase())
    .replace(/\b([A-Z]{2,})\b/g, (m) => m.toUpperCase());
}

// Pretty value display (booleans & dates, etc).
function humanizeValue(value: any): any {
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }
  // If the value aligns to a date string, format it to be readable.
  if (
    typeof value === 'string' && (
      value.match(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)
      || value.match(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)
    )
  ) {
    return formattedDate(value);
  }
  return value;
}

// Checks null, undefined, empty string, empty array, empty object.
function isEmpty(val: any): boolean {
  if (val === null || val === undefined) return true;
  if (typeof val === 'string') return val.trim() === '';
  if (Array.isArray(val)) return val.length === 0;
  if (typeof val === 'object') return Object.keys(val).length === 0;
  return false;
}

// Columns - Update mode has oldValue/newValue, create mode just value.
const tableColumns = computed(() => {
  if (isUpdate.value) {
    return [
      {
        header: 'Key',
        accessorKey: 'key',
        cell: (info) => humanizeKey(info.getValue()),
      },
      {
        header: 'Old Value',
        accessorKey: 'oldValue',
        cell: (info) =>
          h(TableCell, {
            value: humanizeValue(info.getValue()),
            truncate: false,
            class: 'whitespace-pre-line',
          }),
      },
      {
        header: 'New Value',
        accessorKey: 'newValue',
        cell: (info) =>
          h(TableCell, {
            value: humanizeValue(info.getValue()),
            truncate: false,
            class: 'whitespace-pre-line',
          }),
      },
    ];
  }

  return [
    {
      header: 'Key',
      accessorKey: 'key',
      cell: (info) => humanizeKey(info.getValue()),
    },
    {
      header: 'Value',
      accessorKey: 'newValue', // use newValue to keep consistent with update mode
      cell: (info) =>
        h(TableCell, {
          value: humanizeValue(info.getValue()),
          truncate: false,
          class: 'whitespace-pre-line',
        }),
    },
  ];
});

// Build table records for update or create mode.
const tableRecords = computed(() => {
  if (isUpdate.value) {
    const records: { key: string; oldValue: any; newValue: any }[] = [];
    // Process oldAttrs to detect updates and removals.
    for (const [key, oldVal] of Object.entries(oldAttrs.value)) {
      if (key in attrs.value) {
        const newVal = attrs.value[key];
        // Push only if values differ (including null vs non-null).
        if (
          (newVal !== oldVal) &&
          !(isEmpty(newVal) && isEmpty(oldVal)) // Unlikely since API should handle, but ignore if both empty/match.
        ) {
          records.push({ key, oldValue: oldVal, newValue: newVal ?? '—' });
        }
      } else {
        // Key removed in new attrs.
        records.push({ key, oldValue: oldVal, newValue: '—' });
      }
    }
    // Process attrs to detect added keys
    for (const [key, newVal] of Object.entries(attrs.value)) {
      if (!(key in oldAttrs.value) && !isEmpty(newVal)) {
        records.push({ key, oldValue: '—', newValue: newVal });
      }
    }
    return records;
  }

  // Create mode - list all non-empty attrs as newValue with no oldValue.
  return Object.entries(attrs.value)
    .filter(([_, val]) => !isEmpty(val))
    .map(([key, val]) => ({
      key,
      oldValue: '—',
      newValue: val,
    }));
});
</script>
