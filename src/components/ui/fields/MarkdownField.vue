<template>
  <div
    v-html="markdownHtml"
    class="html-content"
  />
</template>

<script setup lang="ts">
import markdownit from 'markdown-it';
import markdownitMark from 'markdown-it-mark';
import { computed } from 'vue';

const props = withDefaults(defineProps<{
  value?: string
}>(), {
  value: '',
});

const md = markdownit({ breaks: true }).use(markdownitMark);
const markdownHtml = computed(() => {
  if (!props.value || typeof props.value !== 'string') return '';
  const unescaped = props.value.replace(/\\n/g, '\n');
  return md.render(unescaped);
});

</script>

<style scoped>
.html-content :deep(p) {
  @apply mb-4;
}

.html-content :deep(p:last-child) {
  @apply mb-0;
}

.html-content :deep(h1) {
  @apply text-2xl font-semibold mb-4 mt-6;
}

.html-content :deep(h2) {
  @apply text-xl font-semibold mb-3 mt-5;
}

.html-content :deep(h3) {
  @apply text-lg font-semibold mb-2 mt-4;
}

.html-content :deep(h4) {
  @apply text-base font-semibold mb-2 mt-3;
}

.html-content :deep(h5) {
  @apply text-sm font-semibold mb-1 mt-2;
}

.html-content :deep(h6) {
  @apply text-xs font-semibold mb-1 mt-2;
}

.html-content :deep(ul) {
  @apply mb-4 pl-6 list-disc;
}

.html-content :deep(ol) {
  @apply mb-4 pl-6 list-decimal;
}

.html-content :deep(li) {
  @apply mb-1;
}

.html-content :deep(blockquote) {
  @apply mb-4 pl-4 border-l-4 border-gray-300;
}

.html-content :deep(code) {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm;
}

.html-content :deep(pre) {
  @apply bg-gray-100 p-4 rounded overflow-x-auto mb-4;
}

.html-content :deep(table) {
  @apply mb-4 w-full;
}

.html-content :deep(thead) {
  @apply border-b;
}

.html-content :deep(th) {
  @apply p-2 text-left;
}

.html-content :deep(td) {
  @apply p-2 border-b;
}

.html-content :deep(img) {
  @apply mb-4;
}

.html-content :deep(hr) {
  @apply mb-4 mt-4;
}

.html-content :deep(strong) {
  @apply font-semibold;
}

.html-content :deep(em) {
  @apply italic;
}
</style>
