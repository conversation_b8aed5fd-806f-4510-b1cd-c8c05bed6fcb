<template>
  <div class="group/row sm:grid sm:grid-cols-3">
    <div
      class="px-6 pt-4 transition-colors group-hover/row:bg-primary-200/30 dark:group-hover/row:bg-primary-600/10 sm:bg-platinum-100 sm:pt-4 sm:dark:bg-midnight-500 lg:gap-4 lg:py-5 xl:group-[.multiple-columns]/container:bg-transparent"
    >
      <dt class="text-sm font-semibold text-primary-500 lg:text-base">
        <slot v-if="$slots.label" name="label" />
        <template v-else>
          {{ label }}
        </template>
      </dt>
    </div>

    <dd
      class="col-span-2 px-6 pb-4 pt-0 text-sm text-platinum-950 transition-colors group-hover/row:bg-primary-200/30 dark:text-midnight-50 dark:group-hover/row:bg-primary-600/10 sm:pt-4 lg:gap-4 lg:py-5 lg:text-base"
    >
      <div class="relative flex justify-between">
        <div class="shrink">
          <slot v-if="$slots.default" name="default" />
          <template v-else>
            <div class="flex items-center gap-2">
              <VTextLink v-if="link" :to="link" class="text-primary-500">
                <component :is="icon" v-if="icon" class="-mt-0.5 mr-0.5 inline w-4" />
                {{ value }}
              </VTextLink>
              <span v-else-if="value">
                <component :is="icon" v-if="icon" class="-mt-0.5 mr-0.5 inline w-4" />
                {{ value }}
              </span>
              <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
            </div>
          </template>
        </div>
        <div
          v-if="value && allowCopy"
          class="flex items-top pl-1"
        >
          <ClipboardCopy :value="value" class="flex-none -mb-1" />
        </div>
      </div>
    </dd>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import ClipboardCopy from '@/components/ui/icons/ClipboardCopy.vue';
import BIconTelephoneFill from '~icons/bi/telephone-fill';
import BIconEnvelopeFill from '~icons/bi/envelope-fill';
import BIconPrinterFill from '~icons/bi/printer-fill';

const props = defineProps({
  label: {
    type: String,
    required: false,
  },
  value: {
    type: String,
    required: false,
  },
  method: {
    type: String,
    required: false,
  },
  allowCopy: {
    type: Boolean,
    default: false,
  },
});

// Computed property to return a relevant link for the contact method if any.
const link = computed(() => {
  if (!props.method) return null;
  const methodLower = props.method.toLowerCase();
  if (methodLower.includes('email')) {
    return `mailto:${props.value}`;
  } else if (methodLower.includes('phone')) {
    return `tel:${props.value}`;
  } else if (methodLower.includes('fax')) {
    return `fax:${props.value}`;
  }
  return null;
});

// Computed property to return a relevant icon for the contact method if any.
const icon = computed(() => {
  if (!props.method) return null;
  const methodLower = props.method.toLowerCase();
  if (methodLower.includes('email')) {
    return BIconEnvelopeFill;
  } else if (methodLower.includes('phone')) {
    return BIconTelephoneFill;
  } else if (methodLower.includes('fax')) {
    return BIconPrinterFill;
  }
  return null;
});
</script>
