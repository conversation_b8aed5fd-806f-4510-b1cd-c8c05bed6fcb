<template>
  <div class="group/row sm:grid sm:grid-cols-3">
    <div
      class="px-6 pt-4 transition-colors group-hover/row:bg-primary-200/30 dark:group-hover/row:bg-primary-600/10 sm:bg-platinum-100 sm:pt-4 sm:dark:bg-midnight-500 lg:gap-4 lg:py-5 xl:group-[.multiple-columns]/container:bg-transparent"
    >
      <dt class="text-sm font-semibold text-primary-500 lg:text-base">
        <slot v-if="$slots.label" name="label" />
        <template v-else>
          {{ label }}
        </template>
      </dt>
    </div>

    <dd
      class="col-span-2 px-6 pb-4 pt-0 text-sm text-platinum-950 transition-colors group-hover/row:bg-primary-200/30 dark:text-midnight-50 dark:group-hover/row:bg-primary-600/10 sm:pt-4 lg:gap-4 lg:py-5 lg:text-base"
    >
      <div class="relative flex justify-between">
        <div class="shrink">
          <slot v-if="$slots.default" name="default" />
          <template v-else>
            <div class="flex items-center gap-2">
              <div class="flex items-center gap-2" v-if="iso2 || value">
                  <img
                    v-if="iso2 && showFlag"
                    :src="`https://flagicons.lipis.dev/flags/4x3/${iso2.toLowerCase()}.svg`"
                    class="inline h-5 w-5"
                    :title="iso2"
                    v-show="flagLoaded"
                    @load="flagLoaded = true"
                    @error="flagLoaded = false"
                  />
                  <VEntityLink
                    v-if="countryId"
                    :entity="countryId"
                    entityType="Country"
                    :entityId="countryId"
                    :value="value"
                    :showIcon="false"
                  />
                  <span v-else>{{ value }}</span>
                </div>
              <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
            </div>
          </template>
        </div>
        <div
          v-if="value && allowCopy"
          class="flex items-top pl-1"
        >
          <ClipboardCopy :value="value" class="flex-none -mb-1" />
        </div>
      </div>
    </dd>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import ClipboardCopy from '@/components/ui/icons/ClipboardCopy.vue';
import BIconPersonFill from '~icons/bi/person-fill';

const props = defineProps({
  label: {
    type: String,
    required: false,
  },
  value: {
    type: String,
    required: false,
  },
  countryId: {
    type: String,
    required: false,
    default: null,
  },
  iso2: {
    type: String,
    required: false,
    default: null,
  },
  allowCopy: {
    type: Boolean,
    default: false,
  },
  showFlag: {
    type: Boolean,
    default: true,
  },
});

const flagLoaded = ref(false);

// Computed property to return a relevant link for the entity.
const link = computed(() => {
  if (!props.entity) return null;
  const entityLower = props.entity.toLowerCase();
  if (entityLower === 'user') {
    return `mailto:${props.value}`;
  }
  return null;
});

// Computed property to return a relevant icon for the contact method if any.
const icon = computed(() => {
  if (!props.entity) return null;
  const entityLower = props.entity.toLowerCase();
  if (entityLower === 'user') {
    return BIconPersonFill;
  }
  return null;
});
</script>
