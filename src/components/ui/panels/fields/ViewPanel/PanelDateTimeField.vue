<template>
  <div class="group/row sm:grid sm:grid-cols-3">
    <div
      class="px-6 pt-4 transition-colors group-hover/row:bg-primary-200/30 dark:group-hover/row:bg-primary-600/10 sm:bg-platinum-100 sm:pt-4 sm:dark:bg-midnight-500 lg:gap-4 lg:py-5 xl:group-[.multiple-columns]/container:bg-transparent"
    >
      <dt class="text-sm font-semibold text-primary-500 lg:text-base">
        <slot v-if="$slots.label" name="label" />
        <template v-else>
          <span class="flex items-center gap-1">
            {{ label }}
            <span v-if="help" class="flex items-center">
              <VTooltip :content="help" class="flex items-center">
                <BIconInfoCircle class="flex h-3.5 w-3.5 text-primary-500 mt-0.5" />
              </VTooltip>
            </span>
          </span>
        </template>
      </dt>
    </div>

    <dd
      class="col-span-2 px-6 pb-4 pt-0 text-sm text-platinum-950 transition-colors group-hover/row:bg-primary-200/30 dark:text-midnight-50 dark:group-hover/row:bg-primary-600/10 sm:pt-4 lg:gap-4 lg:py-5 lg:text-base"
    >
      <div class="relative flex justify-between">
        <div class="shrink">
          <slot v-if="$slots.default" name="default" />
          <template v-else>
            <div class="">
              <span v-if="value">{{ formatted }}</span>
              <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
            </div>
            </template>
        </div>
        <div
          v-if="value && allowCopy"
          class="flex items-top pl-1"
        >
          <ClipboardCopy :value="value" class="flex-none -mb-1" />
        </div>
      </div>
    </dd>
  </div>
</template>

<script setup>
import { useDateFormat } from '@vueuse/shared';
import ClipboardCopy from '@/components/ui/icons/ClipboardCopy.vue';
import { computed } from 'vue';
import BIconInfoCircle from '~icons/bi/info-circle';

const props = defineProps({
  label: {
    type: String,
    required: false,
  },
  value: {
    type: String,
    required: false,
  },
  allowCopy: {
    type: Boolean,
    default: false,
  },
  format: {
    type: String,
    default: 'YYYY-MM-DD HH:mm:ss',
  },
  locale: {
    type: [String, Boolean],
    default: 'en-GB',
  },
  help: {
    type: String,
    required: false,
  },
});

const formatted = computed(() => {
  if (!props.value) return '';

  try {
    if (props.locale) {
      const date = new Date(props.value);
      return new Intl.DateTimeFormat(props.locale, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      }).format(date);
    }
    return useDateFormat(props.value, props.format);
  } catch (error) {
    console.warn('Invalid date format or locale:', error);
    return props.value;
  }
});
</script>
