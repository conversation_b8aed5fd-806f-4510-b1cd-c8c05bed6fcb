<template>
  <div class="group/row sm:grid sm:grid-cols-3">
    <div
      class="px-6 pt-4 transition-colors group-hover/row:bg-primary-200/30 dark:group-hover/row:bg-primary-600/10 sm:bg-platinum-100 sm:pt-4 sm:dark:bg-midnight-500 lg:gap-4 lg:py-5 xl:group-[.multiple-columns]/container:bg-transparent"
    >
      <dt class="text-sm font-semibold text-primary-500 lg:text-base">
        <slot v-if="$slots.label" name="label" />
        <template v-else>
          <span class="flex items-center gap-1">
            {{ label }}
            <span class="flex items-center">
              <VTooltip v-if="help" :content="help" class="flex items-center">
                <BIconInfoCircle class="flex h-3.5 w-3.5 text-primary-500 mt-0.5" />
              </VTooltip>
            </span>
          </span>
        </template>
      </dt>
    </div>

    <dd
      class="col-span-2 px-6 pb-4 pt-0 text-sm text-platinum-950 transition-colors group-hover/row:bg-primary-200/30 dark:text-midnight-50 dark:group-hover/row:bg-primary-600/10 sm:pt-4 lg:gap-4 lg:py-5 lg:text-base"
    >
      <div class="flex items-center gap-1.5">
        <BIconCheckCircle v-if="booleanValue" class="w-4 overflow-visible text-success-300 dark:text-success-500/75 -mt-0.5" />
        <BIconXCircle v-else class="w-4 overflow-visible text-danger-300 dark:text-danger-500 -mt-0.5" />
        <span v-if="showTextValues">{{ booleanValue ? trueText : falseText }}</span>
      </div>
    </dd>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import BIconCheckCircle from '~icons/bi/check-circle-fill';
import BIconXCircle from '~icons/bi/x-circle-fill';
import BIconInfoCircle from '~icons/bi/info-circle';

// Computed property to specifically ensure if value is true or false.
const booleanValue = computed(() => {
  if (typeof props.value === 'boolean') {
    return props.value;
  } else if (props.value === 'true' || props.value === 1) {
    return true;
  } else if (props.value === 'false' || props.value === 0) {
    return false;
  }
});

const props = defineProps({
  label: {
    type: String,
    required: false,
  },
  value: {
    type: [String, Boolean],
    required: false,
  },
  showTextValues: {
    type: Boolean,
    default: true,
  },
  trueText: {
    type: String,
    default: 'Yes',
  },
  falseText: {
    type: String,
    default: 'No',
  },
  help: {
    type: String,
    required: false,
  },
});
</script>
