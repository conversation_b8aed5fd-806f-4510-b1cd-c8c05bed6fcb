<template>
  <div class="group/row sm:grid sm:grid-cols-3">
    <div class="px-6 pt-4 transition-colors group-hover/row:bg-primary-200/30 dark:group-hover/row:bg-primary-600/10 sm:bg-platinum-100 sm:pt-4 sm:dark:bg-midnight-500 lg:gap-4 lg:py-4 xl:group-[.multiple-columns]/container:bg-transparent">
      <dt class="text-sm font-semibold text-primary-500 lg:text-base flex items-center sm:justify-between gap-1 h-full">
        <slot v-if="$slots.label" name="label" />
        <template v-else>
          <span class="flex items-center gap-1">
            {{ label }}
            <span v-if="help" class="flex items-center">
              <VTooltip :content="help" class="flex items-center">
                <BIconInfoCircle class="flex h-3.5 w-3.5 text-primary-500 mt-0.5" />
              </VTooltip>
            </span>
          </span>
          <VTooltip
            v-if="$slots.dropdown"
            :content="collapsed ? 'Expand' : 'Collapse'"
            class="flex items-center"
          >
            <VButton
              v-if="$slots.dropdown"
              intent="panelButton"
              size="sm"
              @click="collapsed = !collapsed"
            >
              <BIconChevronDown
                class="w-2.5 h-2.5 md:w-3 md:h-3 transition-transform duration-300 ease-in-out"
                :class="!collapsed ? 'rotate-180' : ''"
              />
            </VButton>
          </VTooltip>
        </template>
      </dt>
    </div>
    <dd class="col-span-2 relative">
      <div class="px-6 pb-4 pt-0 text-sm text-platinum-950 transition-colors group-hover/row:bg-primary-200/30 dark:text-midnight-50 dark:group-hover/row:bg-primary-600/10 sm:pt-4 lg:gap-4 lg:py-4 lg:text-base [&_ul]:list-disc [&_ul]:pl-5 [&_ol]:list-decimal [&_ol]:pl-5 h-full [&_p]:mb-2">
        <div
          class="flex justify-between"
          :class="extend ? 'pb-6' : ''"
        >
          <div
            class="shrink overflow-y-hidden transition-all duration-300 ease-in-out w-full py-1"
            ref="contentBody"
            :style="{ maxHeight: extend ? `${contentBody?.scrollHeight}px` : '40vh' }"
          >
            <slot v-if="$slots.default" name="default" />
            <template v-else>
              <span v-if="value && html" v-html="value" />
              <span v-else-if="value && markdown" v-html="markdownHtml" />
              <span v-else-if="value && !html">{{ value }}</span>
              <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
            </template>
          </div>
          <div v-if="value && allowCopy" class="flex items-top pl-1 -mt-3 sm:mt-0">
            <ClipboardCopy :value="value" class="flex-none -mb-1" />
          </div>
          <div 
            v-if="hasExceededHeight"
            class="absolute bottom-0 left-0 w-full bg-gradient-to-b from-transparent to-platinum-200 via-platinum-200/80 dark:from-transparent dark:to-midnight-500 dark:via-midnight-500/80 transition-opacity opacity-100 hover:opacity-90 text-center"
          >
            <VTooltip
              :content="extend ? 'Collapse Content' : 'Expand Content'"
              class="w-full"
              placement="bottom"
            >
              <VButton intent="fieldExtend" @click="toggleExtend" centered>
                <BIconChevronDownCompact
                  class="w-8 h-8 transition-transform ease-in-out duration-300"
                  :class="extend ? 'rotate-180' : ''"
                />
              </VButton>
            </VTooltip>
          </div>
        </div>
      </div> 
    </dd>
  </div>
  <!-- <div v-if="$slots.dropdown" class="divide-y divide-platinum-200 dark:divide-midnight-700"> -->
  <VCollapse
    :when="!collapsed"
    class="duration-250 transition-all divide-y divide-platinum-200 dark:divide-midnight-700"
    :class="collapsed ? '!border-0' : ''"
    :on-collapsed="() => (collapsed = true)"
  >
    <slot name="dropdown" />
  </VCollapse>
</template>

<script setup lang="ts">
import { ref, nextTick, computed } from 'vue';
import ClipboardCopy from '@/components/ui/icons/ClipboardCopy.vue';
import BIconChevronDownCompact from '~icons/bi/chevron-compact-down';
import BIconInfoCircle from '~icons/bi/info-circle';
import BIconChevronDown from '~icons/bi/chevron-down';
import markdownit from 'markdown-it';

const props = withDefaults(defineProps<{
  label?: string;
  value?: string | number | boolean | any[] | object;
  allowCopy?: boolean;
  help?: string;
  html?: boolean;
  markdown?: boolean;
}>(), {
  label: '',
  value: '',
  allowCopy: false,
  help: '',
  html: false,
  markdown: false,
});

// Convert markdown to HTML.
const md = markdownit({ breaks: true });
const markdownHtml = computed(() => {
  if (!props.value || typeof props.value !== 'string' || !props.markdown) return '';
  const unescaped = props.value.replace(/\\n/g, '\n');
  return md.render(unescaped);
});

const extend = ref(false);
const contentBody = ref(null);
const collapsed = ref(true);

const toggleExtend = () => {
  extend.value = !extend.value;
  nextTick(() => {
    contentBody.value.style.maxHeight = extend.value
      ? `${contentBody.value.scrollHeight}px`
      : '40vh';
  });
};

const hasExceededHeight = computed(() => {
  if (!contentBody.value) return false;
  return contentBody.value.scrollHeight > (window.innerHeight * 0.4);
});
</script>
