<template>
  <div class="group/row sm:grid sm:grid-cols-3">
    <div
      class="px-6 pt-4 transition-colors group-hover/row:bg-primary-200/30 dark:group-hover/row:bg-primary-600/10 sm:bg-platinum-100 sm:pt-4 sm:dark:bg-midnight-500 lg:gap-4 lg:py-5 xl:group-[.multiple-columns]/container:bg-transparent"
    >
      <dt class="text-sm font-semibold text-primary-500 lg:text-base">
        <slot v-if="$slots.label" name="label" />
        <template v-else>
          <span class="flex items-center gap-1">
            {{ label }}
            <span class="flex items-center">
              <VTooltip v-if="help" :content="help" class="flex items-center">
                <BIconInfoCircle class="flex h-3.5 w-3.5 text-primary-500 mt-0.5" />
              </VTooltip>
            </span>
          </span>
        </template>
      </dt>
    </div>

    <dd
      class="col-span-2 px-6 pb-4 pt-0 text-sm text-platinum-950 transition-colors group-hover/row:bg-primary-200/30 dark:text-midnight-50 dark:group-hover/row:bg-primary-600/10 sm:pt-4 lg:gap-4 lg:py-5 lg:text-base"
    >
      <div class="relative flex justify-between">
        <div class="shrink">
          <slot v-if="$slots.default" name="default" />
          <template v-else>
            <div class="flex items-center gap-2">
              <VEntityLink
                v-if="entityType"
                :entityType="entityType"
                :entityId="entityId"
                :value="value"
                :showAvatar="shouldShowAvatar"
                :avatarSrc="avatarSrc"
                :avatarSize="avatarSize"
              />
              <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
            </div>
          </template>
        </div>
        <div
          v-if="value && allowCopy"
          class="flex items-top pl-1"
        >
          <ClipboardCopy :value="value" class="flex-none -mb-1" />
        </div>
      </div>
    </dd>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import ClipboardCopy from '@/components/ui/icons/ClipboardCopy.vue';
import BIconInfoCircle from '~icons/bi/info-circle';
import BIconPersonFill from '~icons/bi/person-fill';

const props = defineProps({
  label: {
    type: String,
    required: false,
  },
  value: {
    type: String,
    required: false,
  },
  entityType: {
    type: String,
    required: false,
  },
  entityId: {
    type: String,
    required: false,
  },
  allowCopy: {
    type: Boolean,
    default: false,
  },
  showAvatar: {
    type: Boolean,
    default: null,
  },
  avatarSrc: {
    type: String,
    default: '',
  },
  avatarSize: {
    type: String,
    default: 'sm',
  },
  help: {
    type: String,
    default: '',
  },
});

const entityTypeLower = computed(() => {
  return props.entityType?.toLowerCase();
});

// Computed property to return a relevant link for the entity.
const link = computed(() => {
  if (!props.entity) return null;
  if (entityTypeLower.value === 'user') {
    return `mailto:${props.value}`;
  }
  return null;
});

// Computed property to return a relevant icon for the contact method if any.
const icon = computed(() => {
  if (!props.entity) return null;
  
  if (entityTypeLower.value === 'user') {
    return BIconPersonFill;
  }
  return null;
});

// Decide whether we should show the avatar.
const shouldShowAvatar = computed(() => {
  // Check if the showAvatar prop is explicitly set.
  if (props.showAvatar) {
    return true;
  }
  // Check if showAvatar is the default (null) and the entity type is one of the supported types.
  return props.showAvatar === null && ['user', 'company', 'customer'].includes(entityTypeLower.value);
});
</script>
