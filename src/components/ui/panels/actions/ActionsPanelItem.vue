<template>
  <div
    :class="[
      'shadow group bg-platinum-50 p-3 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-300 dark:focus-within:ring-primary-600 dark:bg-midnight-500',
      borderRadiusClasses
    ]"
  >
    <div class="rounded p-3 w-full transition-colors group-hover:bg-primary-100 dark:group-hover:bg-midnight-300">
      <div class="relative flex justify-between">
        <div class="p-2">
          <span :class="[item.iconBackground, item.iconForeground, 'inline-flex rounded-lg p-3 ring-4 ring-primary-500']">
            <component :is="item.icon" class="h-8 w-8 p-1" aria-hidden="true" />
          </span>
        </div>
        
        <div class="ml-3 grow">
          <h3 class="text-md font-semibold text-primary-500 transition-colors group-hover:text-primary-600 dark:group-hover:text-primary-400">
            <span class="focus:outline-none">
              <span class="absolute inset-0" aria-hidden="true" />
              <div class="flex justify-between">
                <div>{{ item.name }}</div>
                <BIconArrowRight
                  v-if="showArrow"
                  class="w-3.5 my-auto -translate-x-3 text-primary-600 opacity-0 transition group-hover:translate-x-0 group-hover:opacity-100 dark:text-primary-400 absolute right-0"
                  aria-hidden="true"
                />
              </div>
            </span>
          </h3>
          <TransitionFade>
            <p
              :key="item.description"
              class="mt-1 text-sm text-platinum-950 dark:text-midnight-50"
            >{{ item.description }}</p>
          </TransitionFade>
        </div>
        <div class="flex-shrink-0 self-center z-10 p-2">
          <slot name="actions" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import BIconArrowRight from '~icons/bi/arrow-right';
import { computed } from 'vue';

type Item = {
  icon: string;
  name: string;
  iconForeground: string;
  iconBackground: string;
  description: string;
};

const props = defineProps<{
  item: Item;
  totalLength: number;
  index: number;
  showArrow?: boolean;
}>();

const borderRadiusClasses = computed(() => {
  const { index, totalLength } = props;
  const classes = [];
  
  // First element
  if (index === 0) {
    classes.push('rounded-tl-lg rounded-tr-lg sm:rounded-tr-none');
  }
  
  // Second element
  if (index === 1) {
    classes.push('sm:rounded-tr-lg');
  }
  
  // Second-to-last element
  if (index === totalLength - 2) {
    classes.push(totalLength % 2 === 1 ? 'sm:rounded-br-lg' : 'sm:rounded-bl-lg');
  }
  
  // Last element
  if (index === totalLength - 1) {
    classes.push(totalLength % 2 === 1 ? 'rounded-bl-lg rounded-br-lg' : 'rounded-bl-lg rounded-br-lg sm:rounded-bl-none');
  }
  
  return classes.join(' ');
});

</script>
