<template>
  <div class="overflow-hidden rounded-lg bg-platinum-50 shadow dark:bg-midnight-400">
    <TransitionFade>
      <div v-if="loading">
        <VSpinner size="lg" />
      </div>
      <dl
        v-else
        class="group/container grid divide-y divide-platinum-200 dark:divide-midnight-700"
        :class="{
          'multiple-columns grid-cols-1 md:divide-x xl:grid-cols-2': props.multipleColumns,
          '': !props.multipleColumns,
        }"
      >
        <slot />
      </dl>
    </TransitionFade>
  </div>
</template>

<script setup>
const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  multipleColumns: {
    type: Boolean,
    default: false,
  },
});
</script>
