<template>
  <div
    ref="headerRef"
    class="bg-primary-500"
    :class="showFixedHeader ? 'sticky top-0 w-full z-50 shadow-lg shadow-white/50 dark:shadow-midnight-900' : ''"
  >
    <VContainer :class="showFixedHeader && hasPageTopOffset ? 'space-y-1 lg:space-y-3' : ''">
      <div
        class="flex justify-between border-b pb-1 lg:pb-3"
        :class="showFixedHeader ? 'border-white/20 dark:border-midnight-500/25' : 'border-transparent'"
      >
        <div class="flex m-0">
          <TransitionFadeLeftToRight>
            <span v-if="showFixedHeader">
              <router-link :to="{ name: 'index' }">
                <Logo
                  customClasses="text-white dark:text-midnight-600 text-2xl transition-colors absolute"
                  wrapperClasses="mt-2"
                />
              </router-link>
            </span>
          </TransitionFadeLeftToRight>
          <VBreadcrumbs
            v-if="breadcrumbs && route?.meta?.breadcrumbs"
            class="flex sm:transition-transform"
            :class="[
              showFixedHeader ? 'translate-x-[22px] sm:translate-x-[128px]' : '',
              showTitle || showDescription ? 'pb-2 pt-5 sm:pt-4' : 'pt-3',
            ]"
            :breadcrumbs="route.meta.breadcrumbs"
            :showHome="!showFixedHeader"
          />
        </div>
        <TransitionFadeLeftToRight>
          <span v-if="hasPageTopOffset && showFixedHeader">
            <VTooltip content="Return to top of page" placement="left" class="h-full">
              <VButton class="mt-2 px-1.5 py-1" size="sm" @click="toTop()" intent="secondary">
                <BIconChevronBarUp class="inline text-lg" />
                <span class="sr-only">Return to top</span>
              </VButton>
            </VTooltip>
          </span>
        </TransitionFadeLeftToRight>
      </div>
      <div class="pb-3 sm:justify-between lg:flex" v-if="showTitle || showDescription">
        <slot v-if="$slots.title && showTitle" name="title" />
        <div v-else>
          <h1
            v-if="showTitle"
            class="flex items-center font-bold uppercase tracking-widest text-platinum-50 transition-all duration-75 dark:text-midnight-700 justify-center sm:justify-start"
            :class="showFixedHeader ? 'text-lg' : 'text-2xl'"
          >
            {{ route?.meta?.headerTitle || title }}
          </h1>
          <span
            v-if="showDescription && (route?.meta?.headerDescription || description)"
            class="flex items-center justify-center sm:justify-start font-semibold text-primary-200 transition-all duration-75 dark:text-midnight-500"
            :class="showFixedHeader ? 'text-sm' : ''"
          >
            <BIconCaretRightFill class="hidden sm:inline mr-2 w-2.5 text-primary-600" aria-hidden="true" />{{ route?.meta?.headerDescription || description }}
          </span>
        </div>
        <div v-if="$slots.default || $slots.actions || returnAction" class="flex justify-center sm:justify-start items-end mt-2 lg:mt-0 space-x-3">
          <slot />
          <slot name="actions" />
          <VButton
            v-if="returnActionEnabled && previousRoute"
            as="router-link"
            intent="secondary"
            centered
            :to="previousRoute ? { name: previousRoute.name } : { name: 'index' }"
          >
            <template #start>
              <BIconArrowReturnLeft />
            </template>
            Return to {{ previousRoute.meta.title }}
          </VButton>
        </div>
      </div>
    </VContainer>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router';
import BIconCaretRightFill from '~icons/bi/caret-right-fill';
import BIconChevronBarUp from '~icons/bi/chevron-bar-up';
import Logo from '@/components/ui/logos/Logo.vue';
import { computed, onMounted, ref } from 'vue';
import layoutStore from '@/store/layout';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';

const router = useRouter();

const props = defineProps({
  /**
   * The title of the page.
   * This is a default only, and will be overridden by the route meta headerTitle if it exists.
   */
  title: {
    type: String,
    default: '',
  },
  /**
   * The description of the page.
   * This is a default only, and will be overridden by the route meta headerDescription if it exists.
   */
  description: {
    type: String,
    default: null,
  },
  breadcrumbs: {
    type: Boolean,
    default: true,
  },
  showTitle: {
    type: Boolean,
    default: true,
  },
  showDescription: {
    type: Boolean,
    default: true,
  },
  returnAction: {
    type: Boolean,
    default: false,
  },
  returnActionExcludeNamedRoutes: {
    type: Array,
    default: () => [],
  },
});

function toTop() {
  document.getElementById('top').scrollIntoView(true);
}

const headerRef = ref(null);

const showFixedHeader = computed(() => layoutStore.getters.shouldShowFixedHeader());
const hasPageTopOffset = computed(() => layoutStore.getters.headerPageTopOffset() > 0);

document.body.addEventListener('scroll', () => {
  if (document.body.scrollTop !== layoutStore.getters.headerPageTopOffset()) {
    layoutStore.actions.updateHeaderScroll(document.body.scrollTop);
  }

  if (
    showFixedHeader.value
    && headerRef.value
    && headerRef.value?.offsetHeight
    && (
      document.body.scrollTop === 0
      || (layoutStore?.state?.header?.height !== headerRef.value?.offsetHeight)
    )
  ) {
    layoutStore.actions.setHeaderConfig({
      height: headerRef.value.offsetHeight
    });
  }
});

document.body.addEventListener('resize', () => {
  layoutStore.actions.updateHeaderWidth(window.innerWidth);
});

const route = useRoute();

onMounted(() => {
  // Set initial header height in the store
  if (headerRef.value?.offsetHeight) {
    layoutStore.actions.setHeaderConfig({
      height: headerRef.value.offsetHeight
    });
  }
});

/**
 * Checks both prop and route meta for return button being enabled.
 */
const returnActionEnabled = computed(() => {
  // Check the relevant prop.
  if (props.returnAction) return true;
  // Else, check the route props.
  if (route.meta.returnAction) return true;

  return false;
})

/**
 * Get the user's previous route using useRouter().
 * If the current route is single level, don't return it.
 * If the route's name is in the excluded list, don't return it.
 */
const previousRoute = computed(() => {
  const back = router.options.history.state.back;

  if (typeof back !== 'string') return null;

  if (router.currentRoute.value.path.split('/').length <= 2) return null;

  const resolved = router.resolve(back);

  if (props.returnActionExcludeNamedRoutes.includes(resolved.name)) {
    return null;
  }

  return resolved;
});

</script>
