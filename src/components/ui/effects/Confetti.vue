<template>
  <div></div>
</template>

<script setup>
import confetti from 'canvas-confetti';

const props = defineProps({
  colors: {
    type: Array,
    default: () => ['#8a1e1e', '#d9d029', '#2998d9']
  },
  duration: {
    type: Number,
    default: 3000
  },
  spread: {
    type: Number,
    default: 45
  },
  angle: {
    type: Number,
    default: 90
  },
  particleCount: {
    type: Number,
    default: 50
  },
  decay: {
    type: Number,
    default: 0.9
  },
  drift: {
    type: Number,
    default: 0
  },
  gravity: {
    type: Number,
    default: 1
  },
  flat: {
    type: Boolean,
    default: false
  },
  ticks: {
    type: Number,
    default: 200
  },
  originX: {
    type: Number,
    default: 0.5
  },
  originY: {
    type: Number,
    default: 0.5
  },
  shapes: {
    type: Array,
    default: () => ['square', 'circle', 'star']
  },
  scalar: {
    type: Number,
    default: 1
  },
  zIndex: {
    type: Number,
    default: 100
  },
  disableForReducedMotion: {
    type: <PERSON>olean,
    default: true
  }
});

const emit = defineEmits(['fired', 'complete']);

function fire() {
  const confettiOptions = {
    particleCount: props.particleCount,
    spread: props.spread,
    angle: props.angle,
    decay: props.decay,
    drift: props.drift,
    gravity: props.gravity,
    flat: props.flat,
    ticks: props.ticks,
    origin: {
      x: props.originX,
      y: props.originY
    },
    shapes: props.shapes,
    scalar: props.scalar,
    zIndex: props.zIndex,
    colors: props.colors,
    disableForReducedMotion: props.disableForReducedMotion
  };

  confetti(confettiOptions);

  emit('fired');

  setTimeout(() => {
    emit('complete');
  }, props.duration);
}

/**
 * This will override many of the default confetti options.
 */
function fireRealistic() {
  const count = 200;
  const defaults = {
    origin: { y: 0.7 },
    colors: props.colors,
    zIndex: props.zIndex,
    disableForReducedMotion: props.disableForReducedMotion
  };

  function fireSequence(particleRatio, opts) {
    confetti({
      ...defaults,
      ...opts,
      particleCount: Math.floor(count * particleRatio)
    });
  }

  fireSequence(0.25, {
    spread: 26,
    startVelocity: 55,
  });
  fireSequence(0.2, {
    spread: 60,
  });
  fireSequence(0.35, {
    spread: 100,
    decay: 0.91,
    scalar: 0.8
  });
  fireSequence(0.1, {
    spread: 120,
    startVelocity: 25,
    decay: 0.92,
    scalar: 1.2
  });
  fireSequence(0.1, {
    spread: 120,
    startVelocity: 45,
  });

  emit('fired');

  setTimeout(() => {
    emit('complete');
  }, props.duration);
}

// Expose methods to parent component
defineExpose({
  fire,
  fireRealistic
});
</script>
