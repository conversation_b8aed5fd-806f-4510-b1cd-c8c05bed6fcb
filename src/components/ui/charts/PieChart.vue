<template>
  <div class="chart-container">
    <canvas ref="chartCanvas"></canvas>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { Chart, registerables } from 'chart.js';
import { generateColors, getCurrentColors } from '@util/colorUtils'; // Adjust the path as necessary

const props = defineProps({
  chartData: {
    type: Object,
    required: true,
  },
  legend: {
    type: Boolean,
    default: true,
  },
  borderColor: {
    type: String,
    default: null,
  },
});

// Register all necessary components, including the chart controller
Chart.register(...registerables);

const chartInstance = ref(null);
const chartCanvas = ref(null);

const primaryColors = getCurrentColors();
const chartColors = ref(
  generateColors(primaryColors[300], primaryColors[700], props.chartData.datasets[0].data.length)
);

onMounted(() => {
  chartInstance.value = new Chart(chartCanvas.value, {
    type: 'doughnut',
    data: {
      ...props.chartData,
      datasets: [
        {
          ...props.chartData.datasets[0],
          backgroundColor: chartColors.value,
          hoverBackgroundColor: primaryColors[500],
          borderWidth: 5,
        },
      ],
    },
    options: {
      responsive: true,
      animation: {
        animateScale: true,
        duration: 1000,
      },
      plugins: {
        tooltip: {
          enabled: true,
          caretSize: 0,
          backgroundColor: '#1B1C20',
          displayColors: false,
          overflow: 'visible',
        },
        legend: {
          display: props.legend,
        },
      },
      layout: {
        padding: {
          top: 30,
          bottom: 30,
          left: 30,
          right: 30,
        },
      },
      elements: {
        arc: {
          borderColor: props.borderColor ? props.borderColor : 'black',
          borderWidth: 5,
          backgroundColor: 'transparent',
        },
      },
    },
  });
});

watch(
  () => props.chartData,
  (newData) => {
    chartInstance.value.data = newData;
    chartInstance.value.update();
  }
);
</script>

<style scoped>
  .chart-container {
    width: 250px;
    height: 250px;
  }
</style>