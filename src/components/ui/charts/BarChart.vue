<template>
  <div>
    <canvas ref="chartCanvas"></canvas>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { Chart, BarElement, CategoryScale, LinearScale, Tooltip, Legend } from 'chart.js'; // Import necessary components

// Register components
Chart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);

// Define the props
const props = defineProps({
  chartData: {
    type: Object,
    required: true,
  },
});

const chartInstance = ref(null);
const chartCanvas = ref(null);

onMounted(() => {
  // Create a new Chart instance
  chartInstance.value = new Chart(chartCanvas.value, {
    type: 'bar', // Specify the type of chart
    data: props.chartData,
    options: {
      responsive: true,
      animation: {
        animateScale: true,
        duration: 1000,
      },
    },
  });
});

// Watch for changes in chartData to update the chart
watch(
  () => props.chartData,
  (newData) => {
    if (chartInstance.value) {
      chartInstance.value.data = newData; // Update the chart data
      chartInstance.value.update(); // Refresh the chart
    }
  }
);
</script>

<style scoped>
canvas {
  max-width: 100%;
}
</style>
