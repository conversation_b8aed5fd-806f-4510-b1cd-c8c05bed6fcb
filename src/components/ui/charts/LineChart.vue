<template>
  <div class="chart-container">
    <canvas ref="lineChart"></canvas>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { Chart, LineElement, PointElement, LinearScale, Tooltip, Filler } from 'chart.js';

// Register the required components
Chart.register(LineElement, PointElement, LinearScale, Tooltip, Filler);

// Define props
const props = defineProps({
  chartData: {
    type: Array,
    required: true,
  },
  chartColor: {
    type: String,
    default: '#42a5f5',
  },
  pointColor: {
    type: String,
    default: 'black',
  },
});

const lineChart = ref(null);
let chartInstance = null;

const data = {
  labels: Array.from({ length: props.chartData.length }, (_, i) => `Month ${i + 1}`),
  datasets: [
    {
      label: 'Data Series',
      data: props.chartData,
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      fill: true,
      pointBackgroundColor: props.pointColor,
      pointRadius: 5,
    },
  ],
};

const options = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    x: {
      grid: {
        color: 'rgba(100,100,100, 0.1)',
      },
      border: {
        color: 'transparent',
      },
      ticks: {
        color: 'rgba(100,100,100, 0.3)',
      },
    },
    y: {
      grid: {
        color: 'rgba(100,100,100, 0.1)',
      },
      border: {
        color: 'transparent',
      },
      ticks: {
        color: 'rgba(100,100,100, 0.3)',
      },
    },
  },
  animations: {
    tension: {
      duration: 1000,
      easing: 'easeOutQuart',
      from: 1,
      to: 0.4,
      loop: false,
    },
  },
  plugins: {
    legend: {
      display: false,
    },
    tooltip: {
      displayColors: false,
      callbacks: {
        label: (tooltipItem) => `${tooltipItem.label}: ${tooltipItem.raw}`,
      },
    },
  },
};

// Create gradient fill for the area
const createGradient = () => {
  const canvas = lineChart.value;
  if (!canvas) return; // Ensure canvas is available
  const ctx = canvas.getContext('2d');
  const gradient = ctx.createLinearGradient(0, 0, 0, 700); // Adjust height as needed

  // Use rgba to include the alpha value
  const colorWithAlpha = props.chartColor; // Change to 50% opacity
  gradient.addColorStop(0, colorWithAlpha); // Start with 50% opacity
  gradient.addColorStop(1, 'rgba(255, 255, 255, 0)'); // Fully transparent
  return gradient;
};

onMounted(() => {
  // Set the background color using the gradient
  data.datasets[0].backgroundColor = createGradient();

  chartInstance = new Chart(lineChart.value, {
    type: 'line',
    data: data,
    options: options,
  });
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.destroy();
  }
});
</script>

<style>
.chart-container {
  width: 100%; /* Make the container full width */
  max-width: 100%; /* Prevent the container from exceeding the full width */
  margin: 0 auto; /* Center the chart container */
  position: relative; /* Position context for responsive sizing */
  height: 300px; /* Set a fixed height to ensure visibility */
}

canvas {
  width: 100% !important; /* Ensure the canvas takes full width */
  height: auto !important; /* Maintain aspect ratio */
}
</style>
