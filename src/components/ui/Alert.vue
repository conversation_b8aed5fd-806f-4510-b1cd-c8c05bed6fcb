<template>
  <div
    class="mb-4 flex items-center gap-3 rounded-md px-5 py-4"
    :class="
      classes.wrapper({
        intent,
      })
    "
    role="alert"
  >
    <component v-if="icon" :is="icon" class="h-4 w-4 flex-shrink-0" />
    <span v-if="icon" class="sr-only">Info</span>

    <div class="text-sm">
      <slot />
    </div>
    <button
      v-if="dismissable"
      :class="
        classes.close({
          intent,
        })
      "
      type="button"
      class="-mx-1.5 -my-1.5 ml-auto inline-flex h-8 w-8 items-center justify-center rounded-lg p-1.5 focus:ring-2"
      aria-label="Close"
    >
      <span class="sr-only">Close</span>
      <BiXLg class="h-4 w-4" />
    </button>
  </div>
</template>

<script lang="ts" setup>
import BiXLg from '~icons/bi/x-lg';
import BiInfoCircleFill from '~icons/bi/info-circle-fill';
import BiExclamationCircleFill from '~icons/bi/exclamation-circle-fill';
import BiCheckCircleFill from '~icons/bi/check-circle-fill';

import { cva, VariantProps } from 'class-variance-authority';
import { computed } from 'vue';

const classes = {
  wrapper: cva('', {
    variants: {
      intent: {
        success:
          'text-success-600 dark:text-success-300 bg-success-50 dark:bg-success-900 border border-success-400 dark:border-success-600',
        info: 'text-info-600 dark:text-info-300 bg-info-50 dark:bg-info-900 border border-info-400 dark:border-info-600',
        warning:
          'text-warning-600 dark:text-warning-300 bg-warning-50 dark:bg-warning-900 border border-warning-400 dark:border-warning-600',
        danger:
          'text-danger-500 dark:text-danger-200 bg-danger-50 dark:bg-danger-700 border border-danger-300 dark:border-danger-600',
      },
    },
  }),
  close: cva('', {
    variants: {
      intent: {
        success: 'focus:ring-success-400 bg-success-50 text-success-500 hover:bg-success-200',
        info: 'focus:ring-info-400 bg-info-50 text-info-500 hover:bg-info-200',
        warning: 'focus:ring-warning-400 bg-warning-50 text-warning-500 hover:bg-warning-200',
        danger: 'focus:ring-danger-400 bg-danger-50 text-danger-500 hover:bg-danger-200',
      },
    },
  }),
};

type WrapperVariant = VariantProps<typeof classes.wrapper>;

const props = withDefaults(
  defineProps<{
    intent?: WrapperVariant['intent'];
    dismissable?: boolean;
    icon?: boolean;
  }>(),
  {
    intent: 'info',
    dismissable: false,
    icon: false,
  }
);

const icon = computed(() =>
  props.icon
    ? {
        success: BiCheckCircleFill,
        info: BiInfoCircleFill,
        warning: BiExclamationCircleFill,
        danger: BiExclamationCircleFill,
      }[props.intent]
    : null
);
</script>
