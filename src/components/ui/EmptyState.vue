<template>
  <div
    class="flex flex-col items-center justify-center h-full p-6"
  >
    <div
      :class="{
        'space-y-2': size === 'md' || size === 'lg',
      }"
    >
      <h3
        :class="{
          'text-2xl': size === 'xs',
          'text-3xl': size === 'sm',
          'text-4xl': size === 'md',
          'text-5xl': size === 'lg',
        }"
        class="font-bold leading-8 text-primary-500"
      >{{ title }}</h3>
      <div class="text-center space-y-1">
        <p 
          :class="{
            'text-md': size === 'xs',
            'text-lg': size === 'sm',
            'text-xl': size === 'md',
            'text-2xl': size === 'lg',
          }"
          class="text-platinum-950 dark:text-midnight-50 font-semibold"
        >{{ description }}</p>
        <p
          v-if="contact"
          :class="{
            'text-xs': size === 'xs',
            'text-sm': size === 'sm',
            'text-md': size === 'md',
            'text-lg': size === 'lg',
          }"
          class="text-platinum-950 dark:text-midnight-50"
        >
          <TransitionFade>
            <span v-if="showContactLink">Think this an error? <TextLink :to="{ name: 'support.contact', query: { topic: 'technical-support-and-bugs' } }">Let us know!</TextLink></span>
          </TransitionFade>
        </p>
      </div>
    </div>
    <VButton
      v-if="props.return && previousRoute"
      as="router-link"
      intent="primary"
      class="mt-4"
      :to="previousRoute ? { name: previousRoute.name } : { name: 'index' }"
    >
      <template #start>
        <BIconArrowReturnLeft />
      </template>
      Go Back
    </VButton>
  </div>
</template>

<script setup>

import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import BIconArrowReturnLeft from '~icons/bi/arrow-return-left';

const props = defineProps({
  title: {
    type: String,
    default: 'Hmm...',
  },
  description: {
    type: String,
    default: 'We couldn\'t find what you were looking for.',
  },
  size: {
    type: String,
    default: 'md',
  },
  contact: {
    type: Boolean,
    default: true,
  },
  return: {
    type: Boolean,
    default: true,
  }
});

const router = useRouter();

const previousRoute = typeof router.options.history.state.back === 'string'
  ? router.resolve(router.options.history.state.back)
  : null;

const showContactLink = ref(false);

// Settimout to show the contact link
if (props.contact) {
  setTimeout(() => {
    showContactLink.value = true;
  }, 3000);
}

</script>
