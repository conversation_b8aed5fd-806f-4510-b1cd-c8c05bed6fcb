<template>
  <TooltipProvider
    :delayDuration="delayValue"
    :disabled="disabled"
  >
    <Tooltip :size="size">
      <TooltipTrigger type="button" :as-child="triggerAsChild" :class="triggerClasses">
        <slot />
      </TooltipTrigger>
      <TooltipContent :align="align" :side="side" :as-child="contentAsChild">
        <slot name="content" v-if="$slots.content" />
        <p
          v-else-if="content"
          class="text-platinum-950 dark:text-midnight-50 font-semibold"
          :class="textSizeClass"
        >{{ content }}</p>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/shadcn/tooltip'

const props = defineProps({
  delay: {
    type: [Number, String],
    required: false,
    default: 300,
  },
  triggerAsChild: {
    type: Boolean,
    required: false,
    default: false,
  },
  disabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  align: {
    type: String,
    required: false,
    default: 'center',
  },
  side: {
    type: String,
    required: false,
    default: 'top',
  },
  contentAsChild: {
    type: Boolean,
    required: false,
    default: false,
  },
  content: {
    type: String,
    required: false,
    default: null,
  },
  triggerClasses: {
    type: String,
    required: false,
    default: '',
  },
  size: {
    type: String,
    required: false,
    default: 'base',
    validator: (value: string) => ['sm', 'base', 'lg'].includes(value),
  },
})

const delayValue = computed(() => {
  return props.delay ? Number(props.delay) : 0
});

const textSizeClass = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'text-xs';
    case 'lg':
      return 'text-base';
    default:
      return 'text-sm';
  }
});
</script>