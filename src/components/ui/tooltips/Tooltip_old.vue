<template>
  <Dropdown
    class="inline-block"
    :triggers="triggers"
    :distance="distance"
    :skidding="skidding"
    :placement="placement"
    :boundary="boundary"
    :prevent-overflow="preventOverflow"
    :handle-resize="true"
    :theme="theme"
  >
    <span>
      <slot />
    </span>
    <template #popper>
      <slot v-if="$slots.content" name="content"></slot>
      <template v-else>{{ content }}</template>
    </template>
  </Dropdown>
</template>

<script setup>
import { Dropdown } from 'floating-vue';

const props = defineProps({
  content: {
    type: String,
    required: false,
  },
  distance: {
    type: Number,
    default: 10,
  },
  skidding: {
    type: Number,
    default: 0,
  },
  triggers: {
    type: Array,
    default: ['hover'],
  },
  placement: {
    type: String,
    default: 'top',
  },
  boundary: {
    type: String,
    default: 'window',
  },
  preventOverflow: {
    type: Boolean,
    default: true,
  },
  theme: {
    type: String,
    default: 'tooltip',
  },
});
</script>

<script>
import { PopperWrapper } from 'floating-vue';

export default {
  ...PopperWrapper,
};
</script>
