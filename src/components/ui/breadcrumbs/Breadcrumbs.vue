<template>
  <nav class="flex" aria-label="Breadcrumb">
    <ol role="list" class="flex items-center space-x-0.5">
      <li v-if="showHome">
        <div>
          <RouterLink
            to="/"
            class="text-primary-300 transition-colors hover:text-primary-100 dark:text-midnight-500/50 dark:hover:text-midnight-500"
          >
            <BiHouseDoorFill class="h-4 w-4 flex-shrink-0 -mt-0.5" aria-hidden="true" />
            <span class="sr-only">Home</span>
          </RouterLink>
        </div>
      </li>
      <li v-for="breadcrumb in breadcrumbs" :key="breadcrumb.index">
        <div class="flex items-center">
          <svg
            class="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0 text-primary-600"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
          </svg>
          <RouterLink
            :to="{ name: breadcrumb.name }"
            class="ml-0.5 text-xs sm:text-sm font-semibold text-primary-300 transition-colors hover:text-primary-100 dark:text-midnight-500/50 dark:hover:text-midnight-500"
          >
            {{ breadcrumb.label }}
          </RouterLink>
        </div>
      </li>
    </ol>
  </nav>
</template>

<script setup>
import BiHouseDoorFill from '~icons/bi/house-door-fill';

defineProps({
  breadcrumbs: {
    type: Array,
    required: true,
  },
  showHome: {
    type: Boolean,
    default: true,
  },
});
</script>
