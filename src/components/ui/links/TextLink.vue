<template>
  <component
    v-if="typeof to === 'string' || linkType === 'button'"
    :is="linkType"
    v-bind="linkAttrs"
    @click="handleClick"
    class="group inline items-center"
    :class="[
      containerClasses({
        intent,
      }),
    ]"
  >
    <slot v-if="$slots.default" />
    <span v-else-if="value">{{ value }}</span>
    <BIconLink45deg
      v-if="!disableIcon"
      class="inline -translate-x-3 text-platinum-600 opacity-0 transition group-hover:translate-x-0 group-hover:opacity-100 dark:text-midnight-200 mt-1"
    />
  </component>

  <router-link
    v-else
    :to="!isExternal && typeof to === 'object' ? to : undefined"
    v-bind="
      isExternal || typeof to === 'string'
        ? {
            href: to,
            target: isExternal ? '_blank' : undefined,
            rel: 'noopener noreferrer',
          }
        : {}
    "
    @click="handleClick"
    class="group inline items-center"
    :class="[
      containerClasses({
        intent,
      }),
    ]"
  >
    <slot v-if="$slots.default" />
    <span v-else-if="value">{{ value }}</span>
    <BIconLink45deg
      v-if="!disableIcon"
      class="inline -translate-x-3 text-platinum-600 opacity-0 transition group-hover:translate-x-0 group-hover:opacity-100 dark:text-midnight-200 mt-1"
      :class="iconClasses"
    />
  </router-link>
</template>

<script setup lang="ts">
import { computed, inject } from 'vue';
import BIconLink45deg from '~icons/bi/link-45deg';
import { cva } from 'class-variance-authority';

const globalConfirm = inject('globalConfirm');

const props = defineProps({
  to: {
    type: [String, Object],
  },
  external: {
    type: Boolean,
    default: false,
  },
  disableIcon: {
    type: Boolean,
    default: false,
  },
  iconClasses: {
    type: String,
    default: '',
  },
  value: {
    type: String,
    default: null,
  },
  linkType: {
    type: String,
    default: 'a',
  },
  intent: {
    type: String,
    default: 'primary',
    validator: (value) => {
      return ['primary', 'secondary'].includes(value);
    },
  },
});

const containerClasses = cva('', {
  variants: {
    intent: {
      primary: 'text-primary-500 transition hover:text-primary-600 dark:text-primary-500 dark:hover:text-primary-400',
      secondary: 'text-platinum-100 transition hover:text-platinum-400 dark:text-midnight-800 dark:hover:text-midnight-50',
    },
  },
});

const isExternal = computed(() => {
  return props.external || (typeof props.to === 'string' && props.to.startsWith('http'));
});

// If is external and does not include http, append it.
const externalLink = computed(() => {
  if (isExternal.value && !props.to.startsWith('http')) {
    return `http://${props.to}`;
  }
  return props.to;
});

const handleClick = async (event) => {
  if (isExternal.value) {
    event.preventDefault();
    const confirmed = await globalConfirm.value.showConfirm(
      'External Link Warning',
      '<p>You are about to navigate away to an unverified external resource.</p><p>Please confirm your action.</p>'
    );
    if (confirmed) {
      window.open(externalLink.value, '_blank', 'noopener,noreferrer');
    }
  }
};

const linkAttrs = computed(() => {
  const attrs = {};
  if (isExternal.value) {
    attrs.target = '_blank';
    attrs.rel = 'noopener noreferrer';
  }
  if (externalLink.value) {
    attrs.href = externalLink.value;
  }
  return attrs;
});

</script>
