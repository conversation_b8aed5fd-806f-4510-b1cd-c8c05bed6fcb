<template>
  <VTooltip 
    v-if="link && value"
    class="inline-block"
    contentAsChild
  >
    <TextLink
      :to="link"
      class="inline-flex items-center"
      :class="showLinkIcon ? '-mr-2' : ''"
      :disable-icon="!showLinkIcon"
      :icon-classes="showEntityType ? 'mt-[1.125rem]' : ''"
    >
      <Avatar
        v-if="showAvatar && isAvatarable"
        :id="entityId"
        :size="avatarSize"
        :name="value"
        :src="avatarSrc"
        class="mr-2"
      />
      <div>
        <div v-if="showEntityType" class="text-start leading-none -mb-1">
          <span class="text-xxs text-platinum-600 dark:text-midnight-200 font-semibold">{{ entityType }}</span>
        </div>
        <div v-if="!showAvatar && showIcon" class="mr-1.5 inline">
          <BIconPersonFill v-if="entityTypeLower === 'user'" class="-mt-1 inline" />
          <BIconBuildingFill v-else-if="entityTypeLower === 'company'" class="-mt-1 inline" />
          <BIconPersonWorkspace v-else-if="entityTypeLower === 'customer'" class="-mt-1 inline" />
          <BIconLink45deg v-else class="-mt-1 inline" />
        </div>
        <slot v-if="$slots.default" :class="[valueClass, textClass]"></slot>
        <span v-else :class="[valueClass, textClass]">{{ displayValue }}</span>
      </div>
    </TextLink>
    <template #content>
      <UserInformationPanel v-if="entityTypeLower === 'user'" :id="entityId" />
      <CompanyInformationPanel v-if="entityTypeLower === 'company'" :id="entityId" />
      <CustomerInformationPanel v-if="entityTypeLower === 'customer'" :id="entityId" />
      <CountryInformationPanel v-if="entityTypeLower === 'country'" :id="entityId" />
      <CurrencyInformationPanel v-if="entityTypeLower === 'currency'" :id="entityId" />
      <AddressInformationPanel v-if="entityTypeLower === 'address'" :id="entityId" />
      <AnnouncementInformationPanel v-if="entityTypeLower === 'announcement'" :id="entityId" />
    </template>
  </VTooltip>
  <span v-else-if="value" :class="textClass">{{ displayValue }}</span>
  <span v-else class="italic text-platinum-600 dark:text-midnight-200" :class="textClass">{{ undefinedText }}</span>
</template>


<script setup>
import { computed } from 'vue';
import { useAuth } from '@/store';
import BIconPersonFill from '~icons/bi/person-fill';
import BIconBuildingFill from '~icons/bi/building-fill';
import BIconPersonWorkspace from '~icons/bi/person-workspace';
import BIconLink45deg from '~icons/bi/link-45deg';
import UserInformationPanel from '@/modules/users/components/UserInformationPanel.vue';
import CompanyInformationPanel from '@/modules/companies/components/CompanyInformationPanel.vue';
import CountryInformationPanel from '@/modules/geo/components/CountryInformationPanel.vue';
import CurrencyInformationPanel from '@/modules/currencies/components/CurrencyInformationPanel.vue';
import CustomerInformationPanel from '@/modules/customers/components/CustomerInformationPanel.vue';
import AddressInformationPanel from '@/modules/addresses/components/AddressInformationPanel.vue';
import AnnouncementInformationPanel from '@/modules/announcements/components/AnnouncementInformationPanel.vue';
const props = defineProps({
  entityType: {
    type: String,
  },
  entityId: {
    type: [String, Number, null],
  },
  // The display value (such as a user's full name or a company name).
  value: {
    type: [String, Number],
    default: '',
  },
  showIcon: {
    type: Boolean,
    default: true,
  },
  showLinkIcon: {
    type: Boolean,
    default: true,
  },
  valueClass: {
    type: String,
    default: '',
  },
  undefinedText: {
    type: String,
    default: 'Undefined',
  },
  showAvatar: {
    type: Boolean,
    default: false,
  },
  avatarSrc: {
    type: String,
    default: '',
  },
  avatarSize: {
    type: String,
    default: 'base',
  },
  textClass: {
    type: String,
    default: '',
  },
  showEntityType: {
    type: Boolean,
    default: false,
  },
  valueLimit: {
    type: [Number, Boolean],
    default: 50,
  },
});

const entityTypeLower = computed(() => {
  return props.entityType?.toLowerCase();
});

const auth = useAuth();

const link = computed(() => {
  if (!props.entityId) {
    return null;
  }

  // User
  if (entityTypeLower.value === 'user') {
    if (props.entityId === auth.user.locked_id) {
      if (auth.can('users.profile.self')) {
        return { name: 'profile.view' };
      }
    } else {
      if (auth.can('users.profile') || auth.can('users.profile.any')) {
        return { name: 'users.profile', params: { id: props.entityId } };
      }
    }
  }

  // Company
  if (entityTypeLower.value === 'company') {
    if (auth.can('companies.view') || auth.can('companies.view.any')) {
      return { name: 'companies.profile', params: { id: props.entityId } };
    }
  }

  // Customer
  if (entityTypeLower.value === 'customer') {
    if (auth.can('customers.view') || auth.can('customers.view.any')) {
      // TODO, switch to customer.profile once ready.
      return { name: 'customers.view', params: { id: props.entityId } };
    }
  }

  // Country
  if (entityTypeLower.value === 'country') {
    if (auth.can('countries.view') || auth.can('countries.view.any')) {
      return { name: 'settings.countries.view', params: { id: props.entityId } };
    }
  }

  // Currency
  if (entityTypeLower.value === 'currency') {
    if (auth.can('currencies.view')) {
      return { name: 'settings.currencies.view', params: { id: props.entityId } };
    }
  }

  // Address
  if (entityTypeLower.value === 'address') {
    if (auth.can('addresses.view') || auth.can('addresses.view.any') || auth.can('addresses.view.self')) {
      return { name: 'addresses.view', params: { id: props.entityId } };
    }
  }

  // Announcement
  if (entityTypeLower.value === 'announcement') {
    if (auth.can('announcements.view') || auth.can('announcements.view.any')) {
      return { name: 'announcements.show', params: { id: props.entityId } };
    }
  }

  return null;
});

/**
 * Whether the entity type is appropriate to show an avatar for.
 */
const isAvatarable = computed(() => {
  if (!entityTypeLower.value) {
    return false;
  }
  const applicableEntityTypes = ['user', 'company', 'customer'];
  return applicableEntityTypes.includes(entityTypeLower.value);
});

/**
 * The value we're going to display. Truncated based on prop.
 */
const displayValue = computed(() => {
  if (!props.value) {
    return '';
  }
  if (props.valueLimit === false) {
    return props.value;
  }
  if (props.value.length <= props.valueLimit) {
    return props.value;
  }
  return props.value.slice(0, props.valueLimit) + '...';
});

</script>