<script setup>
import { computed } from 'vue';
import BIconEnvelopeFill from '~icons/bi/envelope-fill';
import BIconTelephoneFill from '~icons/bi/telephone-fill';
import BIconPrinterFill from '~icons/bi/printer-fill';

const props = defineProps({
  value: {
    type: String,
    required: true,
  },
  method: {
    type: String,
    required: true,
  },
  showIcon: {
    type: Boolean,
    default: false,
  },
  textLinkIcon: {
    type: Boolean,
    default: true,
  },
});

// Computed property to return a relevant link for the contact method if any.
const link = computed(() => {
  if (!props.method) return null;
  const methodLower = props.method.toLowerCase();
  if (methodLower.includes('email')) {
    return `mailto:${props.value}`;
  } else if (methodLower.includes('phone')) {
    return `tel:${props.value}`;
  } else if (methodLower.includes('fax')) {
    return `fax:${props.value}`;
  }
  return null;
});

// Computed property to return a relevant icon for the contact method if any.
const icon = computed(() => {
  if (!props.method) return null;
  const methodLower = props.method.toLowerCase();
  if (methodLower.includes('email')) {
    return BIconEnvelopeFill;
  } else if (methodLower.includes('phone')) {
    return BIconTelephoneFill;
  } else if (methodLower.includes('fax')) {
    return BIconPrinterFill;
  }
  return null;
});

</script>

<template>
  <VTextLink v-if="link" :to="link" class="text-primary-500" :disableIcon="!textLinkIcon">
    <component :is="icon" v-if="icon && showIcon" class="-mt-0.5 mr-0.5 inline w-4" />
    {{ value }}
  </VTextLink>
  <span v-else-if="value">
    <component :is="icon" v-if="icon && showIcon" class="-mt-0.5 mr-0.5 inline w-4" />
    {{ value }}
  </span>
  <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
</template>
