<template>
  <a
    href="#"
    class="relative inline-flex h-[42px] items-center border px-4 text-sm font-medium sm:px-1.5 md:px-2.5 lg:px-4"
    :class="classes"
    @click.prevent="page.click"
  >
    <slot>{{ page.number }}</slot>
  </a>
</template>

<script>
export default {
  name: 'PageButton',
  props: {
    page: {
      type: Object,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    isDisabled() {
      return this.disabled || this.page.disabled;
    },
    isCurrent() {
      return this.page.isCurrent;
    },
    classes() {
      return [
        this.isCurrent && !this.isDisabled
          ? 'z-10 bg-primary-500 border-transparent text-platinum-50 dark:text-midnight-700'
          : 'bg-platinum-200 dark:bg-midnight-500 border-transparent text-primary-500 dark:text-midnight-50',
        !this.isCurrent && !this.isDisabled
          ? 'hover:bg-primary-400 dark:hover:bg-midnight-900 text-primary-500 hover:text-platinum-50 dark:text-midnight-50 hover:dark:text-primary-500'
          : '',
        this.isDisabled ? 'cursor-not-allowed' : '',
      ];
    },
  },
};
</script>
