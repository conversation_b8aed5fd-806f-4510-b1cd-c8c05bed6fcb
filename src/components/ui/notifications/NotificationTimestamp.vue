<template>
  <p class="text-md inline font-semibold text-primary-500 -mt-1" v-if="datetime">
    <VTooltip :content="formattedDate(datetime)">
      <span class="text-xxs block cursor-pointer transition-colors !text-platinum-600 dark:!text-midnight-200/75 hover:!text-platinum-900 dark:hover:!text-midnight-100">
        {{ timeAgo(datetime) }}
      </span>
    </VTooltip>
  </p>
</template>

<script setup>
import { timeAgo, formattedDate } from '@/util/dateUtils';

const props = defineProps({
  datetime: {
    type: String,
    required: true,
  },
});
</script>
