<template>
  <div class="w-full px-3 py-2">
    <div class="flex justify-between items-center w-full">
      <slot name="header" />
    </div>
    <div class="w-full flex justify-between items-center">
      <slot name="description" />
      <div class="h-full pl-2 flex items-center opacity-50 hover:opacity-100 transition-opacity">
        <VTooltip content="Mark as read" side="left">
          <VCheckbox 
            size="sm" 
            :model-value="isRead" 
            @update:model-value="$emit('update:isRead', $event)"
            :checked="isRead"
          />
        </VTooltip>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  isRead: {
    type: Boolean,
    default: false,
  },
  notificationId: {
    type: [String, Number],
    required: false,
  },
});

const emit = defineEmits(['update:isRead', 'mark-as-read']);
</script>
