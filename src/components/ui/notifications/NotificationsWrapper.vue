<template>
  <div class="p-2 h-full relative">
    <div
      aria-live="assertive"
      class="inset-0 h-full items-end overflow-y-auto pb-2 p-px pr-2 scrollbar-thin sm:items-start"
    >
      <transition-group
        name="notification-move"
        tag="div"
        class="flex w-full flex-col items-center space-y-2 sm:items-end"
      >
        <div
          v-for="(notification, index) in sortedNotifications"
          :key="notification.id"
          class="w-full flex-shrink-0"
        >
          <transition-fade>
            <NotificationMarkReadButton
              v-if="((index === firstReadIndex && hasUnread) || (!hasRead && hasUnread && index === notifications.length))"
              class="mb-6"
            />
          </transition-fade>

          <div
            v-if="index === firstReadIndex && hasRead"
            class="pb-3 w-full mt-2"
          >
            <div class="relative w-full block pointer-events-none">
              <div class="flex items-center justify-between w-full border-b offset-2 border-platinum-300 dark:border-midnight-400"></div>
              <span
                class="text-xxs text-platinum-800 dark:text-midnight-200 font-bold uppercase px-2 bg-platinum-100 dark:bg-midnight-600 -mt-px absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
              >Seen Notifications</span>
            </div>
          </div>

          <NotificationDrawerItem
            :notification="notification"
            :read="!!notification.read_at"
          />
          
          <transition-fade>
            <NotificationMarkReadButton
              v-if="index === notifications.length - 1 && !hasRead && hasUnread"
            />
          </transition-fade>
        </div>
      </transition-group>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import NotificationDrawerItem from '@/components/layout/drawers/notification-drawer/NotificationDrawerItem.vue';
import NotificationMarkReadButton from '@/components/ui/notifications/NotificationMarkReadButton.vue';

const props = defineProps({
  notifications: {
    type: Array,
    required: true,
  },
});

/**
 * Sorted notifications.
 * - Unread notifications first (created date, newest first)
 * - Read notifications second (read date, newest first)
 */
const sortedNotifications = computed(() => {
  const unread = props.notifications
    .filter(n => !n.read_at)
    .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
  const read = props.notifications
    .filter(n => !!n.read_at)
    .sort((a, b) => new Date(b.read_at) - new Date(a.read_at));
  return [...unread, ...read];
});

const hasUnread = computed(() => props.notifications.some(n => !n.read_at));
const hasRead = computed(() => props.notifications.some(n => n.read_at));

const firstReadIndex = computed(() => {
  return props.notifications.findIndex(n => n.read_at);
});

</script>

<style scoped>
.notification-move-enter-active,
.notification-move-leave-active,
.notification-move-move {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-move-enter-from,
.notification-move-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
