<template>
  <div
    class="w-full py-4 text-center"
    >
    <VButton
        class="z-10"
        intent="primary"
        size="xs"
        outline
        @click="notificationsStore.markAllAsRead()"
    >Mark all as read
      <template #start>
        <BIconCheckLg />
      </template>
    </VButton>
    </div>
</template>

<script setup>
import { useNotifications } from '@/store/notifications';
import BIconCheckLg from '~icons/bi/check-lg';

const notificationsStore = useNotifications();

</script>

  