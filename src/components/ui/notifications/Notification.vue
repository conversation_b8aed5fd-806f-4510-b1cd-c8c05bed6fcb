<template>
  <transition 
    enter-active-class="transform ease-out duration-300 transition"
    enter-from-class="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
    enter-to-class="translate-y-0 opacity-100 sm:translate-x-0" 
    leave-active-class="transition ease-in-out duration-200"
    leave-from-class="opacity-100" 
    leave-to-class="opacity-0 !h-0 -translate-x-10">
    <component
      :is="to ? 'router-link' : 'div'"
      ref="notification"
      :to="to"
      class="pointer-events-auto relative flex flex-col w-full rounded-md bg-platinum-50 shadow-md ring-1 ring-black ring-opacity-5 transition-all dark:bg-midnight-800 overflow-hidden group/notification"
      :class="to ? 'hover:bg-platinum-100 dark:hover:bg-midnight-400' : ''"
    >
      <slot />
    </component>
  </transition>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { RouteLocationRaw } from 'vue-router';

const props = defineProps({
  to: {
    type: [String, Object] as PropType<RouteLocationRaw>,
    required: false,
    default: undefined,
  },
});
</script>