<template>
  <div class="h-0 overflow-visible">
    <VTooltip v-if="tooltip" :content="tooltip" placement="left">
      <div :class="notificationTypeClasses" class="clip-triangle absolute top-0 h-8 w-8 cursor-pointer rounded-tl-md">
        <BiPin
          v-if="notificationType == 'pinned'"
          class="-mt-1.5 ml-1 inline w-3 scale-x-[-1] transform text-platinum-50 dark:text-midnight-600"
          aria-hidden="true"
        />
        <BIconCheckCircle
          v-if="notificationType == 'success'"
          class="-mt-1.5 ml-1 inline w-3 text-platinum-200 dark:text-midnight-600"
          aria-hidden="true"
        />
        <BIconXCircle
          v-if="notificationType == 'error'"
          class="-mt-1.5 ml-1 inline w-3 text-platinum-200 dark:text-midnight-600"
          aria-hidden="true"
        />
        <BIconExclamationCircle
          v-if="notificationType == 'alert'"
          class="-mt-1.5 ml-1 inline w-3 text-platinum-200 dark:text-midnight-600"
          aria-hidden="true"
        />
        <BIconExclamationTriangle
          v-if="notificationType == 'warning'"
          class="-mt-1.5 ml-1 inline w-3 text-platinum-200 dark:text-midnight-600"
          aria-hidden="true"
        />
        <BIconInfoCircle
          v-if="notificationType == 'info'"
          class="-mt-1.5 ml-1 inline w-3 text-platinum-200 dark:text-midnight-600"
          aria-hidden="true"
        />
      </div>
    </VTooltip>
    <div
      v-else-if="notificationTypeClasses"
      :class="notificationTypeClasses"
      class="clip-triangle absolute top-0 h-8 w-8 rounded-tl-md"
    >
      <BiPin
        v-if="notificationType == 'pinned'"
        class="-mt-0.5 ml-1.5 inline w-3 scale-x-[-1] transform text-platinum-50 dark:text-midnight-600"
        aria-hidden="true"
      />
      <BIconCheckCircle
        v-if="notificationType == 'success'"
        class="-mt-0.5 ml-1.5 inline w-3 text-platinum-200 dark:text-midnight-600"
        aria-hidden="true"
      />
      <BIconXCircle
        v-if="notificationType == 'error'"
        class="-mt-0.5 ml-1.5 inline w-3 text-platinum-200 dark:text-midnight-600"
        aria-hidden="true"
      />
      <BIconExclamationCircle
        v-if="notificationType == 'alert'"
        class="-mt-0.5 ml-1.5 inline w-3 text-platinum-200 dark:text-midnight-600"
        aria-hidden="true"
      />
      <BIconExclamationTriangle
        v-if="notificationType == 'warning'"
        class="-mt-0.5 ml-1.5 inline w-3 text-platinum-200 dark:text-midnight-600"
        aria-hidden="true"
      />
      <BIconInfoCircle
        v-if="notificationType == 'info'"
        class="-mt-0.5 ml-1.5 inline w-3 text-platinum-200 dark:text-midnight-600"
        aria-hidden="true"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import BiPin from '~icons/bi/pin-angle-fill';
import BIconCheckCircle from '~icons/bi/check-circle';
import BIconXCircle from '~icons/bi/x-circle';
import BIconExclamationCircle from '~icons/bi/exclamation-circle';
import BIconExclamationTriangle from '~icons/bi/exclamation-triangle';
import BIconInfoCircle from '~icons/bi/info-circle';

// Props definition
const props = defineProps({
  tooltip: {
    type: String,
    default: null,
  },
  notificationType: {
    type: String,
    default: null,
  },
});

const notificationTypeClasses = computed(
  () =>
    ({
      pinned: 'bg-primary-500 dark:bg-primary-500',
      success: 'bg-green-500 dark:bg-green-500/30',
      error: 'bg-red-400 dark:bg-red-900/75',
      alert: 'bg-orange-500 dark:bg-orange-500/40',
      warning: 'bg-amber-500 dark:bg-amber-500/30',
      info: 'bg-purple-400 dark:bg-purple-500/40',
    }[props.notificationType] || false)
);
</script>

<style scoped>
.clip-triangle {
  clip-path: polygon(100% 0%, 0% 0%, 0% 100%);
}
</style>
