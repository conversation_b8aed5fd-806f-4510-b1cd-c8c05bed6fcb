<template>
  <div class="relative h-1.5 w-full bg-platinum-300 dark:bg-midnight-600">
    <div ref="progress" class="progress h-full" :class="colorClasses" :style="style"></div>
  </div>
</template>

<script>
export default {
  name: 'Progress',
  props: {
    accent: {
      type: String,
      required: true,
    },
    hidden: {
      type: Boolean,
      default: false,
    },
    timeoutMilliseconds: {
      type: [String, Number],
      default: 10000,
    },
    shouldStart: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    style() {
      return {
        animationDuration: `${this.timeoutMilliseconds}ms`,
        animationPlayState: this.shouldStart ? 'running' : 'paused',
      };
    },
    colorClasses() {
      return {
        info: 'bg-blue-600',
        success: 'bg-green-600',
        warning: 'bg-yellow-600',
        danger: 'bg-red-600',
      }[this.accent];
    },
  },
  mounted() {
    if (this.$refs.progress) {
      this.$refs.progress.addEventListener('animationend', () => this.$emit('decayed'));
    }
  },
  beforeUnmount() {
    if (this.$refs.progress) {
      this.$refs.progress.removeEventListener('animationend', () => this.$emit('decayed'));
    }
  },
};
</script>

<style scoped>
@keyframes shrink-x {
  0% {
    transform: scaleX(1);
  }
  100% {
    transform: scaleX(0);
  }
}

.progress {
  width: 100%;
  transform-origin: left;
  animation: shrink-x linear 1 forwards;
}
</style>
