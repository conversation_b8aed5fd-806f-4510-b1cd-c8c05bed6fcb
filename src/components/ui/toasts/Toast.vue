<template>
  <div
    class="pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-platinum-50 dark:bg-midnight-900 shadow-lg dark:shadow-midnight-200/25 ring-1 ring-black ring-opacity-5"
  >
    <div class="p-4" role="alert" :aria-label="toast.title">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <BIconCheckCircle v-if="toast.accent === 'success'" class="h-6 w-6 text-green-500" />
          <BIconExclamationCircle v-if="toast.accent === 'warning'" class="h-6 w-6 text-yellow-500" />
          <BIconXCircle v-if="toast.accent === 'danger'" class="h-6 w-6 text-red-500" />
          <BIconInfoCircle v-if="toast.accent === 'info'" class="h-6 w-6 text-blue-500" />
        </div>
        <div class="ml-3 w-0 flex-1 space-y-1 pt-0.5">
          <p v-if="toast.title" class="text-sm font-bold text-platinum-950 dark:text-midnight-50">{{ toast.title }}</p>
          <ul v-if="Array.isArray(toast.message)" class="list-inside list-disc text-sm text-platinum-800 dark:text-midnight-50">
            <li v-for="(message, index) in toast.message" :key="index">{{ message }}</li>
          </ul>
          <p v-else class="text-sm text-platinum-950 dark:text-midnight-50" v-html="toast.message"/>
        </div>
        <div v-if="toast.dismissable" class="ml-4 flex flex-shrink-0">
          <button
            class="inline-flex rounded-md bg-platinum-300 text-primary-400 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-platinum-200 dark:bg-midnight-600 dark:text-primary-600 dark:hover:bg-midnight-800 dark:focus:ring-offset-midnight-400 transition-colors"
            @click="$emit('dismiss', toast.id)"
          >
            <span class="sr-only">Close</span>
            <BIconX class="h-5 w-5" aria-hidden="true" />
          </button>
        </div>
      </div>
    </div>
    <VProgress
      :should-start="!toast.persist"
      :accent="toast.accent"
      :timeout-milliseconds="toast.timeoutMilliseconds"
      @decayed="$emit('dismiss', toast.id)"
    />
  </div>
</template>

<script setup>
import BIconCheckCircle from '~icons/bi/check-circle';
import BIconExclamationCircle from '~icons/bi/exclamation-circle';
import BIconInfoCircle from '~icons/bi/info-circle';
import BIconX from '~icons/bi/x';
import BIconXCircle from '~icons/bi/x-circle';
import VProgress from './Progress.vue';

defineProps({
  toast: {
    type: Object,
    required: true,
  },
});
</script>
