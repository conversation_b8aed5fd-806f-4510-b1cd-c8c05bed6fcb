<template>
  <div class="pointer-events-none fixed inset-0 z-[80] flex flex-col items-end justify-end space-y-4 px-4 py-6 sm:p-6">
    <transition-group
      name="list"
      tag="ul"
      class="w-full max-w-sm space-y-4"
      enter-active-class="transform ease-out duration-300 transition"
      enter-from-class="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
      enter-to-class="translate-y-0 opacity-100 sm:translate-x-0"
      leave-active-class="transition ease-in duration-100"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <li v-for="toast in toasts" :key="toast.id">
        <Toast :toast="toast" @dismiss="dismissToast" />
      </li>
    </transition-group>
  </div>
</template>

<script>
import { storeToRefs } from 'pinia';

import { useToasts } from '@/store';

import Toast from './Toast.vue';

export default {
  name: 'Toasts',
  components: {
    Toast,
  },
  setup() {
    const toastStore = useToasts();

    const { toasts } = storeToRefs(toastStore);
    const { dismissToast } = toastStore;

    return {
      toasts,
      dismissToast,
    };
  },
};
</script>
