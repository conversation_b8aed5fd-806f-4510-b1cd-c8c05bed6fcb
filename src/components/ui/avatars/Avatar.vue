<template>
  <div
    class="relative inline-block rounded-full mt-0.5 bg-cover"
    :style="src ? { backgroundImage: `url(${src})` } : {}"
    :class="classes.container({ size })"
    
  >
    <VEntityLink
      v-if="avatarable && avatarableId"
      :entity-type="avatarable"
      :entity-id="avatarableId"
      :value="name"
      :show-icon="false"
      :show-link-icon="false"
    >
      <div
        v-if="src"
        class="rounded-full bg-cover"
        :class="classes.container({ size })"
      >

      </div>
      <AvatarPlaceholderImg 
        v-if="!src"
        :name="name"
        :class="classes.image({ size })"
        responsive
      />
    </VEntityLink>
    <span v-else>
      <AvatarPlaceholderImg 
        :name="name"
        :class="classes.image({ size })"
        responsive
      />
    </span>

    <span v-if="dot" class="absolute bottom-0 right-0 block transform rounded-full">
      <span
        :class="
          classes.dot({
            size,
          })
        "
        :style="{
          'background-color': dot,
        }"
        class="block rounded-full transition-colors"
      />
    </span>
  </div>
</template>

<script lang="ts" setup>
import { cva, VariantProps } from 'class-variance-authority';
import AvatarPlaceholderImg from '@/components/ui/avatars/AvatarPlaceholder.vue';

const props = withDefaults(
  defineProps<{
    src?: string | undefined;
    name?: string | null | undefined;
    dot?: string | undefined;
    size?: ImageVariantProps['size'];
    avatarable?: 'user' | 'company' | 'customer' | false;
    avatarableId?: string | undefined;
  }>(),
  {
    size: 'base',
    avatarable: false,
    name: undefined,
    src: undefined,
    avatarableId: undefined,
  }
);

const classes = {
  image: cva('', {
    variants: {
      size: {
        xxs: 'h-4 w-4 text-[0.5rem]',
        xs: 'h-6 w-6 text-[0.6rem]',
        sm: 'h-8 w-8 text-xs',
        base: 'h-10 w-10 text-md',
        lg: 'h-16 w-16 text-2xl',
        xl: 'h-20 w-20 text-3xl',
        '2xl': 'h-24 w-24 text-4xl',
      },
    },
  }),
  dot: cva('', {
    variants: {
      size: {
        xxs: 'h-2 w-2 translate-y-[0.07rem] translate-x-[0.07rem]',
        xs: 'h-2 w-2 translate-y-[0.07rem] translate-x-[0.07rem]',
        sm: 'h-3 w-3 translate-y-0.5 translate-x-0.5',
        base: 'h-3.5 w-3.5 translate-y-0.5 translate-x-0.5',
        lg: 'h-5 w-5 translate-y-[0.15rem] translate-x-[0.15rem]',
        xl: 'h-6 w-6 translate-y-1 translate-x-1',
        '2xl': 'h-8 w-8 translate-y-1.5 translate-x-1.5',
      },
    },
  }),
  container: cva('', {
    variants: {
      size: {
        xxs: 'h-4 w-4',
        xs: 'h-6 w-6',
        sm: 'h-8 w-8',
        base: 'h-10 w-10',
        lg: 'h-16 w-16',
        xl: 'h-20 w-20',
        '2xl': 'h-24 w-24',
      },
    },
  }),
};

type ImageVariantProps = VariantProps<typeof classes.image>;
</script>
