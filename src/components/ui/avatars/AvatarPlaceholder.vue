<template>
  <div
    class="flex items-center justify-center rounded-full font-bold select-none transition-colors duration-300 text-white/70 dark:text-black/50"
    :style="{
      backgroundColor: autoColor ? `${getBackgroundColorFromName}` : `${backgroundColor}`
    }"
  >
    {{ initials }}
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  name?: string | null | undefined;
  backgroundColor?: string;
  autoColor?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  backgroundColor: '#79ADC6',
  autoColor: true,
  name: undefined,
});

const initials = computed(() => {
  if (!props.name) return '?';  // No name, fallback.
  
  const nameParts = props.name.split(' ');
  const firstInitial = nameParts[0].charAt(0).toUpperCase();
  const lastInitial = nameParts.length > 1 ? nameParts[nameParts.length - 1].charAt(0).toUpperCase() : '';
  
  return (firstInitial + lastInitial) || '?';
});

const getBackgroundColorFromName = computed(() => {
  if (!props.name) return props.backgroundColor;
  
  // Create a consistent hash from the name
  let hash = 0;
  for (let i = 0; i < props.name.length; i++) {
    hash = props.name.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  let color = '';
  for (let i = 0; i < 3; i++) {
    // Extract R, G, B components
    const value = (hash >> (i * 8)) & 0xFF;
    
    // Transform to preferred range (125-220 instead of 0-255)
    const pastelValue = 125 + (value % 60);
    
    color += pastelValue.toString(16).padStart(2, '0');
  }
  
  return "#" + color;
});
</script>
