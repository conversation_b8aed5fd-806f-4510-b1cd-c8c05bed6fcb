<template>
  <Primitive
    v-if="as !== 'router-link' && as !== 'a'"
    :as="as"
    :as-child="asChild"
    :class="cn(buttonVariants({ intent, size, square, outline }), props.class)"
    v-bind="(as === 'button') ? { type, disabled } : {}"
  >
    <div class="flex w-full items-center justify-evenly align-middle space-x-2">
      <div class="flex w-full items-center space-x-2">
        <div v-if="$slots.start" class="float-left align-middle" aria-hidden="true">
          <slot name="start" aria-hidden="true" />
        </div>
        <div class="float-left flex items-center" :class="centered ? 'w-full justify-center text-center' : ''">
          <slot />
        </div>
      </div>
      <div v-if="$slots.end" class="flex items-center" aria-hidden="true">
        <slot name="end" aria-hidden="true" />
      </div>
    </div>
  </Primitive>
  <component
    v-else
    :is="as"
    v-bind="(as === 'router-link') ? { to, disabled } : (as === 'a') ? { href, disabled } : {}"
    :as-child="asChild"
    :class="cn(buttonVariants({ intent, size, square, outline }), props.class)"
  >
    <div class="flex w-full items-center justify-evenly align-middle space-x-2">
      <div class="flex w-full items-center space-x-2">
        <div v-if="$slots.start" class="float-left align-middle" aria-hidden="true">
          <slot name="start" aria-hidden="true" />
        </div>
        <div class="float-left flex items-center" :class="centered ? 'w-full justify-center text-center' : ''">
          <slot />
        </div>
      </div>
      <div v-if="$slots.end" class="flex items-center" aria-hidden="true">
        <slot name="end" aria-hidden="true" />
      </div>
    </div>
  </component>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils';
import { Primitive } from 'reka-ui';
import { buttonVariants } from '.';
import { VariantProps } from 'class-variance-authority';
import { type Component } from 'vue';
import type { RouteLocationRaw } from 'vue-router';

type ButtonVariantProps = VariantProps<typeof buttonVariants>;

const props = withDefaults(
  defineProps<{
    size?: ButtonVariantProps['size'];
    intent?: ButtonVariantProps['intent'];
    square?: ButtonVariantProps['square'];
    outline?: boolean;
    as?: string | Component;
    type?: string;
    to?: string | RouteLocationRaw;
    href?: string;
    disabled?: boolean;
    centered?: boolean;
    asChild?: boolean;
    class?: string;
  }>(),
  {
    size: 'md',
    intent: 'primary',
    as: 'button',
    type: 'button',
    square: false,
    outline: false,
    asChild: false,
    centered: false,
    disabled: false,
  }
);
</script>