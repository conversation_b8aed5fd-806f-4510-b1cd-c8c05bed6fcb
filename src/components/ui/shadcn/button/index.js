import { cva } from 'class-variance-authority';

export { default as But<PERSON> } from './Button.vue';

export const buttonVariants = cva(
  'duration-250 inline-flex items-center font-semibold uppercase tracking-widest transition-all disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      intent: {
        primary: 'focus-visible:outline-primary-500 focus:ring-4 focus:ring-primary-200 dark:focus:ring-primary-500 rounded-md',
        secondary: 'focus-visible:outline-platinum-300 focus:ring-4 focus:ring-platinum-300 dark:focus:ring-midnight-400 rounded-md',
        danger: 'focus-visible:outline-danger-500 focus:ring-4 focus:ring-danger-100 dark:focus:ring-danger-500 rounded-md',
        warning: 'focus-visible:outline-warning-700 focus:ring-4 focus:ring-warning-200 dark:focus:ring-warning-600 rounded-md',
        info: 'focus-visible:outline-info-700 focus:ring-4 focus:ring-info-200 dark:focus:ring-info-600 rounded-md',
        success: 'focus-visible:outline-success-700 focus:ring-4 focus:ring-success-100 dark:focus:ring-success-600 rounded-md',
        nav: 'h-full rounded-none hover:bg-primary-500 text-primary-500 hover:text-platinum-200 dark:hover:text-midnight-600',
        input: 'w-full min-w-[80px] appearance-none border-0 p-0 text-base text-primary-500 dark:text-midnight-50 outline-none placeholder:text-platinum-500 dark:placeholder:text-midnight-200 autofill:bg-white focus:outline-none disabled:cursor-not-allowed bg-white dark:bg-midnight-300 !tracking-normal focus:ring-2 rounded',
        table: 'rounded-full !p-1.5',
        panelButton: 'rounded-full !p-1',
        navBottom: 'block w-full select-none !px-6 !py-4 text-left text-white',
        fieldExtend: 'w-full',
        modalHeaderClose: 'rounded-md',
        icon: 'rounded-full !p-0 opacity-75 hover:opacity-100',
        notification: 'w-full text-platinum-950 dark:text-midnight-100 bg-platinum-100 dark:bg-midnight-900/50 hover:bg-primary-500 hover:text-white dark:hover:text-platinum-50 dark:hover:bg-primary-600 gap-1.5 !text-xs',
      },
      size: {
        xxs: 'gap-1 text-xxs',
        xs: 'gap-1.5 text-xxs',
        sm: 'gap-2 text-xs',
        md: 'gap-2.5 text-sm',
        lg: 'gap-3 text-base',
      },
      square: {
        true: null,
      },
      outline: {
        true: null,
      },
    },
    compoundVariants: [
      {
        intent: 'primary',
        class: 'text-primary-50 dark:text-primary-800 hover:text-primary-600 dark:hover:text-primary-400 shadow bg-primary-500 dark:bg-primary-500 hover:bg-primary-200 dark:hover:bg-primary-700 outline outline-1 outline-transparent hover:outline-primary-500 dark:hover:outline-primary-500',
        outline: false,
      },
      {
        intent: 'secondary',
        class: 'text-primary-500 dark:text-primary-500 hover:text-primary-600 dark:hover:text-primary-400 shadow bg-platinum-50 dark:bg-midnight-500 hover:bg-platinum-300 dark:hover:bg-midnight-800 outline outline-1 outline-transparent dark:hover:outline-midnight-500',
        outline: false,
      },
      {
        intent: 'danger',
        class: 'text-danger-50 dark:text-danger-700 hover:text-danger-500 dark:hover:text-danger-400 shadow bg-danger-300 dark:bg-danger-500 hover:bg-danger-100 dark:hover:bg-danger-600 outline outline-1 outline-transparent hover:outline-danger-500 dark:hover:outline-danger-500',
        outline: false,
      },
      {
        intent: 'warning',
        class: 'text-warning-50 dark:text-warning-900 hover:text-warning-600 dark:hover:text-warning-500 shadow bg-warning-400 dark:bg-warning-600 hover:bg-warning-200 dark:hover:bg-warning-800 outline outline-1 outline-transparent hover:outline-warning-700 dark:hover:outline-warning-700',
        outline: false,
      },
      {
        intent: 'info',
        class: 'text-info-50 dark:text-info-900 hover:text-info-600 dark:hover:text-info-500 shadow bg-info-300 dark:bg-info-600 hover:bg-info-200 dark:hover:bg-info-800 outline outline-1 outline-transparent dark:hover:outline-info-700',
        outline: false,
      },
      {
        intent: 'success',
        class: 'text-success-50 dark:text-success-900 hover:text-success-600 dark:hover:text-success-500 shadow bg-success-300 dark:bg-success-600 hover:bg-success-100 dark:hover:bg-success-800 outline outline-1 outline-transparent dark:hover:outline-success-700',
        outline: false,
      },
      {
        intent: 'input',
        class: 'flex items-center text-gray-500 ring-inset ring-1 focus-within:caret-primary-400 bg-platinum-200 dark:bg-midnight-300 ring-platinum-200 dark:ring-transparent transition hover:ring-primary-400 dark:hover:ring-primary-600 focus:ring-primary-400 dark:focus:ring-primary-600 active:ring-primary-400 dark:active:ring-primary-600 focus-within:ring-primary-400 dark:focus-within:ring-primary-600 normal-case tracking-normal',
        outline: false,
      },
      {
        intent: 'table',
        class: 'hover:bg-platinum-300 focus:bg-platinum-300 text-primary-400 hover:text-primary-500 dark:text-primary-500 hover:dark:text-primary-400 hover:dark:bg-midnight-400 focus:dark:bg-midnight-400 outline-transparent data-[state=open]:bg-platinum-300 data-[state=open]:text-primary-600 dark:data-[state=open]:bg-midnight-400 dark:data-[state=open]:text-primary-400',
        outline: false,
      },
      {
        intent: 'icon',
        class: '!rounded-full focus-visible:outline focus-visible:outline-primary-500',
        outline: false,
      },
      {
        intent: 'panelButton',
        class: 'text-primary-50 dark:text-primary-800 shadow bg-primary-400 dark:bg-primary-500 opacity-80 dark:opacity-70 hover:opacity-100 dark:hover:opacity-100',
        outline: false,
      },
      {
        intent: 'fieldExtend',
        class: 'text-primary-300 hover:text-primary-400 dark:text-midnight-200 dark:hover:text-primary-500',
        outline: false,
      },
      {
        intent: 'modalHeaderClose',
        class: 'text-primary-500 dark:text-primary-500 hover:text-primary-600 dark:hover:text-primary-400 bg-platinum-300 dark:bg-midnight-600 hover:bg-platinum-400 dark:hover:bg-midnight-800 outline outline-1 outline-transparent dark:hover:outline-midnight-500',
        outline: false,
      },
      {
        intent: 'primary',
        class: 'text-primary-500 dark:text-primary-500 hover:text-primary-600 dark:hover:text-primary-400 outline outline-1 outline-primary-500 hover:bg-primary-200 dark:hover:bg-primary-700',
        outline: true,
      },
      {
        intent: 'secondary',
        class: 'text-platinum-200 dark:text-midnight-500 hover:text-primary-600 dark:hover:text-primary-400 outline outline-1 outline-platinum-50 dark:outline-midnight-400 hover:bg-platinum-300 hover:outline-primary-500 dark:hover:bg-midnight-700 dark:focus:outline-primary-500',
        outline: true,
      },
      {
        intent: 'danger',
        class: 'text-danger-200 dark:text-danger-500 hover:text-danger-600 dark:hover:text-danger-400 outline outline-1 outline-danger-200 hover:bg-danger-100 dark:hover:bg-danger-600 dark:focus:outline-danger-500 hover:outline-danger-500 dark:hover:outline-danger-500',
        outline: true,
      },
      {
        intent: 'warning',
        class: 'text-warning-200 dark:text-warning-500 hover:text-warning-600 dark:hover:text-warning-500 outline outline-1 outline-warning-200 hover:bg-warning-200 dark:hover:bg-warning-800 hover:outline-warning-700 dark:hover:outline-warning-500',
        outline: true,
      },
      {
        intent: 'info',
        class: 'text-info-300 dark:text-info-500 hover:text-info-600 dark:hover:text-info-500 outline outline-1 outline-info-300 hover:bg-info-200 dark:hover:bg-info-800 hover:outline-info-700 dark:hover:outline-info-500',
        outline: true,
      },
      {
        intent: 'success',
        class: 'text-success-200 dark:text-success-500 hover:text-success-600 dark:hover:text-success-400 outline outline-1 outline-success-200 hover:bg-success-100 dark:hover:bg-success-600 dark:focus:outline-success-500 hover:outline-success-500 dark:hover:outline-success-500',
        outline: true,
      },
      {
        size: 'xxs',
        square: false,
        class: 'leading-5 text-xxs py-[0.1875rem] px-1.5',
      },
      {
        size: 'xs',
        square: false,
        class: 'leading-5 text-xs py-1 px-2',
      },
      {
        size: 'sm',
        square: false,
        class: 'leading-5 text-sm py-1.5 px-3',
      },
      {
        size: 'md',
        square: false,
        class: 'py-2 leading-6 px-4',
      },
      {
        size: 'lg',
        square: false,
        class: 'py-3 leading-6 px-6',
      },
      {
        size: 'xxs',
        square: true,
        class: 'p-0.5',
      },
      {
        size: 'xs',
        square: true,
        class: 'p-1',
      },
      {
        size: 'sm',
        square: true,
        class: 'p-1.5',
      },
      {
        size: 'md',
        square: true,
        class: 'p-2',
      },
      {
        size: 'lg',
        square: true,
        class: 'p-4',
      },
    ],
    defaultVariants: {
      intent: 'primary',
      size: 'md',
      square: false,
      outline: false,
    },
  }
);
