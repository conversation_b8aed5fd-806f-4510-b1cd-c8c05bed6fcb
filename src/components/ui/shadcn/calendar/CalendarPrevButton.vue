<script setup>
import { cn } from '@/lib/utils';
import { buttonVariants } from '@/components/ui/shadcn/button';
import { ChevronLeft } from 'lucide-vue-next';
import { CalendarPrev, useForwardProps } from 'reka-ui';
import { computed } from 'vue';

const props = defineProps({
  prevPage: { type: Function, required: false },
  asChild: { type: Boolean, required: false },
  as: { type: null, required: false },
  class: { type: null, required: false },
});

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props;

  return delegated;
});

const forwardedProps = useForwardProps(delegatedProps);
</script>

<template>
  <CalendarPrev
    :class="
      cn(buttonVariants({ variant: 'outline' }), 'h-7 w-7 bg-transparent p-0 flex items-center justify-center opacity-50 hover:opacity-100', props.class)
    "
    v-bind="forwardedProps"
  >
    <slot>
      <ChevronLeft class="h-4 w-4" />
    </slot>
  </CalendarPrev>
</template>