<script lang="ts" setup>
import type { StepperSeparatorProps } from 'reka-ui'
import type { HTMLAttributes } from 'vue'
import { reactiveOmit } from '@vueuse/core'

import { StepperSeparator, useForwardProps } from 'reka-ui'
import { cn } from '@/lib/utils'

const props = defineProps<StepperSeparatorProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = reactiveOmit(props, 'class')

const forwarded = useForwardProps(delegatedProps)
</script>

<template>
  <StepperSeparator
    v-bind="forwarded"
    :class="cn(
      'bg-primary-500 dark:bg-primary-400 transition-colors duration-500',
      // Disabled
      // 'group-data-[disabled]:bg-platinum-900 group-data-[disabled]:opacity-75',
      // Completed
      'group-data-[state=completed]:bg-platinum-50/25 group-data-[state=completed]:dark:bg-midnight-700/25',
      props.class,
    )"
  />
</template>