<script lang="ts" setup>
import type { StepperIndicatorProps } from 'reka-ui'
import type { HTMLAttributes } from 'vue'
import { reactiveOmit } from '@vueuse/core'

import { StepperIndicator, useForwardProps } from 'reka-ui'
import { cn } from '@/lib/utils'

const props = defineProps<StepperIndicatorProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = reactiveOmit(props, 'class')

const forwarded = useForwardProps(delegatedProps)
</script>

<template>
  <StepperIndicator
    v-slot="slotProps"
    v-bind="forwarded"
    :class="cn(
      'text-lg inline-flex items-center justify-center rounded-full text-platinum-50 w-10 h-10 bg-primary-200/75 dark:bg-midnight-500/50 font-semibold transition duration-500',
      // Inactive
      'group-data-[state=inactive]:text-primary-400 group-data-[state=inactive]:dark:text-primary-600',
      // Active
      'group-data-[state=active]:bg-primary-600 group-data-[state=active]:dark:bg-primary-500 group-data-[state=active]:text-platinum-50 group-data-[state=active]:dark:text-midnight-400',
      // Completed
      'group-data-[state=completed]:bg-success-400/75 group-data-[state=completed]:dark:bg-success-800/70 group-data-[state=completed]:text-success-100/75 group-data-[state=completed]:dark:text-success-500/75',
      props.class,
    )"
  >
    <slot v-bind="slotProps" />
  </StepperIndicator>
</template>