<script setup>
import { TooltipRoot, useForwardPropsEmits } from 'reka-ui';
import { provide } from 'vue';

const props = defineProps({
  defaultOpen: { type: Boolean, required: false },
  open: { type: Boolean, required: false },
  delayDuration: { type: Number, required: false },
  disableHoverableContent: { type: Boolean, required: false },
  disableClosingTrigger: { type: Boolean, required: false },
  disabled: { type: Boolean, required: false },
  ignoreNonKeyboardFocus: { type: Boolean, required: false },
  size: { type: String, required: false, default: 'base', validator: (value) => ['sm', 'base', 'lg'].includes(value) },
});
const emits = defineEmits(['update:open']);

provide('tooltip-size', props.size);

const forwarded = useForwardPropsEmits(props, emits);
</script>

<template>
  <TooltipRoot v-bind="forwarded">
    <slot />
  </TooltipRoot>
</template>
