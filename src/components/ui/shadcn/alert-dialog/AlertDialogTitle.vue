<script setup lang="ts">
import { cn } from '@/lib/utils';
import { AlertDialogTitle, type AlertDialogTitleProps } from 'reka-ui';
import { computed, type HTMLAttributes } from 'vue';

const props = defineProps<AlertDialogTitleProps & { class?: HTMLAttributes['class'] }>();

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props;

  return delegated;
});
</script>

<template>
  <AlertDialogTitle v-bind="delegatedProps" :class="cn('text-lg font-medium text-primary-500 flex items-start justify-between border-b mb-1 border-platinum-300 pb-3 pr-7 dark:border-midnight-600', props.class)">
    <slot />
  </AlertDialogTitle>
</template>
