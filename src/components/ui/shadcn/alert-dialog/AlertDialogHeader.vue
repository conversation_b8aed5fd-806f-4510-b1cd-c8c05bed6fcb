<script setup lang="ts">
import type { HTMLAttributes } from 'vue';
import { cn } from '@/lib/utils';
import type { Component } from 'vue';
const props = withDefaults(
  defineProps<{
    class?: HTMLAttributes['class'];
    icon?: Component;
  }>(), {
    class: '',
    icon: undefined,
  }
);
</script>

<template>
  <div class="px-6 pt-6">
    <div class="sm:flex sm:items-start sm:space-x-6 relative">
      <div
        v-if="props.icon"
        class="mx-auto hidden h-14 w-14 flex-shrink-0 items-center justify-center rounded-full bg-primary-500 sm:mx-0 sm:flex sm:h-10 sm:w-10"
      >
        <component :is="props.icon" class="h-5 w-5 text-platinum-200 dark:text-midnight-500" />
      </div>
      <div :class="cn('flex flex-col gap-y-2 text-left w-full', props.class)">
        <slot />
      </div>
    </div>
  </div>
</template>
