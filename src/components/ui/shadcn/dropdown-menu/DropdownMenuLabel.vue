<script setup>
import { cn } from '@/lib/utils';
import { DropdownMenuLabel, useForwardProps } from 'reka-ui';
import { computed } from 'vue';

const props = defineProps({
  asChild: { type: Boolean, required: false },
  as: { type: null, required: false },
  class: { type: null, required: false },
  inset: { type: Boolean, required: false },
});

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props;

  return delegated;
});

const forwardedProps = useForwardProps(delegatedProps);
</script>

<template>
  <DropdownMenuLabel
    v-bind="forwardedProps"
    :class="cn('px-2 py-1.5 pb-0.5 text-xs font-semibold text-primary-400 dark:text-primary-500', inset && 'pl-8', props.class)"
  >
    <slot />
  </DropdownMenuLabel>
</template>
