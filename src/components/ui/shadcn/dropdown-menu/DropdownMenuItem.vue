<script setup>
import { cn } from '@/lib/utils';
import { DropdownMenuItem, useForwardProps } from 'reka-ui';
import { computed, useSlots } from 'vue';

const slots = useSlots();

const props = defineProps({
  disabled: { type: Boolean, required: false },
  textValue: { type: String, required: false },
  asChild: { type: Boolean, required: false },
  as: { type: null, required: false },
  class: { type: null, required: false },
  inset: { type: Boolean, required: false },
});

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props;

  return delegated;
});

const forwardedProps = useForwardProps(delegatedProps);

// Check if the slot element is a router-link, button or anchor.
// Simply use a span or similar element to avoid cursor pointer.
const shouldHaveCursorPointer = computed(() => {
  const slotNodes = slots.default?.(); // Get slot content
  if (!slotNodes || slotNodes.length === 0) return false;
  
  return slotNodes.some(node => {
    if (!node.type) return false; // Ensure it has a type
    const tag = typeof node.type === 'string' ? node.type : node.type.name;
    return ['RouterLink', 'a', 'button'].includes(tag) || tag === undefined;
  });
});

</script>

<template>
  <DropdownMenuItem
    v-bind="forwardedProps"
    :class="
      cn(
        'relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50 focus:bg-platinum-200 dark:focus:bg-midnight-600 [&>svg]:size-4 [&>svg]:shrink-0 text-platinum-950 focus:text-primary-500 dark:text-midnight-50 dark:focus:text-primary-500',
        inset && 'pl-8',
        props.class,
        shouldHaveCursorPointer && 'cursor-pointer'
      )
    "
  >
    <slot />
  </DropdownMenuItem>
</template>
