<template>
  <div class="rounded-md bg-platinum-50 px-4 py-3 dark:bg-midnight-400 max-w-xl">
    <TransitionFade>
      <div v-if="loading">
        <VSpinner size="lg" />
      </div>
      <dl v-else class="grid grid-cols-1 gap-x-3 gap-y-4 sm:grid-cols-2">
        <slot />
      </dl>
    </TransitionFade>
  </div>
</template>

<script setup>
defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
});
</script>
