<template>
  <div class="sm:col-span-1">
    <dt class="text-sm font-bold text-primary-500">
      <slot v-if="$slots.label" name="label" />
      <template v-else>
        {{ label }}
      </template>
    </dt>
    <dd class="mt-1 text-sm text-platinum-950 dark:text-midnight-50">
      <slot v-if="$slots.default" />
      <template v-else>
        <span v-if="value">{{ value.slice(0, limit) }}{{ value.length > limit ? '...' : '' }}</span>
        <span v-else class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
      </template>
    </dd>
  </div>
</template>

<script setup>
defineProps({
  label: {
    type: String,
    required: false,
  },
  value: {
    type: String,
    required: false,
  },
  limit: {
    type: Number,
    default: 150,
  },
});
</script>
