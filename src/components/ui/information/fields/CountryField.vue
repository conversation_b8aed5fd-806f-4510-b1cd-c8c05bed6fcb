<template>
  <div class="sm:col-span-1">
    <dt class="text-sm font-bold text-primary-500">
      <slot v-if="$slots.label" name="label" />
      <template v-else>
        {{ label }}
      </template>
    </dt>
    <dd class="mt-1 text-sm text-platinum-950 dark:text-midnight-50">
      <slot v-if="$slots.default" name="default" />
      <span v-else class="space-x-1">
        <img
          v-if="iso2"
          class="-mt-1 inline h-5 w-5"
          :src="`https://flagicons.lipis.dev/flags/4x3/${iso2.toLowerCase()}.svg`"
          :title="iso2"
          v-show="flagLoaded"
          @load="flagLoaded = true"
          @error="flagLoaded = false"
        />
        <span v-if="value">{{ value }}</span>
        <span v-else-if="showValue" class="italic text-platinum-600 dark:text-midnight-200">Undefined</span>
      </span>
    </dd>
  </div>
</template>

<script setup>
import { ref } from 'vue';

defineProps({
  label: {
    type: String,
    required: false,
  },
  value: {
    type: String,
    required: false,
  },
  iso2: {
    type: String,
    required: false,
  },
  showValue: {
    type: Boolean,
    default: true,
  },
});

const flagLoaded = ref(false);

</script>
