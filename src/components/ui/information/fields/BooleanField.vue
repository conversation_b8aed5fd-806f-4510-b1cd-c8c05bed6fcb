<template>
  <VDefaultField>
    <BooleanField :value="value" :true-label="trueLabel" :false-label="falseLabel" />
  </VDefaultField>
</template>

<script setup>

import BooleanField from '@/components/ui/fields/BooleanField.vue';

defineProps({
  value: {
    type: Boolean,
    required: false,
  },
  trueLabel: {
    type: String,
    required: false,
    default: 'Yes',
  },
  falseLabel: { 
    type: String,
    required: false,
    default: 'No',
  },
});
</script>
