<template>
  <VDefaultField>
    <template #label>{{ label }}</template>
    <template #value>
      <template v-if="value">{{ value }}</template>
      <span v-else class="italic text-gray-500">Undefined</span>
    </template>
  </VDefaultField>
</template>

<script setup>
defineProps({
  label: {
    type: String,
    required: false,
  },
  value: {
    type: String,
    required: false,
  },
});
</script>
