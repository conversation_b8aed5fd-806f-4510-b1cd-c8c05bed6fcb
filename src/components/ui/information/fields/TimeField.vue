<template>
  <VDefaultField>
    <template v-if="value">{{ formatted }}</template>
    <span v-else class="italic text-gray-500">Undefined</span>
  </VDefaultField>
</template>

<script setup>
import { useDateFormat } from '@vueuse/shared';
import { computed } from 'vue';

const props = defineProps({
  value: {
    type: String,
    required: false,
  },
  format: {
    type: String,
    default: 'YYYY-MM-DD HH:mm:ss',
  },
  locale: {
    type: [String, Boolean],
    default: 'en-GB',
  },
});

const formatted = computed(() => {
  if (!props.value) return '';

  try {
    if (props.locale) {
      const date = new Date(props.value);
      return new Intl.DateTimeFormat(props.locale, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      }).format(date);
    }
    return useDateFormat(props.value, props.format);
  } catch (error) {
    console.warn('Invalid date format or locale:', error);
    return props.value;
  }
});
</script>
