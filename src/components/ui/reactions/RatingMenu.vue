<template>
  <div>
    <ReactionItem
      name="Downvote"
      :emoji="BIconDashCircleFill"
      size="xs"
      tooltip="Vote Down"
      @reacted="createRating"
      :showCount="false"
      :selected="selectedRatingReactive == 'Downvote'"
      :selectedClasses="selectedRatingReactive == 'Downvote' ? 'bg-none' : ''"
      :iconClasses="getIconClasses('Downvote')"
      :animate="false"
    />
    <span class="text-midnight-200 font-semibold">{{ score }}</span>
    <ReactionItem
      name="Upvote"
      :emoji="BIconPlusCircleFill"
      size="xs"
      tooltip="Vote Up"
      @reacted="createRating"
      :showCount="false"
      :selected="selectedRatingReactive == 'Upvote'"
      :selectedClasses="selectedRatingReactive == 'Upvote' ? 'bg-none' : ''"
      :iconClasses="getIconClasses('Upvote')"
      :animate="false"
    />
  </div>
</template>

<script setup lang="ts">
import { axios } from '@/lib/axios';
import { computed, ref } from 'vue';
import BIconPlusCircleFill from '~icons/bi/plus-circle-fill';
import BIconDashCircleFill from '~icons/bi/dash-circle-fill';
import { Rating } from '@/types/api';

interface Props {
  size?: 'xs' | 'sm' | 'base' | 'lg',
  compact?: boolean,
  reactableType?: string,
  reactableId?: string,
  ratings: Rating[],
  selectedRating?: string,
  forceShow?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'base',
  compact: false,
  ratings: () => [],
  reactableType: undefined,
  reactableId: undefined,
  selectedRating: undefined,
  forceShow: false,
})

const initialSelectedRating = props.selectedRating;

const emit = defineEmits(['rated']);

// The reactions need to be reactive for when the user clicks a reaction.
const reactiveRatings = ref(props.ratings);
// The selected reaction
const selectedRatingReactive = ref(props.selectedRating);

// Computed property for upvotes, downvotes, and overall score.
const score = computed(() => {
  // Existing votes.
  const upvotes = reactiveRatings.value?.find((rating) => rating.name.toLowerCase() === 'upvote')?.count || 0;
  const downvotes = reactiveRatings.value?.find((rating) => rating.name.toLowerCase() === 'downvote')?.count || 0;
  return upvotes - downvotes + 1;
});

async function createRating(name: string) {

  if (!props.reactableType || !props.reactableId) {
    console.error('RatingMenu: reactableType and reactableId are required');
    return;
  }

  try {
    const response = await axios.post('/api/react', {
      reactable_type: props.reactableType,
      reactable_id: props.reactableId,
      name: name
    });

    // Update the reactiveRatings array with the new reaction.
    reactiveRatings.value = response.data.reactions;

    // The selected reaction.
    if (response.data?.selectedReaction == selectedRatingReactive.value) {
      selectedRatingReactive.value = undefined;
    } else {
      selectedRatingReactive.value = response.data.selectedReaction;
    }

    // Emit to the parent component that the reaction was created for potential use.
    emit('rated', selectedRatingReactive.value);
  } catch (error) {
    console.error('Failed to submit rating:', error);
  }
}

function getIconClasses(reactionName: string): string {
  const base = 'text-primary-400 dark:text-primary-600 transition-all duration-300 overflow-visible opacity-75 group-hover/container:opacity-100 p-0.5';
  
  const reactionBase =
    reactionName === 'Downvote'
      ? 'hover:!text-red-500/75 dark:hover:!text-red-700/75'
      : 'hover:!text-green-600/75 dark:hover:!text-green-600/75';
  

  const selected =
    selectedRatingReactive.value === reactionName
      ? reactionName === 'Downvote'
        ? '!opacity-100 !text-red-500/50 hover:!text-red-500/75 dark:!text-red-600/50 dark:hover:!text-red-700/75'
        : '!opacity-100 !text-green-600/50 hover:!text-green-600/75 dark:!text-green-600/50 dark:hover:!text-green-600/75'
      : '';

  const faded =
    selectedRatingReactive.value &&
    selectedRatingReactive.value !== reactionName &&
    !props.forceShow
      ? '!opacity-25 hover:!opacity-100'
      : '';

  const forceVisible = props.forceShow ? '!opacity-100' : '';

  return [base, reactionBase, selected, faded, forceVisible].filter(Boolean).join(' ');
}


</script>