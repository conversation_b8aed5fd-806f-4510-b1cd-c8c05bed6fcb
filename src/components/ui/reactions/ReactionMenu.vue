<template>
  <div class="flex items-center gap-1">
    <!-- Existing Reactions (if any) -->
    <div
      v-if="preferredReactions.length > 0"
      class="bg-platinum-200 dark:bg-midnight-600 rounded-lg flex items-center ease-in-out whitespace-nowrap transition-all duration-300"
      :class="containerClasses[size]"
    >
      <ReactionItem
        v-for="reaction in preferredReactions"
        :key="reaction.name"
        :name="reaction.name"
        :emoji="getEmojiForReaction(reaction.name)"
        :count="getReactionCount(reaction.name)"
        :tooltip="getTooltipForReaction(reaction.name)"
        :selected="selectedReactionReactive === reaction.name"
        @reacted="createReaction"
        :disabled="disabled"
      />
    </div>

    <!-- Reactions menu (adding more!) -->
    <div
      v-if="!disabled && (compact || (!compact && preferredReactions.length !== displayReactions.length))"
      class="bg-platinum-200 dark:bg-midnight-600 rounded-lg flex items-center ease-in-out whitespace-nowrap transition-all duration-300"
      :class="containerClasses[size]"
    >
      <TooltipProvider
        :delayDuration="300"
        :disabled="false"
      >
        <Tooltip
          :open="showAllReactions"
          :size="['xs', 'sm'].includes(size) ? 'sm' : 'base'"
          @openChange="showAllReactions = $event"
        >
          <TooltipTrigger
            @click="openReactionsTooltip"
            type="button"
            @mouseenter="openReactionsTooltip()"
            @mouseleave="hoveringReactionBtn = false"
            @focus="focusingReactionBtn = true"
            @blur="focusingReactionBtn = false"
          >
            <button
              type="button"
              :class="[
                'transform-gpu transition-all duration-300 ease-in-out flex items-center justify-center scale-[0.7]',
                'hover:z-10 hover:scale-[0.8]',
                'focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-500',
                'rounded-full text-platinum-600 dark:text-midnight-50/50',
                sizeClasses[size]
              ]"
            >
              <BiIconPlusCircleDotted />
            </button>
          </TooltipTrigger>
          <TooltipContent
            class="p-0 overflow-visible"
            @mouseenter="hoveringReactionTooltip = true"
            @mouseleave="hoveringReactionTooltip = false"
            @focus="focusingReactionTooltip = true"
            @blur="focusingReactionTooltip = false"
          >
            <div class="bg-platinum-50 dark:bg-midnight-600 rounded-lg p-[0.0625rem]">
              <div class="flex flex-row" :class="['xs', 'sm'].includes(size) ? 'gap-1.5' : 'gap-2'">
                <ReactionItem
                  v-for="reaction in displayReactions"
                  :key="reaction.name"
                  :name="reaction.name"
                  :emoji="getEmojiForReaction(reaction.name)"
                  :size="['xs', 'sm'].includes(size) ? 'base' : size"
                  :tooltip="reaction.tooltip"
                  :showCount="compact"
                  :count="getReactionCount(reaction.name)"
                  :selected="selectedReactionReactive === reaction.name"
                  @reacted="createReaction"
                />
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ReactionItem from '@/components/ui/reactions/ReactionItem.vue'
import { axios } from '@/lib/axios';

import NotoIconThumbsUp from '~icons/noto/thumbs-up';
import NotoIconRedHeart from '~icons/noto/red-heart';
import NotoIconFaceWithTearsOfJoy from '~icons/noto/face-with-tears-of-joy';
import NotoIconAstonishedFace from '~icons/noto/astonished-face';
import NotoIconCryingFace from '~icons/noto/loudly-crying-face';
import NotoIconAngryFace from '~icons/noto/angry-face';

import BiIconPlusCircleDotted from '~icons/bi/plus-circle-dotted';

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/shadcn/tooltip';

interface Reaction {
  name: string
  count: number
  tooltip: string
}

interface Props {
  size?: 'xs' | 'sm' | 'base' | 'lg',
  compact?: boolean,
  reactableType?: string,
  reactableId?: string,
  reactions: Reaction[],
  selectedReaction?: string,
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'base',
  compact: false,
  reactions: () => [],
  reactableType: undefined,
  reactableId: undefined,
  selectedReaction: undefined,
  disabled: false
})

const displayReactions = [
  { name: 'Like', emoji: NotoIconThumbsUp, tooltip: 'I like this!' },
  { name: 'Love', emoji: NotoIconRedHeart, tooltip: 'Love it!' },
  { name: 'Laugh', emoji: NotoIconFaceWithTearsOfJoy, tooltip: 'Funny!' },
  { name: 'Wow', emoji: NotoIconAstonishedFace, tooltip: 'Wow!' },
  { name: 'Sad', emoji: NotoIconCryingFace, tooltip: 'Sad times...' },
  { name: 'Angry', emoji: NotoIconAngryFace, tooltip: 'Angry!' }
]

// The reactions need to be reactive for when the user clicks a reaction.
const reactiveReactions = ref(
  // From props.reactions, filter out any reactions not defined in displayReactions.
  props.reactions.filter(r => displayReactions.some(dr => dr.name === r.name))
);

const emit = defineEmits<{
  (e: 'reacted', name: string): void
}>()

const showAllReactions = ref(false);

const hoveringReactionBtn = ref(false);
const focusingReactionBtn = ref(false); // Focus for mobile support mostly.
const hoveringReactionTooltip = ref(false);
const focusingReactionTooltip = ref(false);

// Open the reactions tooltip.
const openReactionsTooltip = () => {
  hoveringReactionBtn.value = true;
  showAllReactions.value = true;
  // start a interval that will close the tooltip after a few seconds when not hovering the button or tooltip content.
  const interval = setInterval(() => {
    if (
      !hoveringReactionBtn.value &&
      !hoveringReactionTooltip.value &&
      !focusingReactionBtn.value &&
      !focusingReactionTooltip.value
    ) {
      // Close the tooltip and clear the interval.
      showAllReactions.value = false;
      clearInterval(interval);
    }
  }, 1000);
};

const selectedReactionReactive = ref(props.selectedReaction)

function getEmojiForReaction(name: string): object {
  return displayReactions.find(r => r.name === name)?.emoji || ''
}

function getTooltipForReaction(name: string): string {
  return displayReactions.find(r => r.name === name)?.tooltip || ''
}

const containerClasses = {
  xs: 'px-[0.15rem] py-[0.05rem] gap-0.5',
  sm: 'px-0.5 py-0.5 gap-1',
  base: 'px-0.5 py-0.5 gap-1',
  lg: 'px-1 py-1 gap-1.5'
}

function getReactionCount(name: string): number {
  return reactiveReactions.value.find(r => r.name === name)?.count || 0
}

/**
 * Get the preferred reactions to display.
 * @returns array of reactions.
 */
const preferredReactions = computed(() => {

  if (!props.compact) {
    // Match the displayReactions order, then filter out any less than 0
    return reactiveReactions.value.filter(r => r.count > 0).sort((a, b) => {
      const aIndex = displayReactions.findIndex(r => r.name === a.name);
      const bIndex = displayReactions.findIndex(r => r.name === b.name);
      return aIndex - bIndex;
    });
  }

  // Sort reactions by count descending, then by order defined in displayReactions.
  const sorted = [...reactiveReactions.value].sort((a, b) => {
    const aIndex = displayReactions.findIndex(r => r.name === a.name);
    const bIndex = displayReactions.findIndex(r => r.name === b.name);
    return aIndex - bIndex;
  });

  // If the user has already reacted, we want to show that reaction first.
  if (selectedReactionReactive.value) {
    return sorted.filter(r => r.name === selectedReactionReactive.value);
  }

  if (props.compact) {
    // We only want to show one (the most popular) if any.
    return sorted.filter(r => r.count > 0).slice(0, 1);
  } else {
    // show any reactions that have a count greater than 0.
    return sorted.filter(r => r.count > 0);
  }
});

/**
 * Add a reaction for relevant reactable type and id.
 * @param name - The name of the reaction
 */
async function createReaction(name: string) {

  if (!props.reactableType || !props.reactableId) {
    console.error('ReactionMenu: reactableType and reactableId are required');
    return;
  }

  try {
    const response = await axios.post('/api/react', {
      reactable_type: props.reactableType,
      reactable_id: props.reactableId,
      name: name
    });

    // Update the reactiveReactions array with the new reaction.
    reactiveReactions.value = response.data.reactions;

    // The selected reaction.
    if (response.data.selectedReaction == selectedReactionReactive.value) {
      selectedReactionReactive.value = undefined;
    } else {
      selectedReactionReactive.value = response.data.selectedReaction;
    }

    // Emit to the parent component that the reaction was created for potential use.
    emit('reacted', selectedReactionReactive.value);
    // Close the menu if open.
    showAllReactions.value = false;
  } catch (error) {
    console.error('Failed to submit reaction:', error);
  }
}

const sizeClasses = {
  xs: 'text-lg h-4 w-4',
  sm: 'text-xl h-5 w-[1.125rem]',
  base: 'text-2xl h-6 w-6',
  lg: 'text-3xl h-7 w-7'
}

</script>
