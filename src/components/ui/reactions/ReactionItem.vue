<template>
  <button
    type="button"
    class="flex items-center gap-0.5 rounded-lg group/reaction-item"
    :class="selected ? selectedClasses : ''"
    @click="!disabled ? emit('reacted', name) : null"
  >
    <VTooltip :content="tooltip" :size="['xs', 'sm'].includes(size) ? 'sm' : 'base'">
      <div
        :class="[
          selected ? '' : '',
          'flex items-center justify-center scale-[0.8] p-[0.0625rem]',
          animate && !disabled ? 'transform-gpu transition-all duration-300 ease-in-out group-hover/reaction-item:z-10 group-hover/reaction-item:scale-[1.4] group-hover/reaction-item:-translate-x-0.5 group-hover/reaction-item:-rotate-[8deg] group-active/reaction-item:scale-[1.5]' : '',
          'group-focus/reaction-item:outline-none group-focus/reaction-item-visible:ring-2 group-focus/reaction-item-visible:ring-primary-500',
          'rounded-full',
          sizeClasses[size],
        ]"
      >
        <component :is="emoji" class="icon" :class="iconClasses" />
      </div>
    </VTooltip>
    <div class="text-xs mr-1 mt-0.5 font-semibold text-primary-500" v-if="showCount">{{ count > 999 ? '999+' : count }}</div>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const emit = defineEmits<{
  (e: 'reacted', name: string): void
}>()

interface Props {
  name: string,
  emoji: object,
  size?: 'xs' | 'sm' | 'base' | 'lg',
  tooltip?: string,
  showCount?: boolean,
  count?: number,
  selected?: boolean,
  iconClasses?: string | string[],
  animate?: boolean,
  selectedClasses?: string | string[],
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'base',
  showCount: true,
  count: 0,
  selected: false,
  iconClasses: '',
  animate: true,
  selectedClasses: 'bg-primary-100 dark:bg-primary-800',
})

const sizeClasses = {
  xs: 'text-lg h-4 w-4',
  sm: 'text-xl h-5 w-[1.125rem]',
  base: 'text-2xl h-6 w-6',
  lg: 'text-3xl h-7 w-7'
}
</script>
