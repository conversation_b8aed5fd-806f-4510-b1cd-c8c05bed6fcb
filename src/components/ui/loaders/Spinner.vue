<template>
  <div>
    <div
      v-if="!hideSpinner"
      role="status"
      class="block m-auto"
      :class="classes.loader({ size })"
    >
      <svg
        :class="classes.loader({ size })"
        aria-hidden="true"
        class="mr-2 animate-spin fill-primary-400 text-platinum-300 dark:fill-primary-600 dark:text-midnight-700"
        viewBox="0 0 100 101"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
          fill="currentColor"
        />
        <path
          d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
          fill="currentFill"
        />
      </svg>
      <span class="sr-only">{{ label }}</span>
    </div>

    <TransitionFade
      enter-active-class="transition-opacity duration-700 ease-in"
      leave-active-class="transition-opacity duration-700 ease-out"
    >
      <div
        v-if="showContactUsMessage"
        class="text-nowrap px-1 pt-2 text-center text-sm text-platinum-900 dark:text-midnight-100 select-none"
      >
        <span class="font-bold">Ugh, still loading?!</span><br />
        <span class="pl-3 text-xs">That's not good - <TextLink :to="{ name: 'support.contact', query: { topic: 'technical-support-and-bugs' } }">Let us know!</TextLink></span>
      </div>
    </TransitionFade>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { cva, VariantProps } from 'class-variance-authority';

export type LoaderVariantProps = VariantProps<typeof classes.loader>;

const classes = {
  loader: cva('', {
    variants: {
      size: {
        xs: 'h-4 w-4',
        sm: 'h-5 w-5',
        base: 'h-6 w-6',
        lg: 'h-8 w-8',
        xl: 'h-10 w-10',
        '2xl': 'h-14 w-14',
        '3xl': 'h-24 w-24',
        '4xl': 'h-48 w-48',
      },
    },
  }),
};

const props = withDefaults(defineProps<{
  size?: LoaderVariantProps['size'];
  label?: string;
  contactUsTimeout?: number;
  contactUsMessage?: boolean;
  hideSpinner?: boolean;
}>(), {
  size: 'base',
  label: 'Loading...',
  contactUsTimeout: 15000,
  contactUsMessage: true,
  hideSpinner: false,
});

const showContactUsMessage = ref(false);
let timeoutId: number;

onMounted(() => {
  if (props.contactUsMessage) {
    timeoutId = window.setTimeout(() => {
      showContactUsMessage.value = true;
    }, props.contactUsTimeout);
  }
});

onUnmounted(() => {
  if (timeoutId) {
    clearTimeout(timeoutId);
  }
});
</script>
