<template>
  <TransitionRoot as="template" :show="open">
    <Dialog as="div">
      <div class="absolute inset-0 overflow-hidden">
        <DialogOverlay class="absolute inset-0 cursor-pointer" @click="$emit('close')" />
        <div class="fixed inset-y-0 flex max-w-full" :class="[position === 'left' ? 'left-0' : 'right-0']">
          <TransitionChild
            as="template"
            enter="transition-all duration-300"
            :enter-from="enterFromTransition"
            enter-to="translate-x-0"
            leave="transform transition-all ease-in-out duration-300"
            leave-from="translate-x-0"
            :leave-to="leaveToTransition"
          >
            <div :class="drawerSize">
              <div class="flex h-full flex-col bg-platinum-100 dark:bg-midnight-600">
                <div v-if="$slots.title" class="flex select-none items-center px-4 relative min-h-16">
                  <DialogTitle class="text-lg">
                    <slot name="title" />
                  </DialogTitle>
                </div>
                <div class="relative h-full overflow-y-auto scrollbar-thin">
                  <slot name="content" />
                </div>
              </div>
            </div>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup>
import { Dialog, DialogOverlay, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import { useEventBus } from '@vueuse/core';
import { nanoid } from 'nanoid';
import { ref, watch, computed } from 'vue';
import { twMerge } from 'tailwind-merge';

const props = defineProps({
  open: {
    type: Boolean,
    required: true,
  },
  name: {
    type: String,
    default: 'basic menu',
  },
  position: {
    type: String,
    default: 'left',
  },
  scrollable: {
    type: Boolean,
    default: true,
  },
  size: {
    type: String,
    default: 'md',
  },
});

const emit = defineEmits(['close']);

const id = nanoid();
const resetTranslation = ref(true);
const drawerBus = useEventBus('drawer');

watch(
  () => props.open,
  function (value) {
    if (props.scrollable) {
      document.querySelector('html').style.overflow = 'auto';
    }

    drawerBus.emit(value ? 'opened' : 'closed', {
      id: id,
      position: props.position,
      size: props.size,
      resetTranslation: resetTranslation.value,
    });

    resetTranslation.value = true;
  }
);

drawerBus.on(function (event, payload) {
  if (event === 'close' && payload?.exclude !== id) {
    resetTranslation.value = payload?.resetTranslation ?? true;
    emit('close');
  }
});

const drawerSize = computed(() => {
  return {
    xl: 'w-[400px] sm:w-[450px]',
    lg: 'w-[350px] sm:w-[400px]',
    md: 'w-[300px] sm:w-[350px]',
    sm: 'w-[250px] sm:w-[300px]',
  }[props.size];
});

const enterFromTransition = computed(() => {
  return {
    xl:
      props.position == 'left'
        ? 'translate-x-[-400px] sm:translate-x-[-450px]'
        : 'translate-x-[400px] sm:translate-x-[450px]',
    lg:
      props.position == 'left'
        ? 'translate-x-[-350px] sm:translate-x-[-400px]'
        : 'translate-x-[350px] sm:translate-x-[400px]',
    md:
      props.position == 'left'
        ? 'translate-x-[-300px] sm:translate-x-[-350px]'
        : 'translate-x-[300px] sm:translate-x-[350px]',
    sm:
      props.position == 'left'
        ? 'translate-x-[-250px] sm:translate-x-[-300px]'
        : 'translate-x-[250px] sm:translate-x-[300px]',
  }[props.size];
});

const leaveToTransition = enterFromTransition;
</script>
