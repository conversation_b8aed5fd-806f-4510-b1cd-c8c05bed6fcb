<template>
  <li>
    <Disclosure :default-open="open" v-slot="{ open }">
      <DisclosureButton
        class="group flex w-full items-center justify-between border-transparent py-3 pl-6 pr-3 text-primary-500 group-hover:text-white hover:bg-primary-500 dark:text-midnight-50 dark:hover:bg-midnight-800"
        @click="checkDoubleClick"
      >
        <div class="group flex items-center">
          <component
            v-if="link?.icon"
            :is="link.icon"
            :class="['mr-3 h-5 w-5 flex-shrink-0 text-primary-500 group-hover:text-white']"
          />
          <span v-if="link?.label" class="font-semibold group-hover:text-white">{{ link.label }}</span>
          <slot v-else name="label" :open="open" />
        </div>
        <span class="ml-2 flex h-7 items-center">
          <div
            class="dropdown-icon transition-color flex h-5 w-5 items-center rounded-full bg-primary-500 p-0.5 text-white duration-200 group-hover:bg-platinum-200 group-hover:text-primary-500 dark:text-midnight-800 dark:group-hover:bg-midnight-300"
            :class="open ? '-rotate-180' : ''"
          >
            <BiChevronDown class="h-2.5" />
          </div>
        </span>
      </DisclosureButton>

      <transition
        enter-active-class="transition duration-100 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition duration-75 ease-out"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <DisclosurePanel
          class="flex w-full list-none flex-col bg-platinum-300 shadow-inner shadow-platinum-800/50 dark:bg-midnight-700 dark:shadow-midnight-800"
          as="ul"
        >
          <slot name="content" :open="open" />
        </DisclosurePanel>
      </transition>
    </Disclosure>
  </li>
</template>

<script lang="ts" setup>
import { type Component, ref } from 'vue';
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue';
import BiChevronDown from '~icons/bi/chevron-down';
import { useRouter } from 'vue-router';

interface Props {
  link?: Object;
  open?: boolean;
}

const props = defineProps<Props>();

const router = useRouter();

// When a DrawerDropdown is clicked, check if it is a double click. Use named route or url if exists.
let clickTimeout = null;
const checkDoubleClick = () => {
  if (clickTimeout && props?.link) {
    clearTimeout(clickTimeout);
    clickTimeout = null;
    // Double click detected, take action.
    if (props?.link?.name) {
      router.push({ name: props?.link.name });
    } else if (props?.link?.url) {
      window.open(props.link.url, '_blank');
    }
  } else {
    // Reset the timeout
    clickTimeout = setTimeout(() => {
      clickTimeout = null;
    }, 250);
  }
};

</script>
