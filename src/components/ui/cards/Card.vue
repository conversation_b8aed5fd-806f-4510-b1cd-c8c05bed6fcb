<template>
  <div class="overflow-hidden rounded-lg bg-platinum-50 shadow dark:bg-midnight-500">
    <slot />
    <!-- <div v-if="$slots.header || title || description" class="rounded-t-md border-b bg-primary-500 px-6 py-5">
            <slot v-if="$slots.header" name="header" />
            <template v-else>
                <h2 v-if="title" class="text-base font-semibold text-platinum-50">
                    {{ title }}
                </h2>
                <p v-if="description" class="mt-1 max-w-2xl text-sm text-gray-500">
                    {{ description }}
                </p>
            </template>
        </div>
        <div
            class="bg-white p-6"
            :class="{
                'rounded-b-md': !($slots.footer || footer),
                'rounded-t-md': !($slots.header || title || description),
            }"
        >
            <slot />
        </div>
        <div v-if="$slots.footer || footer" class="rounded-b-md border-t bg-gray-50 px-6 py-5">
            <slot v-if="$slots.footer" name="footer" />
            <template v-else>
                {{ footer }}
            </template>
        </div> -->
  </div>
</template>

<script lang="ts" setup>
defineProps<{
  title?: string;
  description?: string;
  footer?: string;
}>();
</script>
