<template>
  <div
    :attrs="attrs"
    :class="twMerge('bg-primary-500 px-6 py-3 xl:px-6 xl:py-5 font-semibold text-white dark:text-midnight-500', attrs.class)"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
import { useAttrs } from 'vue';
import { twMerge } from 'tailwind-merge';
const attrs = useAttrs();
</script>

<script lang="ts">
export default {
  inheritAttrs: false,
};
</script>
