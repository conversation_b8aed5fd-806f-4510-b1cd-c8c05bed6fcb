<template>
  <transition mode="out-in" enter-active-class="transition" leave-active-class="transition">
    <slot></slot>
  </transition>
</template>

<style lang="scss" scoped>
.transition {
  animation-duration: 0.5s; /* Default */
  animation-name: widthFromLeft;
  animation-iteration-count: 1;
  animation-direction: alternate;
  overflow-x: hidden;
}

.transition__fadeInRight {
  animation-name: fadeInRight;
}
.transition__fadeOutLeft {
  animation-name: fadeOutLeft;
}

@keyframes widthFromLeft {
  0% {
    width: 0px;
    transform: translate3d(10%, 0, 0);
    opacity: 0;
  }

  40% {
    opacity: 0;
  }

  60% {
    width: 44px;
    opacity: 100%;
    transform: translate3d(-10%, 0, 0);
  }

  100% {
    width: auto;
    transform: translate3d(0%, 0, 0);
  }
}
</style>
