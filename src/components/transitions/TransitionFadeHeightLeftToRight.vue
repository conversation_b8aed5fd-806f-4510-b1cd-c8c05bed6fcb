<template>
  <transition
    mode="out-in"
    enter-active-class="transition transition__fadeInRight transition__faster"
    leave-active-class="transition transition__fadeOutLeft transition__faster"
  >
    <slot></slot>
  </transition>
</template>

<style lang="scss" scoped>
.transition {
  animation-duration: 1s; /* Default */
  animation-name: fadeInRight;
  animation-iteration-count: 3;
  animation-direction: alternate;
  height: 0;
  overflow: hidden;

  &.transition__fastest {
    animation-duration: 250ms;
  }
  &.transition__faster {
    animation-duration: 400ms;
  }
  &.transition__fast {
    animation-duration: 800ms;
  }
  &.transition__slow {
    animation-duration: 1s;
  }
  &.transition__slower {
    animation-duration: 1.5s;
  }
  &.transition__slowest {
    animation-duration: 2s;
  }
}

.transition__fadeInRight {
  animation-name: fadeInRight;
}
.transition__fadeOutLeft {
  animation-name: fadeOutLeft;
}

@keyframes fadeInRight {
  0% {
    height: 0;
    transform: translate3d(8%, 0, 0);
    opacity: 0;
  }

  60% {
    height: 100%;
    opacity: 100%;
    transform: translate3d(-1%, 0, 0);
  }

  100% {
    height: 100%;
    transform: translate3d(0%, 0, 0);
  }
}

@keyframes fadeOutLeft {
  0% {
    height: 100%;
    transform: translate3d(0, 0, 0);
    opacity: 100%;
  }

  60% {
    height: 0;
    opacity: 0%;
  }

  100% {
    height: 0;
    transform: translate3d(-10%, 0, 0);
    opacity: 0%;
  }
}
</style>
