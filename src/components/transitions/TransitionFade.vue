<template>
  <transition
    mode="out-in"
    name="fade"
    :style="{ transitionDuration: duration + 'ms' }"
  >
    <slot></slot>
  </transition>
</template>

<script setup>
const props = defineProps({
  duration: {
    type: Number,
    default: 200
  }
});
</script>

<style>
.fade-enter-active,
.fade-leave-active {
  transition-timing-function: ease;
  transition-property: all;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
