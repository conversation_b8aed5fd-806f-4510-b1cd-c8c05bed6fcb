import { ref, computed, watchEffect, unref, toValue, toRefs } from 'vue';

export function usePagination(options) {
  const activePage = ref(options.currentPage);
  const pagination = ref(null);

  watchEffect(() => {
    pagination.value = null;
    const { totalItems, pageSize, maxPages } = toValue(options);
    pagination.value = paginate(totalItems, activePage.value, pageSize, maxPages);
  });

  return {
    totalPages: computed(() => pagination.value.totalPages),
    pages: computed(() => pagination.value.pages),
    selectedPage: computed(() => pagination.value.currentPage),
    endPage: computed(() => pagination.value.endPage),
    startPage: computed(() => pagination.value.startPage),
    maxVisiblePages: options.value.maxPages,
    next: () => (activePage.value += 1),
    prev: () => (activePage.value -= 1),
    setPage: (newPage) => (activePage.value = newPage),
  };
}

const paginate = (totalItems, currentPage = 1, pageSize = 10, maxPages = 10) => {
  let totalPages = Math.ceil(totalItems / pageSize);

  if (currentPage < 1) {
    currentPage = 1;
  } else if (currentPage > totalPages) {
    currentPage = totalPages;
  }

  let startPage, endPage;

  if (totalPages <= maxPages) {
    startPage = 1;
    endPage = totalPages;
  } else {
    let maxPagesBeforeCurrentPage = Math.floor(maxPages / 2);
    let maxPagesAfterCurrentPage = Math.ceil(maxPages / 2) - 1;
    if (currentPage <= maxPagesBeforeCurrentPage) {
      startPage = 1;
      endPage = maxPages;
    } else if (currentPage + maxPagesAfterCurrentPage >= totalPages) {
      startPage = totalPages - maxPages + 1;
      endPage = totalPages;
    } else {
      startPage = currentPage - maxPagesBeforeCurrentPage;
      endPage = currentPage + maxPagesAfterCurrentPage;
    }
  }

  let startIndex = (currentPage - 1) * pageSize;
  let endIndex = Math.min(startIndex + pageSize - 1, totalItems - 1);
  let pages = Array.from(Array(endPage + 1 - startPage).keys()).map((i) => startPage + i);

  return {
    totalItems: totalItems,
    currentPage: currentPage,
    pageSize: pageSize,
    totalPages: totalPages,
    startPage: startPage,
    endPage: endPage,
    startIndex: startIndex,
    endIndex: endIndex,
    pages: pages,
  };
};
