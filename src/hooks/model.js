import { isDef, isFunction } from '@vueuse/shared';
import _ from 'lodash';
import { computed, ref, watch } from 'vue';

export function useVModel(props, key, emit, options = {}) {
  const { clone = false, passive = false, eventName, deep = false, defaultValue, shouldEmit } = options;

  const _emit = emit;
  let event = eventName;

  if (!key) {
    key = 'modelValue';
  }

  event = eventName || event || `update:${key.toString()}`;

  const cloneFn = (val) => (!clone ? val : isFunction(clone) ? clone(val) : _.cloneDeep(val));

  const getValue = () => (isDef(props[key]) ? cloneFn(props[key]) : defaultValue);

  const triggerEmit = (value) => {
    if (shouldEmit) {
      if (shouldEmit(value)) _emit(event, value);
    } else {
      _emit(event, value);
    }
  };

  if (passive) {
    const initialValue = getValue();
    const proxy = ref(initialValue);

    watch(
      () => props[key],
      (v) => (proxy.value = cloneFn(v)),
      {
        deep: true,
      }
    );

    watch(
      proxy,
      (v) => {
        if (v !== props[key] || !_.isEqual(v, props[key])) triggerEmit(v);
      },
      { deep }
    );

    return proxy;
  } else {
    return computed({
      get() {
        return getValue();
      },
      set(value) {
        triggerEmit(value);
      },
    });
  }
}
