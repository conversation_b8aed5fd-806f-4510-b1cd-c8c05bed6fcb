import _ from 'lodash';
import { nanoid } from 'nanoid';
import { reactive, ref, watch } from 'vue';

import { axios } from '@/lib/axios';
import Form from '@/lib/form';

export function useForm(data, options) {
  return ref(
    new Form(data, {
      http: axios,
      ...options,
    })
  );
}

export function useResourceAccordion(model, onUpdate) {
  const accordions = ref([]);

  watch(
    accordions,
    (value, oldValue) => {
      if (onUpdate) {
        onUpdate(value, oldValue);
      }
    },
    {
      deep: true,
    }
  );

  const create = (attributes = {}, isOpen = false) => {
    const accordion = {
      id: nanoid(),
      isOpen: false,
      resource: { ...model, ...attributes },
    };

    accordions.value.push(accordion);

    if (isOpen) {
      open(accordion.id);
    }
  };

  const remove = (id) => {
    accordions.value = accordions.value.filter((accordion) => accordion.id !== id);
  };

  const updateResource = (id, attributes) => {
    accordions.value = accordions.value.map((accordion) => {
      if (accordion.id !== id) return accordion;

      return {
        ...accordion,
        resource: {
          ...accordion.resource,
          ...attributes,
        },
      };
    });
  };

  const updateResources = (attributes) => {
    accordions.value.forEach((accordion) => updateResource(accordion.id, attributes));
  };

  const open = (id) => {
    accordions.value = accordions.value.map((accordion) => {
      return {
        ...accordion,
        isOpen: id.includes(accordion.id),
      };
    });
  };

  const clear = (keep = 1, attributes) => {
    accordions.value = [];

    _.times(keep, () => create(attributes));
  };

  return {
    accordions,
    create,
    remove,
    clear,
    open,
    updateResource,
    updateResources,
  };
}
