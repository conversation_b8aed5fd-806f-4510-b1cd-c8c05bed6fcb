import { ref } from 'vue';

export function useFilterParams(overrides) {
  const params = ref({
    search: '',
    sort: '',
    page: '',
    per_page: 15,
    ...overrides,
  });

  const search = (value) => {
    params.value.search = value;
  };

  const sort = (value) => {
    params.value.sort = value;
  };

  const paginate = (page, limit = null) => {
    params.value.page = page;
    if (!limit) return;
    params.value.per_page = limit;
  };

  return { params, search, sort, paginate };
}
