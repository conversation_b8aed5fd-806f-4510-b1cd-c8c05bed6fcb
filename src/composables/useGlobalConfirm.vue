<template>
  <VModal
    v-model:open="isOpen"
    :title="title"
    showClose
    :showCancel="false"
    :showConfirm="true"
    @confirmed="handleConfirm"
    @cancelled="handleClose"
  >
    <template #description>
      <div class="space-y-2" v-html="description" />
    </template>
  </VModal>
</template>


<script setup>
import { onMounted, ref } from 'vue';

const isOpen = ref(false);

const title = ref(null);
const description = ref(null);

let resolvePromise = null;

const showConfirm = (titleValue = null, descriptionHtmlValue = null) => {
  if (titleValue) {
    title.value = titleValue;
  }
  if (descriptionHtmlValue) {
    description.value = descriptionHtmlValue;
  }
  isOpen.value = true;
  return new Promise((resolve) => {
    resolvePromise = resolve;
  });
};

const handleConfirm = () => {
  resetDefaults();
  resolvePromise?.(true);
  isOpen.value = false;
};

const handleClose = () => {
  isOpen.value = false;
  resetDefaults();
  resolvePromise?.(false);
};

// Reset in the event the modal needs to be used again with different values.
const resetDefaults = () => {
  // Slightly delayed for the modal transition.
  setTimeout(() => {
    title.value = "Please Confirm";
    description.value = "<p>Are you sure you wish to proceed?</p>";
  }, 300);
};

defineExpose({
  showConfirm,
  isOpen,
  handleConfirm,
  handleClose,
});

onMounted(() => {
  resetDefaults();
});

</script>