import { onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { ucfirst } from '@/util/textUtils';

/**
 * Composable for handling OAuth-related toast messages from route query parameters.
 */
export function useOAuthToasts(options = {}) {
  const route = useRoute();

  // Default error message mapping - can be overridden via options.
  const defaultErrorMessages = {
    email: (provider) => ({
      title: 'Email Mismatch',
      message: `The email provided by ${ucfirst(provider)} does not match your account email address. Consider changing your profile email address and try again.`
    }),
    link: (provider) => ({
      title: 'Connection Error',
      message: `It seems you already have an existing account in relation to this ${ucfirst(provider)} account. Unlink the existing account, sign in with a different ${ucfirst(provider)} account or use a different authentication method.`,
      timeout: 15000,
    }),
    server: () => ({
      title: 'Server Error',
      message: 'Failed to connect to the server. Please contact us if this issue persists.'
    }),
    timeout: (provider) => ({
      title: 'Timeout',
      message: `A timeout occurred when attempting to link to ${ucfirst(provider)}. Please try again.`
    }),
    user: (provider) => ({
      title: 'User Not Found',
      message: `We were unable to find a user associated with this ${ucfirst(provider)} account. Please register instead.`
    })
  };

  // Default success message mapping - can be overridden via options.
  const defaultSuccessMessages = {
    login: (provider) => ({
      title: 'Welcome!',
      message: `You have successfully logged in${(provider ? ' via ' + ucfirst(provider) : '')}.`
    }),
    register: (provider) => ({
      title: 'Registration Successful',
      message: `You have successfully registered${(provider ? ' via ' + ucfirst(provider) : '')}.`
    }),
    connect: (provider) => ({
      title: 'Connection Successful',
      message: `You have successfully linked your ${ucfirst(provider)} account. You can now use this to login.`
    }),
  };

  // Merge with provided options.
  const errorMessages = { ...defaultErrorMessages, ...(options.errorMessages || {}) };
  const successMessages = { ...defaultSuccessMessages, ...(options.successMessages || {}) };

  /**
   * Handle route query parameters and display appropriate toasts.
   */
  const handleRouteQuery = () => {
    const error = route.query.error;
    const success = route.query.success;
    const provider = route.query.provider || 'the provider';
    let shouldRedirect = false;

    if (success) {
      // Determine success message type based on options or default to 'login'.
      const successType = options.successType || 'login';
      const messageConfig = successMessages[successType];
      
      if (messageConfig) {
        const message = messageConfig(provider);
        const toastParams = {
          title: message.title,
          message: message.message
        };

        const timeoutMs = message.timeout || 10000;

        Reshape.toast(toastParams, 'success', timeoutMs);
        shouldRedirect = true;
      }
    }
    else if (error && errorMessages[error]) {
      const message = errorMessages[error](provider);

      const toastParams = {
        title: message.title,
        message: message.message
      };

      const timeoutMs = message.timeout || 10000;

      Reshape.toast(toastParams, 'danger', timeoutMs);
      shouldRedirect = true;
    }

    if (shouldRedirect) {
      // Clean up URL parameters without causing a page refresh.
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
    }
  };

  // Auto-handle route query on mount unless disabled.
  if (options.autoHandle !== false) {
    onMounted(() => {
      handleRouteQuery();
    });
  }

  return {
    handleRouteQuery,
    errorMessages,
    successMessages
  };
}
