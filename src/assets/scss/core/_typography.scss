@import '../variables/colors';
@import '../variables/other';
@import '../variables/typography';

.page-title {
  color: $primaryPageTitleColor;
  font-size: $header_size_large;
}

.card {
  .page-title {
    color: $cardPageTitleColor;
  }
}

.font-size-largest {
  font-size: $text-size-largest;
}
.font-size-small {
  font-size: $text-size-small;
}
.font-size-tiny {
  font-size: $text-size-tiny;
}

/* Default colour classes */
.text-black {
  color: black;
}
.text-white {
  color: white;
}
.text-primary {
  color: $ascent2;
}

.text-link {
  color: $darkColor;
  text-decoration: none;

  &:hover,
  &:active,
  &:focus,
  &:visited {
    color: $darkColor;
    text-decoration: underline;
  }
}

.first-capitalize::first-letter {
  text-transform: capitalize;
}

label.required:after {
  content: ' *';
}
