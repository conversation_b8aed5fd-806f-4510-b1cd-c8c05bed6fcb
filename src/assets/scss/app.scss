@use 'variables/colors';

@import url(https://fonts.bunny.net/css?family=nunito:100,200,300,400,500,600,700,800,900|major-mono-display:400|inter:100,200,300,400,500,600,700,800,900);

@import 'floating-vue/dist/style.css';
@import 'tippy.js/dist/tippy.css';
@import '@vueform/multiselect/themes/default.css';

@tailwind base;
@tailwind components;
@tailwind utilities;


body {
  font-family: 'Nunito', sans-serif;
}

html {
  scroll-behavior: smooth;
}

@layer base {
  :root {
    --primary-50: 202 61.54% 94.9%;
    --primary-100: 201 56.1% 91.96%;
    --primary-200: 200 46.48% 86.08%;
    --primary-300: 200 41.98% 74.31%;
    --primary-400: 199 40.31% 62.55%;
    --primary-500: 199 38.89% 50.59%;
    --primary-600: 199 39.3% 39.41%;
    --primary-700: 199 41.67% 28.24%;
    --primary-800: 199 47.13% 17.06%;
    --primary-900: 200 72.41% 5.69%;
    --primary-950: 202 73.33% 2.94%;
    --radius: 0.5rem;

    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --navbar: 0 0% 100%;
    --navbar-foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --font-title: 'Inter', system-ui, sans-serif;
    --font-heading: 'Inter', system-ui, sans-serif;
    --font-content: 'Inter', system-ui, sans-serif;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

.default-icon-size {
  height: 19.5px;
  width: 19.5px;
}

mark {
  padding: 0 0.25rem;
  border-radius: 0.25rem;
  background-color: hsl(var(--primary-600) / 0.75);
  color: hsl(var(--platinum-50));
}

.dark mark {
  background-color: hsl(var(--primary-600));
  color: var(--midnight-950);
  font-weight: 600;
}