<template>
  <div class="h-full">
    <div id="top"></div>
    <div :class="classes.wrapper" class="h-full">
      <div :class="classes.container" class="transition duration-300 ease-in-out h-full">
        <Navbar class="relative" />

        <div class="relative h-[calc(100vh-4rem)]">
          <main class="relative h-full">
            <RouterView v-slot="{ Component }">
              <TransitionFadeLeftToRight>
                <component :is="Component" :key="route.fullPath" />
              </TransitionFadeLeftToRight>
            </RouterView>
          </main>

          <Transition
            enter-active-class="duration-300 ease-out"
            enter-from-class="transform opacity-0"
            enter-to-class="opacity-100"
            leave-active-class="duration-200 ease-in"
            leave-from-class="opacity-100"
            leave-to-class="transform opacity-0"
          >
            <div v-if="translationDirection" class="absolute inset-0 z-50 bg-black/50 backdrop-blur-sm" />
          </Transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useEventBus } from '@vueuse/core';
import { watchEffect, computed, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useAuth } from '@/store';
import layout from '@/store/layout';

import Navbar from '@/components/layout/navbar/Navbar.vue';

const route = useRoute();
const auth = useAuth();

watchEffect(() => {
  Object.entries(auth?.user?.theme?.colors || {}).forEach(([variable, value]) => {
    const split = value.split(' ');
    document.documentElement.style.setProperty(`--primary-${variable}`, value);
    const hsl = `hsl(${split[0]},${split[1]},${split[2]})`;
    if (hsl) {
      document.documentElement.style.setProperty(`--primary-color-${variable}`, hsl);
    }
  });
});

// Watch for theme changes and show an alert
watch(
  () => auth?.user?.theme,
  (newTheme, oldTheme) => {
    // Only process theme changes if theme exists and has actually changed
    if (newTheme && (!oldTheme || newTheme !== oldTheme)) {
      if (newTheme?.light_dark_mode === null) {
        // Theme allows user to switch - enable toggle
        layout.actions.setDarkModeToggle(true);
        
        // First, check if the user has already set a preference
        // This means accessing the browser's local storage (vueuse-color-scheme, being either 'light' or 'auto' for dark)
        const colorScheme = localStorage.getItem('vueuse-color-scheme');

        if (colorScheme === 'auto') {
          // If the user has not set a preference, we need to check the default preference of the theme.
          if (newTheme?.default_light_dark === 'dark') {
            layout.actions.setDarkMode(true);
          } else if (newTheme?.default_light_dark === 'light') {
            layout.actions.setDarkMode(false);
          }
          // If no theme preference, continue with default behaviour (auto will use system preference).
        }
      } else {
        // Theme forces a specific mode - disable toggle and set mode immediately
        layout.actions.setDarkModeToggle(false);
        const forcedDarkMode = newTheme?.light_dark_mode === 'dark';
        layout.actions.setDarkMode(forcedDarkMode);
      }
    }
  },
  { deep: true, immediate: true }
);

/**
 * Indicate if the layout is translated (left or right).
 */
const translationDirection = ref('');
const translationSize = ref('');
const overflow = ref('');

// We listen to 'opened' and 'closed' events broadcast over the 'drawer' event
// bus. When an opened event is broadcast, apply transform classes to the
// layout container to push it towards the left or right by 300 pixels.
const drawerBus = useEventBus('drawer');

drawerBus.on(function (event, drawer) {
  if (event === 'opened') {
    overflow.value = 'overflow-hidden';
    drawerBus.emit('close', {
      exclude: drawer.id,
      resetTranslation: translationDirection.value !== drawer.position,
    });
    translationDirection.value = drawer.position;
    translationSize.value = drawer.size;
  }

  if (event === 'closed' && drawer.resetTranslation) {
    translationDirection.value = '';
    translationSize.value = '';
    // Deals with the bottom scrollbar while resetting..
    setTimeout(() => {
      overflow.value = '';
    }, 300);
  }
});

const classes = computed(() => {
  const containerClasses = {
    xl:
      translationDirection.value == 'left'
        ? 'translate-x-[400px] sm:translate-x-[450px]'
        : 'translate-x-[-400px] sm:translate-x-[-450px]',
    lg:
      translationDirection.value == 'left'
        ? 'translate-x-[350px] sm:translate-x-[400px]'
        : 'translate-x-[-350px] sm:translate-x-[-400px]',
    md:
      translationDirection.value == 'left'
        ? 'translate-x-[300px] sm:translate-x-[350px]'
        : 'translate-x-[-300px] sm:translate-x-[-350px]',
    sm:
      translationDirection.value == 'left'
        ? 'translate-x-[250px] sm:translate-x-[300px]'
        : 'translate-x-[-250px] sm:translate-x-[-300px]',
  }[translationSize.value];

  return {
    container: containerClasses,
    wrapper: translationDirection.value || overflow.value ? 'overflow-hidden h-screen' : '',
  };
});
</script>
